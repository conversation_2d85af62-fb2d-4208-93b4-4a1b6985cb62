package integration

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/volatiletech/null/v8"
	"golang.org/x/mod/semver"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/client-go/rest"
	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/manifests"
	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/client/apis/clusterupgrader"
	"github.com/akuityio/agent/pkg/client/apis/kargo/clusteragent"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	customErrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/kustomize"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"
)

const (
	kargoAgentIDLabel = "akuity.io/kargo-agent-id"
)

type kargoAgentReconciler struct {
	stateClient          tenant.KargoStateClient
	tenantsFactory       KargoTenantsFactory
	repoSet              client.RepoSet
	inCluster            bool
	cfg                  config.InstanceConfig
	domainSuffix         string
	log                  *logr.Logger
	customCert           string
	shard                string
	portalURL            string
	featureSvc           features.Service
	kargoVersions        []agentclient.ComponentVersion
	kargoUnstableVersion *agentclient.ComponentVersion
}

func NewKargoAgentReconciler(
	tenantsFactory KargoTenantsFactory,
	settings ControllerSettings,
	stateClient tenant.KargoStateClient,
	customCert string,
	featureSvc features.Service,
	kargoVersions []agentclient.ComponentVersion,
	kargoUnstableVersion *agentclient.ComponentVersion,
) *kargoAgentReconciler {
	return &kargoAgentReconciler{
		tenantsFactory:       tenantsFactory,
		repoSet:              settings.RepoSet,
		inCluster:            !settings.EnableIngress,
		stateClient:          stateClient,
		cfg:                  settings.InstanceConfig,
		domainSuffix:         settings.DomainSuffix,
		log:                  settings.Log,
		customCert:           customCert,
		shard:                settings.Shard,
		portalURL:            settings.PortalURL,
		featureSvc:           featureSvc,
		kargoVersions:        kargoVersions,
		kargoUnstableVersion: kargoUnstableVersion,
	}
}

func (r *kargoAgentReconciler) ItemToID(item *models.KargoAgent) string {
	return item.ID
}

func (r *kargoAgentReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"cluster_id", id}
}

func (r *kargoAgentReconciler) IDColumn() string {
	return fmt.Sprintf("%s as %s", models.KargoAgentTableColumns.ID, models.KargoAgentColumns.ID)
}

func (r *kargoAgentReconciler) LogValuesFromItem(item *models.KargoAgent) []interface{} {
	return []interface{}{"instance_id", item.InstanceID, "cluster_name", item.Name}
}

func (r *kargoAgentReconciler) Reconcile(ctx context.Context, cluster *models.KargoAgent) error {
	status, err := cluster.GetStatus()
	if err != nil {
		return fmt.Errorf("failed to get cluster status: %w", err)
	}
	oldStatus := status.DeepCopy()
	reconcileError := r.reconcile(ctx, cluster, &status)
	if !reflect.DeepEqual(status, oldStatus) {
		if err := cluster.SetStatus(status); err != nil {
			return fmt.Errorf("failed to set cluster status: %w", err)
		}
		if err := r.repoSet.KargoAgents().Update(context.Background(), cluster, models.KargoAgentStatusColumns...); err != nil {
			return fmt.Errorf("failed to persist cluster status: %w", err)
		}
	}
	return reconcileError
}

type kubeconfigGetter struct {
	config         *rest.Config
	originalConfig *rest.Config
}

func (k *kubeconfigGetter) Get(original ...bool) *rest.Config {
	if len(original) > 0 && original[0] {
		if k.originalConfig == nil {
			return k.config
		}
		return k.originalConfig
	}
	return k.config
}

type agentRelatedData struct {
	agent              *models.KargoAgent
	instance           *models.KargoInstance
	instanceConfig     *models.KargoInstanceConfig
	organization       *models.Organization
	autoscaleEnabled   bool
	unsupportedVersion bool
	orgFeatureStatuses *featuresv1.FeatureStatuses
}

func (r *kargoAgentReconciler) loadAgentRelatedData(ctx context.Context, agent *models.KargoAgent) (*agentRelatedData, error) {
	instance, err := r.repoSet.KargoInstances().GetByID(ctx, agent.InstanceID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch instance: %w", err)
	}
	instanceConfig, err := r.repoSet.KargoInstanceConfigs().GetByID(ctx, agent.InstanceID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch instance config: %w", err)
	}
	organization, err := r.repoSet.Organizations().GetByID(ctx, instance.OrganizationOwner.String)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch organization: %w", err)
	}
	orgFeatureStatuses := r.featureSvc.GetFeatureStatusesWithOrg(ctx, organization)
	autoscaleEnabled := orgFeatureStatuses.GetClusterAutoscaler().Enabled()
	return &agentRelatedData{
		agent:              agent,
		instance:           instance,
		instanceConfig:     instanceConfig,
		organization:       organization,
		autoscaleEnabled:   autoscaleEnabled,
		unsupportedVersion: misc.IsComponentVersionSupported(instanceConfig.Version.String, r.kargoVersions) != nil,
		orgFeatureStatuses: orgFeatureStatuses,
	}, nil
}

func (r *kargoAgentReconciler) reconcile(ctx context.Context, cluster *models.KargoAgent, status *models.KargoAgentStatus) error {
	tnt, err := r.tenantsFactory.NewKargoTenant(cluster.InstanceID)
	if err != nil {
		return fmt.Errorf("failed to create new Kargo tenant: %w", err)
	}
	var remoteArgocdConfig kubeconfigGetter
	if !cluster.RemoteArgocdInstanceID.IsZero() && cluster.RemoteArgocdInstanceID.String != "" {
		argocdInstanceConfig, err := r.repoSet.ArgoCDInstanceConfigs().GetByID(ctx, cluster.RemoteArgocdInstanceID.String, "private_spec")
		if err != nil {
			return fmt.Errorf("failed to get remote argocd instance config: %w", err)
		}
		privSpec, err := argocdInstanceConfig.GetPrivateSpec()
		if err != nil {
			return fmt.Errorf("failed to get remote argocd instance private spec: %w", err)
		}
		remoteArgocdKconfig, err := privSpec.GetRestConfig()
		if err != nil {
			return fmt.Errorf("failed to get remote argocd instance rest config: %w", err)
		}
		remoteArgocdConfig = kubeconfigGetter{config: remoteArgocdKconfig}
		argocdInstance, err := r.repoSet.ArgoCDInstances().GetByID(ctx, cluster.RemoteArgocdInstanceID.String, "shard")
		if err != nil {
			return fmt.Errorf("failed to get remote argocd instance: %w", err)
		}
		if argocdInstance.Shard == r.shard {
			remoteArgocdConfig.originalConfig, err = privSpec.GetRestConfig()
			if err != nil {
				return fmt.Errorf("failed to get remote argocd instance original rest config: %w", err)
			}
			remoteArgocdConfig.config.Host = fmt.Sprintf("https://k3s-proxy.argocd-%s.svc.cluster.local:6445", cluster.RemoteArgocdInstanceID.String)
			remoteArgocdConfig.config.Insecure = true
		}
	}

	if !cluster.DeletionTimestamp.IsZero() {
		if err := r.deleteCluster(ctx, cluster, status, tnt, remoteArgocdConfig.Get()); err != nil {
			return fmt.Errorf("failed to delete cluster: %w", err)
		}
		return nil
	}

	agentData, err := r.loadAgentRelatedData(ctx, cluster)
	if err != nil {
		return fmt.Errorf("failed to load agent related data: %w", err)
	}

	// update agent version to latest if target is empty before doing anything else and force a reconciliation
	// forcing reconciliation immediately to prevent duplicate processing of same cluster twice
	spec, err := cluster.GetUnmodifiedSpec()
	if err != nil {
		return err
	}
	latestVersion := version.GetLatestAgentVersion()
	if spec.TargetVersion == "" && !agentData.unsupportedVersion {
		spec.TargetVersion = latestVersion
		if err := cluster.SetSpec(*spec); err != nil {
			return err
		}
		return r.repoSet.KargoAgents().Update(ctx, cluster, "spec")
	}

	if spec.AkuityManaged && !agentData.unsupportedVersion {
		// always update target agent version to latest for akuity managed clusters
		if spec.TargetVersion != version.GetLatestAgentVersion() {
			// update target version to latest
			spec.TargetVersion = version.GetLatestAgentVersion()
			if err := cluster.SetSpec(*spec); err != nil {
				return err
			}
			// updated in db and short circuit to force reconciliation
			return r.repoSet.KargoAgents().Update(ctx, cluster, "spec")
		}
	} else if !agentData.unsupportedVersion {
		// generate kargo job webhook cert if not akuity managed
		changed, err := r.updateAgentPrivateSpec(ctx, agentData)
		if err != nil {
			return err
		}
		if changed {
			// force next reconciliation
			return customErrors.NewRetryableError(fmt.Errorf("kargo agent private spec updated, retry reconciliation"), "")
		}
	}

	// perform config creation before setting observed generation,
	// to prevent prematurely setting it in case config creation fails
	clusterConfig, err := r.fillClusterApplyConfig(ctx, agentData)
	if err != nil {
		return err
	}

	// generation should not be updated in case of deletions or errors with tenant instance creation
	// also skip setting observed generation if agent target version is being updated (see above)
	defer func() {
		status.ObservedGeneration = null.IntFrom(cluster.Generation)
	}()

	if !status.Conditions.IsEstablishedOrFailedWithReason(models.ClusterConditionTypeConfigured, models.ClusterInvalidConfigReason) || status.Manifests.IsZero() || status.ObservedGeneration != null.IntFrom(cluster.Generation) {
		// DEV: ensure no form of manifest apply/generation happens when this
		// condition is encountered or else it will error during manifest gen
		// stop reconciliation if instance version is unsupported
		if !agentData.unsupportedVersion {
			if err := r.configureCluster(ctx, clusterConfig, agentData, tnt, remoteArgocdConfig); err != nil {
				var kustomizationErr *agentclient.KustomizationError
				if errors.As(err, &kustomizationErr) {
					status.Conditions.SetNotEstablished(models.ClusterConditionTypeConfigured, models.ClusterInvalidConfigReason, kustomizationErr.Message)
					return fmt.Errorf("cluster has invalid user provided configuration: %w", err)
				}
				status.Conditions.SetNotEstablished(models.ClusterConditionTypeConfigured, "FailedToApplyConfiguration", err.Error())
				return customErrors.NewRetryableError(err, "cluster configuration failed")
			} else {
				if status.AgentState != nil {
					status.AgentState.ObservedGenerationAppliedAt = null.TimeFrom(time.Now())
				}
				status.Conditions.SetNotEstablished(models.ClusterConditionTypePruned, "NewConfigurationApplied", "")
				status.Conditions.SetEstablished(models.ClusterConditionTypeConfigured)
			}
		}
	}

	clusterData, err := r.stateClient.GetClusterData(ctx, cluster)
	if err != nil {
		return err
	}
	if clusterData != nil {
		status.Manifests = null.StringFrom(clusterData.Manifest)
	}

	var state *models.KargoAgentState
	if spec.AkuityManaged {
		// if akuity managed cluster i.e. controller is running on tenant ns, check for health status directly
		if status.Conditions.IsEstablished(models.ClusterConditionTypeConfigured) {
			// only update once cluster is configured
			health, err := tnt.AkuityManagedAgentStatus(ctx, cluster.Name)
			if err != nil {
				return err
			}
			state = &models.KargoAgentState{
				Version:                   "v" + spec.TargetVersion,
				KargoVersion:              clusterConfig.kargoVersion,
				ObservedAt:                null.TimeFrom(time.Now()),
				Status:                    health,
				AgentIDs:                  []string{},
				LastUserAppliedGeneration: int64(cluster.Generation),
			}
			if status.AgentState == nil || time.Since(status.AgentState.ObservedAt.Time) > 2*time.Minute {
				// if agent state is already set and observed at is less than 5 minutes
				// then skip updating the state we reconcile for configmap changes.
				// we get duplicate event when we update configmap below
				// so skip updates if the observed generation is already updated

				// update agent status cm with info for cplane components like cplane-controller
				if err := tnt.UpdateAgentStatusCM(ctx, cluster.Name, &agentclient.AgentStatusData{
					Version:      state.Version,
					KargoVersion: state.KargoVersion,
					ObservedAt:   state.ObservedAt.Time,
				}); err != nil {
					return fmt.Errorf("failed to update akuity managed agent status cm: %w", err)
				}
			}
		}
	} else {
		state, err = r.stateClient.GetAgentState(ctx, cluster)
		if err != nil {
			return err
		}
	}

	var prevObservedGenerationAppliedAt null.Time
	if status.AgentState != nil {
		prevObservedGenerationAppliedAt = status.AgentState.ObservedGenerationAppliedAt
	}
	status.AgentState = state
	if status.AgentState != nil {
		status.AgentState.ObservedGenerationAppliedAt = prevObservedGenerationAppliedAt
	}

	if state != nil && state.Status != nil && int(state.Status.MinObservedGeneration) == cluster.Generation && status.Conditions.IsEstablished(models.ClusterConditionTypeConfigured) && status.Conditions.IsNotEstablishedAndFailedWithReason(models.ClusterConditionTypePruned, "NewConfigurationApplied") {
		status.AgentState.ObservedGenerationAppliedAt = null.TimeFromPtr(nil)
		// if cluster is configured properly and the agent generation matches the current generation then delete credentials and run prune
		currentRotation, err := r.pruneCluster(ctx, clusterConfig, tnt, remoteArgocdConfig.Get())
		if err != nil {
			return err
		}
		status.Conditions.SetEstablished(models.ClusterConditionTypePruned)
		status.ObservedRotationCount = null.IntFrom(int(currentRotation))
	}

	return nil
}

// pruneCluster prunes old credentials and k8s resources
func (r *kargoAgentReconciler) pruneCluster(ctx context.Context, clusterConfig kargoAgentApplyConfig, tnt KargoTenant, remoteArgocdConfig *rest.Config) (uint64, error) {
	if clusterConfig.agentRotationCount > 0 {
		currentSuffix := GetAgentSuffix(clusterConfig.agentRotationCount)
		_, errs := tnt.DeleteAgentCredentials(ctx, clusterConfig.agentName, currentSuffix, remoteArgocdConfig)
		for _, er := range errs {
			if er != nil && !k8sErrors.IsNotFound(er) {
				return clusterConfig.agentRotationCount, er
			}
		}

	}
	return clusterConfig.agentRotationCount, tnt.PruneAgent(ctx, clusterConfig.agentName, uint64(clusterConfig.generation), false)
}

func (r *kargoAgentReconciler) configureCluster(ctx context.Context, kargoAgentConfig kargoAgentApplyConfig, agentData *agentRelatedData, tnt KargoTenant, remoteArgocdConfig kubeconfigGetter) error {
	if err := r.applyCluster(ctx, kargoAgentConfig, agentData, tnt, remoteArgocdConfig); err != nil {
		return fmt.Errorf("failed to apply cluster manifests: %w", err)
	}
	return nil
}

// deleteCluster deletes the cluster connected to the tenant including all its k8s resources and entries in db
func (r *kargoAgentReconciler) deleteCluster(ctx context.Context, cluster *models.KargoAgent, status *models.KargoAgentStatus, tnt KargoTenant, remoteArgocdConfig *rest.Config) error {
	spec, err := cluster.GetSpec()
	if err != nil {
		return err
	}

	errs := tnt.DeleteAgent(ctx, cluster.Name, remoteArgocdConfig, spec.AkuityManaged)

	for _, er := range errs {
		if !k8sErrors.IsNotFound(er) {
			return er
		}
	}

	if err := tnt.PruneAgent(ctx, cluster.Name, uint64(cluster.Generation), false); err != nil {
		return err
	}

	return r.repoSet.KargoAgents().Delete(ctx, cluster.ID)
}

type kargoAgentApplyConfig struct {
	orgID               string
	instanceID          string
	agentName           string
	agentID             string
	namespace           string
	generation          uint64
	kargoVersion        string
	kustomization       kustomize.Kustomization
	autoUpgradeDisabled bool
	resources           models.KargoAgentResources
	labels              map[string]string
	annotations         map[string]string
	agentVersion        string
	agentRotationCount  uint64
	defaultShard        bool
	akuityManaged       bool
	argocdNs            string

	webhookCert string
	webhookKey  string
	logURL      string
	password    string
}

func (r *kargoAgentReconciler) fillClusterApplyConfig(ctx context.Context, agentData *agentRelatedData) (kargoAgentApplyConfig, error) {
	if agentData.instanceConfig.Version.IsZero() || agentData.instanceConfig.Version.String == "" {
		return kargoAgentApplyConfig{}, fmt.Errorf("failed to fetch tenant argocd version, tenant=%v, cluster=%v", agentData.agent.InstanceID, agentData.agent.Name)
	}

	spec, err := agentData.agent.GetSpec()
	if err != nil {
		return kargoAgentApplyConfig{}, fmt.Errorf("failed to get agent spec: %w", err)
	}

	versionResources, err := models.GetKargoAgentResources(spec.SizeVersion)
	if err != nil {
		return kargoAgentApplyConfig{}, err
	}
	resources, ok := versionResources[spec.Size]
	if !ok {
		return kargoAgentApplyConfig{}, fmt.Errorf("unknown agent size: %s", spec.Size)
	}
	agentVersion := spec.TargetVersion
	if agentVersion == "" {
		agentVersion = version.GetLatestAgentVersion()
	}

	kustomization := spec.Kustomization
	if kustomization == nil {
		kustomization = kustomize.Kustomization{}
	}

	privateSpec, err := agentData.agent.GetPrivateSpec()
	if err != nil {
		return kargoAgentApplyConfig{}, fmt.Errorf("failed to get agent private spec: %w", err)
	}

	agentConf := kargoAgentApplyConfig{
		instanceID:          agentData.agent.InstanceID,
		agentName:           agentData.agent.Name,
		namespace:           agentData.agent.Namespace,
		generation:          uint64(agentData.agent.Generation),
		kargoVersion:        agentData.instanceConfig.Version.String,
		kustomization:       kustomization,
		autoUpgradeDisabled: agentData.agent.AutoUpgradeDisabled,
		resources:           resources,
		labels:              spec.Labels,
		annotations:         spec.Annotations,
		agentVersion:        agentVersion,
		agentRotationCount:  uint64(spec.AgentRotationCount),
		akuityManaged:       spec.AkuityManaged,
		agentID:             agentData.agent.ID,
		argocdNs:            spec.ArgocdNamespace,
		webhookCert:         privateSpec.WebhookCert,
		webhookKey:          privateSpec.WebhookKey,
	}

	if agentData.orgFeatureStatuses.GetKargoAnalysisLogs().Enabled() {
		// logging feature is only available to nighty and 1.4 +
		if strings.Contains(agentData.instanceConfig.Version.String, "unstable") || semver.Compare(agentData.instanceConfig.Version.String, "v1.4.0") >= 0 {
			agentConf.logURL = fmt.Sprintf("%s/agent-api/v1/stream/kargo/agent/logs", r.portalURL)
			agentConf.password = privateSpec.AgentPassword
		}
	}

	instanceSpec, err := agentData.instanceConfig.GetSpec()
	if err != nil {
		return kargoAgentApplyConfig{}, fmt.Errorf("failed to get instance spec: %w", err)
	}

	if instanceSpec.DefaultShardAgentID == agentData.agent.ID {
		agentConf.defaultShard = true
	}
	agentConf.orgID = agentData.organization.ID

	return agentConf, nil
}

func (r *kargoAgentReconciler) applyCluster(ctx context.Context, config kargoAgentApplyConfig, agentData *agentRelatedData, tnt KargoTenant, remoteArgocdConfig kubeconfigGetter) error {
	suffix := GetAgentSuffix(config.agentRotationCount)
	clusterConfig := agentclient.KargoAgentConfig{
		UsernameSuffix:      suffix,
		Namespace:           config.namespace,
		Name:                config.agentName,
		Generation:          &config.generation,
		Labels:              config.labels,
		Annotations:         config.annotations,
		AutoUpgradeDisabled: config.autoUpgradeDisabled,
		AkuityManaged:       config.akuityManaged,
	}

	// add custom cluster id label to resources for tracking when akuity managed
	if config.akuityManaged {
		if clusterConfig.Labels == nil {
			clusterConfig.Labels = make(map[string]string)
		}
		clusterConfig.Labels[kargoAgentIDLabel] = config.agentID
	}

	if err := tnt.ApplyAgent(ctx, clusterConfig, remoteArgocdConfig.Get()); err != nil {
		return err
	}

	// initialize cluster data cm
	values, err := tnt.GetAgentDataValues(ctx, config.agentName, suffix, remoteArgocdConfig.Get(), r.inCluster || config.akuityManaged)
	// if cluster data cm is not found ignore error as this is the first time the cluster is being created
	if err != nil && !errors.Is(err, agentclient.ErrClusterDataSecretNotFound) {
		return err
	}

	if remoteArgocdConfig.originalConfig != nil && remoteArgocdConfig.config != nil && !config.akuityManaged {
		// only update if remote argocd kubeconfig was updated to use local svc but the agent is self-hosted
		originalConfig := remoteArgocdConfig.Get(true)
		values.RemoteArgocd.Kubeconfig.CertificateAuthorityData = ptr.To(string(originalConfig.CAData))
		values.RemoteArgocd.Kubeconfig.Server = ptr.To(originalConfig.Host)
		values.RemoteArgocd.Kubeconfig.InsecureSkipTlsVerify = ptr.To(originalConfig.Insecure)
		if !strings.HasPrefix(*values.RemoteArgocd.Kubeconfig.Server, "https://") {
			// need to append https:// to the server address if missing
			values.RemoteArgocd.Kubeconfig.Server = ptr.To(fmt.Sprintf("https://%s", originalConfig.Host))
		}
	}

	instanceSpec, err := agentData.instanceConfig.GetSpec()
	if err != nil {
		return fmt.Errorf("failed to get kargo instance spec: %w", err)
	}
	values.Kargo.GlobalServiceAccountNs = ptr.To(strings.Join(instanceSpec.GlobalServiceAccountNs, ","))
	values.Kargo.GlobalCredentialsNs = ptr.To(strings.Join(instanceSpec.GlobalCredentialsNs, ","))
	if !agentData.instanceConfig.ControllerCM.IsZero() {
		controllerCM, err := agentData.instanceConfig.GetControllerCMValues()
		if err != nil {
			return fmt.Errorf("failed to get controller cm values: %w", err)
		}
		values.Kargo.Configmap = controllerCM
	}

	values.SetNamespace(config.namespace)

	if config.argocdNs != "" {
		values.SetArgocdNamespace(config.argocdNs)
	}

	if values.Agent == nil {
		values.Agent = clusteragent.NewDataValuesAgent()
		values.Agent.SetVersion(version.GetLatestAgentVersion())
		values.Agent.SetLogUrl(config.logURL)
		values.Agent.SetPassword(config.password)
		values.Agent.SetAgentId(config.agentID)

	}

	if values.Kargo == nil {
		values.Kargo = clusteragent.NewDataValuesKargo()
	}

	// if custom akp version or >= 1.6.0 rc is used update image host to akuity repo
	// NOTE: even if the enterprise feature is disabled for the customer
	// allow the instance to run if ak version is selected or else it might error out,
	// this is possible if user demoed the feature and then later had it turned off
	if r.featureSvc.GetFeatureStatuses(ctx, nil).GetKargoEnterprise().Enabled() {
		if isAKPImageVersion(config.kargoVersion) || (semver.Compare(config.kargoVersion, "v1.6.0-rc.0") >= 0 && strings.Contains(config.kargoVersion, "rc")) {
			values.Kargo.SetImageHost(AkpVersionImgHost)
		}
	}

	// if unstable version use unstable image repo
	if strings.Contains(config.kargoVersion, "unstable") {
		if agentData.orgFeatureStatuses.GetKargoEnterprise().Enabled() {
			// use ak nightly release for unstable if enterprise enabled
			values.Kargo.SetImageHost(AkpVersionImgHost)
		}
		values.Kargo.SetImageRepo(UnstableKargoImageRepo)
	}

	values.Agent.SetStatusUpdateInterval(int32(r.cfg.AgentStatusUpdateIntervalSeconds))
	values.Kargo.ControllerResources = config.resources.ControllerRequirements

	kustomization := config.kustomization
	if r.cfg.AgentImage != "" && kustomization.FindImageByName(models.DefaultAgentImage) == nil {
		host, repo, agentVersion, err := manifests.SplitImage(r.cfg.AgentImage)
		if err != nil {
			return err
		}
		kustomization.SetImage(kustomize.Image{Name: models.DefaultAgentImage, NewName: fmt.Sprintf("%s/%s", host, repo), NewTag: agentVersion})
		values.Agent.SetVersion(agentVersion)
	}

	// Kargo and Agent image tags and managed explicitly in platform settings
	if image := kustomization.FindImageByName(models.DefaultKargoImage); image != nil {
		image.NewTag = config.kargoVersion
	}
	if image := kustomization.FindImageByName(models.DefaultAgentImage); image != nil {
		image.NewTag = config.agentVersion
	}

	upgraderValues := clusterupgrader.NewDataValues()
	upgraderValues.SetNamespace(config.namespace)
	upgraderValues.SetClusterName(config.agentName)
	upgraderValues.SetGeneration(int32(config.generation))

	values.Kustomization = kustomization.AsMap()
	values.Kargo.SetGeneration(int32(config.generation))
	values.Kargo.SetVersion(config.kargoVersion)

	if config.agentVersion != "" {
		upgraderValues.SetAgent(clusterupgrader.DataValuesAgent{Version: ptr.To(config.agentVersion)})
		values.Agent.SetVersion(config.agentVersion)
	}
	version.UpdateAgentImageHost(values.Agent)
	version.UpdateAgentImageHost(upgraderValues.Agent)

	values.Kargo.SetDefaultShard(config.defaultShard)
	if remoteArgocdConfig.Get() != nil {
		values.RemoteArgocd.SetEnabled(true)
	}
	values.Kargo.SetAkuityManaged(config.akuityManaged)

	if !config.akuityManaged {
		values.KargoWebhook = clusteragent.NewDataValuesKargoWebhook()
		values.KargoWebhook.Cert = &config.webhookCert
		values.KargoWebhook.Key = &config.webhookKey
		if r.customCert != "" {
			// custom cert for cplane services is not needed if it's akuity managed
			// as those agents use internal service endpoints
			values.Agent.CustomCplaneCert = ptr.To(r.customCert)
		}
	}

	return tnt.InitializeAgentData(ctx, clusterConfig, values, upgraderValues)
}

func (r *kargoAgentReconciler) updateAgentPrivateSpec(ctx context.Context, agentData *agentRelatedData) (bool, error) {
	privateSpec, err := agentData.agent.GetPrivateSpec()
	if err != nil {
		return false, err
	}

	privateSpecModified, err := modifyKargoAgentPrivateSpec(privateSpec, agentData)
	if err != nil {
		return false, err
	}

	if privateSpecModified {
		logging.GetContextLogger(ctx).Info("regenerating private spec")
		if err := agentData.agent.SetPrivateSpec(*privateSpec); err != nil {
			return false, err
		}

		return true, r.repoSet.KargoAgents().Update(ctx, agentData.agent, models.KargoAgentColumns.PrivateSpec)
	}

	return false, nil
}

func modifyKargoAgentPrivateSpec(privateSpec *models.KargoAgentPrivateSpec, agentData *agentRelatedData) (bool, error) {
	privateSpecModified := false

	if privateSpec.WebhookKey == "" || privateSpec.WebhookCert == "" {
		webhookKey, webhookCert, err := GetKargoAgentWebhookCert(agentData.agent.Namespace)
		if err != nil {
			return false, err
		}
		privateSpec.WebhookKey = webhookKey
		privateSpec.WebhookCert = webhookCert
		privateSpecModified = true
	} else {
		cert, err := ParseCert(privateSpec.WebhookCert)
		if err != nil {
			return false, err
		}
		// if webhook cert is expiring in 5 days, renew it
		if cert.NotAfter.Before(time.Now().AddDate(0, 0, 5)) {
			webhookKey, webhookCert, err := GetKargoAgentWebhookCert(agentData.agent.Namespace)
			if err != nil {
				return false, err
			}
			privateSpec.WebhookKey = webhookKey
			privateSpec.WebhookCert = webhookCert
			privateSpecModified = true
		} else {
			// check if the kargo agent namespace was updated
			expected := ExpectedCertDNSNames(agentData.agent.Namespace, false, true)
			if !reflect.DeepEqual(cert.DNSNames, expected) {
				webhookKey, webhookCert, err := GetKargoAgentWebhookCert(agentData.agent.Namespace)
				if err != nil {
					return false, err
				}
				privateSpec.WebhookKey = webhookKey
				privateSpec.WebhookCert = webhookCert
				privateSpecModified = true
			}
		}
	}

	if privateSpec.AgentPassword == "" {
		password, err := database.RandomAlphabetString()
		if err != nil {
			return false, err
		}
		privateSpec.AgentPassword = password
		privateSpecModified = true
	}

	return privateSpecModified, nil
}
