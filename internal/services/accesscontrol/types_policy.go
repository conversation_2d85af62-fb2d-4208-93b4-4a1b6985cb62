package accesscontrol

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const ResourceAny = "*"

func checkWorkspaceOwnership(ctx context.Context, db boil.ContextExecutor, orgID, resource string) (bool, error) {
	if resource == ResourceAny {
		return true, nil
	}
	rs := client.NewRepoSet(db)
	_, err := rs.Workspaces().Filter(models.WorkspaceWhere.OrganizationID.EQ(orgID), models.WorkspaceWhere.ID.EQ(resource)).One(ctx)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}
	return false, err
}

func checkTeamOwnership(ctx context.Context, db boil.ContextExecutor, orgID, resource string) (bool, error) {
	if resource == ResourceAny {
		return true, nil
	}
	rs := client.NewRepoSet(db)
	_, err := rs.Teams().Filter(models.TeamWhere.OrganizationID.EQ(orgID), models.TeamWhere.Name.EQ(resource)).One(ctx)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}
	return false, err
}

func checkWorkspaceMemberOwnership(ctx context.Context, db boil.ContextExecutor, orgID, resource string) (bool, error) {
	if resource == ResourceAny {
		return true, nil
	}
	workspaceID, memberID, err := ParseWorkspaceResource(resource)
	if err != nil {
		return false, err
	}
	rs := client.NewRepoSet(db)

	ok, err := checkWorkspaceOwnership(ctx, db, orgID, workspaceID)
	if err != nil {
		return false, err
	}
	if !ok {
		return false, nil
	}
	if memberID == ResourceAny {
		return true, nil
	}

	_, err = rs.WorkspaceMembers().Filter(
		models.WorkspaceMemberWhere.WorkspaceID.EQ(workspaceID),
	).GetByID(ctx, memberID)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}
	return false, err
}

func checkInstanceOwnership(ctx context.Context, db boil.ContextExecutor, orgID, resource string) (bool, error) {
	if resource == ResourceAny {
		return true, nil
	}
	workspaceID, instanceID, err := ParseWorkspaceResource(resource)
	if err != nil {
		return false, err
	}
	rs := client.NewRepoSet(db)
	if workspaceID == "" && instanceID != ResourceAny {
		inst, err := rs.ArgoCDInstances().Filter(models.ArgoCDInstanceWhere.OrganizationOwner.EQ(orgID)).GetByID(ctx, instanceID)
		if err != nil {
			return false, err
		}
		workspaceID = inst.WorkspaceID.String
	}

	_, err = rs.ArgoCDInstances().Filter(
		models.ArgoCDInstanceWhere.OrganizationOwner.EQ(orgID),
		models.ArgoCDInstanceWhere.WorkspaceID.EQ(null.StringFrom(workspaceID)),
	).GetByID(ctx, instanceID)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}
	return false, err
}

func checkInstanceClusterOwnership(ctx context.Context, db boil.ContextExecutor, orgID, resource string) (bool, error) {
	workspaceID, instanceID, clusterName, err := ParseClusterResource(resource)
	if err != nil {
		return false, err
	}

	// first we are checking that provided instance is a part of organization.
	ok, err := checkInstanceOwnership(ctx, db, orgID, fmt.Sprintf(workspaceResourceFormat, workspaceID, instanceID))
	if err != nil {
		return false, err
	}
	if !ok {
		return false, nil
	}

	// then we are checking there there is a cluster with such name for specific instance.
	rs := client.NewRepoSet(db)
	_, err = rs.ArgoCDClusters(qm.Select("1")).Filter(models.ArgoCDClusterWhere.InstanceID.EQ(instanceID)).Filter(models.ArgoCDClusterWhere.Name.EQ(clusterName)).One(ctx)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}
	return false, err
}

func checkCustomRoleOwnership(ctx context.Context, db boil.ContextExecutor, orgID, resource string) (bool, error) {
	if resource == ResourceAny {
		return true, nil
	}
	rs := client.NewRepoSet(db)
	_, err := rs.CustomRoles().Filter(models.CustomRoleWhere.WorkspaceID.EQ(null.NewString("", false)), models.CustomRoleWhere.OrganizationID.EQ(orgID)).GetByID(ctx, resource)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}
	return false, err
}

func checkWorkspaceCustomRoleOwnership(ctx context.Context, db boil.ContextExecutor, orgID, resource string) (bool, error) {
	if resource == ResourceAny {
		return true, nil
	}
	workspaceID, keyID, err := ParseWorkspaceResource(resource)
	if err != nil {
		return false, err
	}
	rs := client.NewRepoSet(db)

	ok, err := checkWorkspaceOwnership(ctx, db, orgID, workspaceID)
	if err != nil {
		return false, err
	}
	if !ok {
		return false, nil
	}
	if keyID == ResourceAny {
		return true, nil
	}
	_, err = rs.CustomRoles().Filter(models.CustomRoleWhere.WorkspaceID.EQ(null.StringFrom(workspaceID)), models.CustomRoleWhere.OrganizationID.EQ(orgID)).GetByID(ctx, keyID)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}
	return false, err
}

func checkAPIKeyOwnership(ctx context.Context, db boil.ContextExecutor, orgID, resource string) (bool, error) {
	if resource == ResourceAny {
		return true, nil
	}
	rs := client.NewRepoSet(db)
	_, err := rs.APIKeys().Filter(models.APIKeyWhere.WorkspaceID.EQ(null.NewString("", false)), models.APIKeyWhere.Organization.EQ(null.StringFrom(orgID))).GetByID(ctx, resource)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}
	return false, err
}

func checkWorkspaceAPIKeyOwnership(ctx context.Context, db boil.ContextExecutor, orgID, resource string) (bool, error) {
	if resource == ResourceAny {
		return true, nil
	}
	workspaceID, keyID, err := ParseWorkspaceResource(resource)
	if err != nil {
		return false, err
	}
	rs := client.NewRepoSet(db)

	ok, err := checkWorkspaceOwnership(ctx, db, orgID, workspaceID)
	if err != nil {
		return false, err
	}
	if !ok {
		return false, nil
	}
	if keyID == ResourceAny {
		return true, nil
	}
	_, err = rs.APIKeys().Filter(models.APIKeyWhere.WorkspaceID.EQ(null.StringFrom(workspaceID)), models.APIKeyWhere.Organization.EQ(null.StringFrom(orgID))).GetByID(ctx, keyID)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}
	return false, err
}

func checkKargoInstanceOwnership(ctx context.Context, db boil.ContextExecutor, orgID, resource string) (bool, error) {
	if resource == ResourceAny {
		return true, nil
	}
	workspaceID, instanceID, err := ParseWorkspaceResource(resource)
	if err != nil {
		return false, err
	}
	rs := client.NewRepoSet(db)
	if workspaceID == "" && instanceID != ResourceAny {
		inst, err := rs.KargoInstances().Filter(models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(orgID))).GetByID(ctx, instanceID)
		if err != nil {
			return false, err
		}
		workspaceID = inst.WorkspaceID.String
	}

	_, err = rs.KargoInstances().Filter(
		models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(orgID)),
		models.KargoInstanceWhere.WorkspaceID.EQ(null.StringFrom(workspaceID)),
	).GetByID(ctx, instanceID)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}
	return false, err
}

func checkKargoInstanceAgentOwnership(ctx context.Context, db boil.ContextExecutor, orgID, resource string) (bool, error) {
	workspaceID, instanceID, agentName, err := ParseAgentResource(resource)
	if err != nil {
		return false, err
	}

	// first we are checking that provided instance is a part of organization.
	ok, err := checkKargoInstanceOwnership(ctx, db, orgID, fmt.Sprintf(workspaceResourceFormat, workspaceID, instanceID))
	if err != nil {
		return false, err
	}
	if !ok {
		return false, nil
	}
	if agentName == ResourceAny {
		return true, nil
	}

	// then we are checking there is an agent with such name for specific instance.
	rs := client.NewRepoSet(db)
	_, err = rs.KargoAgents(
		models.KargoAgentWhere.InstanceID.EQ(instanceID),
		models.KargoAgentWhere.Name.EQ(agentName),
	).One(ctx)
	if err == nil {
		return true, nil
	}
	if errors.Is(err, sql.ErrNoRows) {
		return false, nil
	}
	return false, err
}

// func (a Action) String() string {
// 	return fmt.Sprintf("%s:%s:%s", a.Object, a.Verb, a.Resource)
// }

// func ParseAction(action string) (*Action, error) {
// 	segments := strings.Split(action, ":")
// 	if len(segments) != 3 {
// 		return nil, fmt.Errorf("invalid action: %q", action)
// 	}
// 	return &Action{
// 		Object:   Object(segments[0]),
// 		Verb:     Verb(segments[1]),
// 		Resource: segments[2],
// 	}, nil
// }

func NewActionGetOrganizationWorkspaces(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationWorkspaces,
		Verb:     permissions.VerbGet,
		Resource: resource,
	}
}

func NewActionCreateOrganizationWorkspaces() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationWorkspaces,
		Verb:     permissions.VerbCreate,
		Resource: ResourceAny,
	}
}

func NewActionUpdateOrganizationWorkspaces(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationWorkspaces,
		Verb:     permissions.VerbUpdate,
		Resource: resource,
	}
}

func NewActionDeleteOrganizationWorkspaces(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationWorkspaces,
		Verb:     permissions.VerbDelete,
		Resource: resource,
	}
}

func NewActionGetWorkspaceAPIKeys(workspace, id string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceAPIKeys,
		Verb:     permissions.VerbGet,
		Resource: FormatWorkspaceResource(workspace, id),
	}
}

func NewActionCreateWorkspaceAPIKeys(workspace string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceAPIKeys,
		Verb:     permissions.VerbCreate,
		Resource: FormatWorkspaceResource(workspace, ResourceAny),
	}
}

func NewActionUpdateWorkspaceAPIKeys(workspace, id string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceAPIKeys,
		Verb:     permissions.VerbUpdate,
		Resource: FormatWorkspaceResource(workspace, id),
	}
}

func NewActionDeleteWorkspaceAPIKeys(workspace, id string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceAPIKeys,
		Verb:     permissions.VerbDelete,
		Resource: FormatWorkspaceResource(workspace, id),
	}
}

func NewActionGetAPIKeys(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAPIKeys,
		Verb:     permissions.VerbGet,
		Resource: resource,
	}
}

func NewActionCreateAPIKeys() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAPIKeys,
		Verb:     permissions.VerbCreate,
		Resource: ResourceAny,
	}
}

func NewActionUpdateAPIKeys(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAPIKeys,
		Verb:     permissions.VerbUpdate,
		Resource: resource,
	}
}

func NewActionDeleteAPIKeys(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAPIKeys,
		Verb:     permissions.VerbDelete,
		Resource: resource,
	}
}

func NewActionGetCustomRole(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationCustomRole,
		Verb:     permissions.VerbGet,
		Resource: resource,
	}
}

func NewActionCreateCustomRole() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationCustomRole,
		Verb:     permissions.VerbCreate,
		Resource: ResourceAny,
	}
}

func NewActionUpdateCustomRole(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationCustomRole,
		Verb:     permissions.VerbUpdate,
		Resource: resource,
	}
}

func NewActionDeleteCustomRole(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationCustomRole,
		Verb:     permissions.VerbDelete,
		Resource: resource,
	}
}

func NewActionGetWorkspaceCustomRole(workspace, id string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceCustomRole,
		Verb:     permissions.VerbGet,
		Resource: FormatWorkspaceResource(workspace, id),
	}
}

func NewActionCreateWorkspaceCustomRole(workspace string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceCustomRole,
		Verb:     permissions.VerbCreate,
		Resource: FormatWorkspaceResource(workspace, ResourceAny),
	}
}

func NewActionUpdateWorkspaceCustomRole(workspace, id string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceCustomRole,
		Verb:     permissions.VerbUpdate,
		Resource: FormatWorkspaceResource(workspace, id),
	}
}

func NewActionDeleteWorkspaceCustomRole(workspace, id string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceCustomRole,
		Verb:     permissions.VerbDelete,
		Resource: FormatWorkspaceResource(workspace, id),
	}
}

func NewActionGetOrganizationAuditLog() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAuditLog,
		Verb:     permissions.VerbGet,
		Resource: ResourceAny,
	}
}

const workspaceResourceFormat = "%s/%s"

func FormatWorkspaceResource(workspaceID, instanceID string) string {
	return fmt.Sprintf(workspaceResourceFormat, workspaceID, instanceID)
}

func ParseWorkspaceResource(resource string) (string, string, error) {
	parts := strings.Split(resource, "/")
	if len(parts) != 2 {
		return "", "", fmt.Errorf("wrong workspace instance resource format, should be in format of - workspaceID/resourceID, but we have %s", resource)
	}
	return parts[0], parts[1], nil
}

func NewActionUpdateWorkspaceMemberRole(workspaceID, memberID string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceMemberRole,
		Verb:     permissions.VerbUpdate,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, memberID),
	}
}

func NewActionGetWorkspaceMembers(workspaceID, memberID string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceMembers,
		Verb:     permissions.VerbGet,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, memberID),
	}
}

func NewActionCreateWorkspaceMembers(workspaceID string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceMembers,
		Verb:     permissions.VerbCreate,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, ResourceAny),
	}
}

func NewActionDeleteWorkspaceMembers(workspaceID, memberID string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceMembers,
		Verb:     permissions.VerbDelete,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, memberID),
	}
}

func NewActionGetWorkspaceInstances(workspaceID, instanceID string) permissions.Action {
	action := permissions.Action{
		Object:   permissions.ObjectWorkspaceInstances,
		Verb:     permissions.VerbGet,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, instanceID),
	}
	return action
}

func NewActionGetOrganizationAllInstances() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceInstances,
		Verb:     permissions.VerbGet,
		Resource: ResourceAny,
	}
}

func NewActionCreateWorkspaceInstances(workspaceID string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceInstances,
		Verb:     permissions.VerbCreate,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, ResourceAny),
	}
}

func NewActionUpdateWorkspaceInstances(workspaceID, instanceID string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceInstances,
		Verb:     permissions.VerbUpdate,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, instanceID),
	}
}

func NewActionDeleteWorkspaceInstances(workspaceID, instanceID string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceInstances,
		Verb:     permissions.VerbDelete,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, instanceID),
	}
}

func NewActionDeleteWorkspaceKargoInstances(workspaceID, instanceID string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceKargoInstances,
		Verb:     permissions.VerbDelete,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, instanceID),
	}
}

func NewActionGetWorkspaceKargoInstances(workspaceID, instanceID string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceKargoInstances,
		Verb:     permissions.VerbGet,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, instanceID),
	}
}

func NewActionGetOrganizationAllKargoInstances() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceKargoInstances,
		Verb:     permissions.VerbGet,
		Resource: ResourceAny,
	}
}

func NewActionCreateWorkspaceKargoInstances(workspaceID string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceKargoInstances,
		Verb:     permissions.VerbCreate,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, ResourceAny),
	}
}

func NewActionUpdateWorkspaceKargoInstances(workspaceID, instanceID string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceKargoInstances,
		Verb:     permissions.VerbUpdate,
		Resource: fmt.Sprintf(workspaceResourceFormat, workspaceID, instanceID),
	}
}

func NewActionGetOrganizationMembers(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationMembers,
		Verb:     permissions.VerbGet,
		Resource: resource,
	}
}

func NewActionCreateOrganizationMembers(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationMembers,
		Verb:     permissions.VerbCreate,
		Resource: resource,
	}
}

func NewActionUpdateOrganizationMembers(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationMembers,
		Verb:     permissions.VerbUpdate,
		Resource: resource,
	}
}

func NewActionDeleteOrganizationMembers(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationMembers,
		Verb:     permissions.VerbDelete,
		Resource: resource,
	}
}

func NewActionUpdateOrganizationMemberRole(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationMemberRole,
		Verb:     permissions.VerbUpdate,
		Resource: resource,
	}
}

func NewActionCreateOrganizationAdmins(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAdmins,
		Verb:     permissions.VerbCreate,
		Resource: resource,
	}
}

func NewActionUpdateOrganizationAdmins(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAdmins,
		Verb:     permissions.VerbUpdate,
		Resource: resource,
	}
}

func NewActionDeleteOrganizationAdmins(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAdmins,
		Verb:     permissions.VerbDelete,
		Resource: resource,
	}
}

func NewActionGetOrganizationBilling(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationBilling,
		Verb:     permissions.VerbGet,
		Resource: resource,
	}
}

func NewActionCreateOrganizationBilling(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationBilling,
		Verb:     permissions.VerbCreate,
		Resource: resource,
	}
}

func NewActionUpdateOrganizationBilling(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationBilling,
		Verb:     permissions.VerbUpdate,
		Resource: resource,
	}
}

func NewActionCancelSubscription(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationBilling,
		Verb:     permissions.VerbDelete,
		Resource: resource,
	}
}

func NewActionGetOrganization(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganization,
		Verb:     permissions.VerbGet,
		Resource: resource,
	}
}

func NewActionUpdateOrganization(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganization,
		Verb:     permissions.VerbUpdate,
		Resource: resource,
	}
}

func NewActionDeleteOrganization(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganization,
		Verb:     permissions.VerbDelete,
		Resource: resource,
	}
}

func NewActionCreateOrganizationSSOConfiguration() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationSSOConfiguration,
		Verb:     permissions.VerbCreate,
		Resource: ResourceAny,
	}
}

func NewActionGetOrganizationSSOConfiguration() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationSSOConfiguration,
		Verb:     permissions.VerbGet,
		Resource: ResourceAny,
	}
}

func NewActionUpdateOrganizationSSOConfiguration() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationSSOConfiguration,
		Verb:     permissions.VerbUpdate,
		Resource: ResourceAny,
	}
}

func NewActionDeleteOrganizationSSOConfiguration() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationSSOConfiguration,
		Verb:     permissions.VerbDelete,
		Resource: ResourceAny,
	}
}

func NewActionGetOIDCMap(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationOIDCMap,
		Verb:     permissions.VerbGet,
		Resource: ResourceAny,
	}
}

func NewActionUpdateOIDCMap(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationOIDCMap,
		Verb:     permissions.VerbUpdate,
		Resource: ResourceAny,
	}
}

func NewActionGetTeams(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationTeams,
		Verb:     permissions.VerbGet,
		Resource: resource,
	}
}

func NewActionCreateTeams() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationTeams,
		Verb:     permissions.VerbCreate,
		Resource: ResourceAny,
	}
}

func NewActionUpdateTeams(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationTeams,
		Verb:     permissions.VerbUpdate,
		Resource: resource,
	}
}

func NewActionDeleteTeams(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationTeams,
		Verb:     permissions.VerbDelete,
		Resource: resource,
	}
}

func NewActionGetTeamMembers(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectTeamMembers,
		Verb:     permissions.VerbGet,
		Resource: resource,
	}
}

func NewActionCreateTeamMembers(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectTeamMembers,
		Verb:     permissions.VerbCreate,
		Resource: resource,
	}
}

func NewActionDeleteTeamMembers(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectTeamMembers,
		Verb:     permissions.VerbDelete,
		Resource: resource,
	}
}

func NewActionListOrgNotificationConfigs() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationNotificationConfigs,
		Verb:     permissions.VerbGet,
		Resource: ResourceAny,
	}
}

func NewActionGetOrgNotificationConfig(id string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationNotificationConfigs,
		Verb:     permissions.VerbGet,
		Resource: id,
	}
}

func NewActionCreateOrgNotificationConfig() permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationNotificationConfigs,
		Verb:     permissions.VerbCreate,
		Resource: ResourceAny,
	}
}

func NewActionUpdateOrgNotificationConfig(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationNotificationConfigs,
		Verb:     permissions.VerbUpdate,
		Resource: resource,
	}
}

func NewActionDeleteOrgNotificationConfig(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationNotificationConfigs,
		Verb:     permissions.VerbDelete,
		Resource: resource,
	}
}

const agentResourceFormat = "%s/%s/%s"

func ParseAgentResource(resource string) (string, string, string, error) {
	parts := strings.Split(resource, "/")
	if len(parts) == 2 && parts[1] == ResourceAny {
		parts = append(parts, ResourceAny)
	}
	if len(parts) != 3 {
		return "", "", "", fmt.Errorf("wrong agent resource format, should be in format of - workspaceID/instanceID/agentName, but we have %s", resource)
	}
	return parts[0], parts[1], parts[2], nil
}

func NewActionGetKargoInstanceAgents(workspaceID, instanceID, agentName string) permissions.Action {
	// resource should be in format of - workspaceID/instanceID/clusterName
	action := permissions.Action{
		Object:   permissions.ObjectWorkspaceKargoInstanceAgents,
		Verb:     permissions.VerbGet,
		Resource: fmt.Sprintf(agentResourceFormat, workspaceID, instanceID, agentName),
	}
	return action
}

func NewActionCreateKargoInstanceAgents(workspaceID, instanceID, agentName string) permissions.Action {
	// resource should be in format of - workspaceID/instanceID/clusterName
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceKargoInstanceAgents,
		Verb:     permissions.VerbCreate,
		Resource: fmt.Sprintf(agentResourceFormat, workspaceID, instanceID, agentName),
	}
}

func NewActionUpdateKargoInstanceAgents(workspaceID, instanceID, agentName string) permissions.Action {
	// resource should be in format of - workspaceID/instanceID/clusterName
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceKargoInstanceAgents,
		Verb:     permissions.VerbUpdate,
		Resource: fmt.Sprintf(agentResourceFormat, workspaceID, instanceID, agentName),
	}
}

func NewActionDeleteKargoInstanceAgents(workspaceID, instanceID, agentName string) permissions.Action {
	// resource should be in format of - workspaceID/instanceID/clusterName
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceKargoInstanceAgents,
		Verb:     permissions.VerbDelete,
		Resource: fmt.Sprintf(agentResourceFormat, workspaceID, instanceID, agentName),
	}
}

const clusterResourceFormat = "%s/%s/%s"

func ParseClusterResource(resource string) (string, string, string, error) {
	parts := strings.Split(resource, "/")
	if len(parts) == 2 && parts[1] == ResourceAny {
		parts = append(parts, ResourceAny)
	}
	if len(parts) != 3 {
		return "", "", "", fmt.Errorf("wrong cluster resource format, should be in format of - workspaceID/instanceID/clusterName, but we have %s", resource)
	}
	return parts[0], parts[1], parts[2], nil
}

func NewActionGetInstanceClusters(workspaceID, instanceID, clusterName string) permissions.Action {
	// resource should be in format of - workspaceID/instanceID/clusterName
	action := permissions.Action{
		Object:   permissions.ObjectWorkspaceInstanceClusters,
		Verb:     permissions.VerbGet,
		Resource: fmt.Sprintf(clusterResourceFormat, workspaceID, instanceID, clusterName),
	}
	return action
}

func NewActionCreateInstanceClusters(workspaceID, instanceID, clusterName string) permissions.Action {
	// resource should be in format of - workspaceID/instanceID/clusterName
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceInstanceClusters,
		Verb:     permissions.VerbCreate,
		Resource: fmt.Sprintf(clusterResourceFormat, workspaceID, instanceID, clusterName),
	}
}

func NewActionUpdateInstanceClusters(workspaceID, instanceID, clusterName string) permissions.Action {
	// resource should be in format of - workspaceID/instanceID/clusterName
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceInstanceClusters,
		Verb:     permissions.VerbUpdate,
		Resource: fmt.Sprintf(clusterResourceFormat, workspaceID, instanceID, clusterName),
	}
}

func NewActionDeleteInstanceClusters(workspaceID, instanceID, clusterName string) permissions.Action {
	// resource should be in format of - workspaceID/instanceID/clusterName
	return permissions.Action{
		Object:   permissions.ObjectWorkspaceInstanceClusters,
		Verb:     permissions.VerbDelete,
		Resource: fmt.Sprintf(clusterResourceFormat, workspaceID, instanceID, clusterName),
	}
}

func NewActionGetKubernetesDashboard(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationKubernetesDashboard,
		Verb:     permissions.VerbGet,
		Resource: ResourceAny,
	}
}

func NewActionDeleteKubernetesDashboard(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationKubernetesDashboard,
		Verb:     permissions.VerbDelete,
		Resource: ResourceAny,
	}
}

func NewActionGetAISupportEngineer(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAISupportEngineer,
		Verb:     permissions.VerbGet,
		Resource: ResourceAny,
	}
}

func NewActionCreateAISupportEngineer(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAISupportEngineer,
		Verb:     permissions.VerbCreate,
		Resource: ResourceAny,
	}
}

func NewActionUpdateAISupportEngineer(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAISupportEngineer,
		Verb:     permissions.VerbUpdate,
		Resource: ResourceAny,
	}
}

func NewActionDeleteAISupportEngineer(resource string) permissions.Action {
	return permissions.Action{
		Object:   permissions.ObjectOrganizationAISupportEngineer,
		Verb:     permissions.VerbDelete,
		Resource: ResourceAny,
	}
}

type OrganizationBinding struct {
	OrganizationID string
	Permissions    permissions.Permissions
}

type ActorType string

const (
	ActorTypeAPIKey ActorType = "apikey"
	ActorTypeUser   ActorType = "user"
	ActorTypeArgoCD ActorType = "argocd"
	ActorTypeKargo  ActorType = "kargo"
)

type Actor struct {
	Type                 ActorType
	ID                   string
	OrganizationBindings []OrganizationBinding
	Extras               map[string]interface{}
}

func (a Actor) AddOrganizationBinding(ob OrganizationBinding) Actor {
	for idx, b := range a.OrganizationBindings {
		if ob.OrganizationID == b.OrganizationID {
			bindings := make([]OrganizationBinding, len(a.OrganizationBindings))
			copy(bindings, a.OrganizationBindings)
			// We don't have to deduplicate permission elements since casbin will take care of it.
			bindings[idx].Permissions.Actions = append(bindings[idx].Permissions.Actions, ob.Permissions.Actions...)
			bindings[idx].Permissions.Roles = append(bindings[idx].Permissions.Roles, ob.Permissions.Roles...)
			bindings[idx].Permissions.CustomRoles = append(bindings[idx].Permissions.CustomRoles, ob.Permissions.CustomRoles...)
			return Actor{
				Type:                 a.Type,
				ID:                   a.ID,
				OrganizationBindings: bindings,
				Extras:               a.Extras,
			}
		}
	}
	return Actor{
		Type:                 a.Type,
		ID:                   a.ID,
		OrganizationBindings: append(a.OrganizationBindings, ob),
		Extras:               a.Extras,
	}
}
