package k8sresource

import (
	"bufio"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"reflect"
	"regexp"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/samber/lo"
	"github.com/samber/lo/mutable"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	k8sresource "k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/utils/ptr"

	agentv1 "github.com/akuityio/akuity-platform/agent/pkg/api/gen/agent/v1"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	eventsutil "github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	k8sv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/k8s/v1"

	_ "embed"
)

var (
	objectColNum = reflect.TypeOf(models.ArgoCDClusterK8SObjectColumns).NumField()
	eventColNum  = reflect.TypeOf(models.ArgoCDClusterK8SEventColumns).NumField()
)

type Service struct {
	resourceRepoSet

	db boil.ContextExecutor

	log      *logr.Logger
	enforcer *Enforcer
}

type Option func(*Service)

func WithLogger(log logr.Logger) Option {
	return func(s *Service) {
		s.log = &log
	}
}

func WithEnforcer(enforcer *Enforcer) Option {
	return func(s *Service) {
		s.enforcer = enforcer
	}
}

func NewServiceWithOptions(rs client.RepoSet, db boil.ContextExecutor, organizationID string, opts ...Option) *Service {
	svc := &Service{
		resourceRepoSet: newResourceRepoSet(rs, organizationID),
		db:              db,
	}

	for _, opt := range opts {
		opt(svc)
	}
	if svc.log == nil {
		nopLogger := logr.Discard()
		svc.log = &nopLogger
	}
	return svc
}

func (s *Service) buildResource(orgID, instanceID, clusterID string, resource *agentv1.Resource) (*models.ArgoCDClusterK8SObject, error) {
	obj := &models.ArgoCDClusterK8SObject{
		ID:             resource.GetUid(),
		ClusterID:      clusterID,
		OrganizationID: orgID,
		InstanceID:     instanceID,
		Name:           resource.GetName(),
		Namespace:      null.StringFrom(resource.GetNamespace()),
		Group:          null.StringFrom(resource.GetGroupVersionKind().GetGroup()),
		Version:        null.StringFrom(resource.GetGroupVersionKind().GetVersion()),
		Kind:           null.StringFrom(resource.GetGroupVersionKind().GetKind()),
		OwnerID:        null.StringFrom(resource.GetOwnerId()),
		ChildrenCount:  int(resource.GetChildrenCount()),
	}
	if resource.GetArgocdApplicationInfo().GetName() != "" || resource.GetArgocdApplicationInfo().GetSyncStatus() != "" || resource.GetArgocdApplicationInfo().GetHealthStatus() != "" {
		appInfoJSON, err := json.Marshal(resource.GetArgocdApplicationInfo())
		if err != nil {
			return nil, err
		}
		obj.ArgocdApplicationInfo = null.JSONFrom(appInfoJSON)
	}

	columns, err := buildColumns(resource.GetColumns(), resource.GetGroupVersionKind().GetGroup(), resource.GetGroupVersionKind().GetKind())
	if err != nil {
		return nil, err
	}

	if len(columns) != 0 {
		columnsJSON, err := json.Marshal(columns)
		if err != nil {
			return nil, err
		}
		obj.Columns = null.JSONFrom(columnsJSON)
	}

	if resource.GetCreateTime() != "" {
		ts, err := time.Parse(time.RFC3339, resource.GetCreateTime())
		if err != nil {
			return nil, err
		}
		obj.CreationTimestamp = ts
	}
	if resource.GetDeleteTime() != "" {
		ts, err := time.Parse(time.RFC3339, resource.GetDeleteTime())
		if err != nil {
			return nil, err
		}
		obj.DeletionTimestamp = null.TimeFrom(ts)
	}
	return obj, nil
}

// UpsertResources inserts or updates multiple Kubernetes resources in the database using PostgreSQL's COPY command.
// The method follows these steps:
// 1. Creates a temporary table with the same schema as argo_cd_cluster_k8s_object.
// 2. Streams resource data in CSV format to the temp table using COPY.
// 3. Performs a final INSERT with ON CONFLICT handling to merge data from the temp table into the argo_cd_cluster_k8s_object table.
func (s *Service) UpsertResources(ctx context.Context, stmtPreparer database.Commiter, instanceID, clusterID string, resources ...*agentv1.Resource) error {
	seen := make(map[string]*agentv1.Resource)
	var duplicates []string
	duplicatesDetails := map[string][]*agentv1.Resource{}
	for _, resource := range resources {
		if _, exists := seen[resource.GetUid()]; exists {
			duplicates = append(duplicates, resource.GetUid())
			if len(duplicatesDetails[resource.GetUid()]) == 0 {
				duplicatesDetails[resource.GetUid()] = append(duplicatesDetails[resource.GetUid()], seen[resource.GetUid()])
			}
			duplicatesDetails[resource.GetUid()] = append(duplicatesDetails[resource.GetUid()], resource)
			seen[resource.GetUid()].ArgocdApplicationInfo = resource.GetArgocdApplicationInfo()
			seen[resource.GetUid()].Columns = resource.GetColumns()
			seen[resource.GetUid()].ChildrenCount = resource.GetChildrenCount()
			seen[resource.GetUid()].DeleteTime = resource.GetDeleteTime()
		} else {
			seen[resource.GetUid()] = resource
		}
	}
	if len(duplicates) > 0 {
		s.log.Info("Encountering duplicate resources",
			"instance_id", instanceID,
			"cluster_id", clusterID,
			"duplicate_count", len(duplicates),
			"duplicatesDetails", duplicatesDetails,
		)
	}

	uniqueResources := lo.Values(seen)
	if len(uniqueResources) == 0 {
		return nil
	}

	tempTableName := fmt.Sprintf("temp_objects_%d", time.Now().UnixNano())

	// Create a temporary table with the same schema as argo_cd_cluster_k8s_object.
	// This temporary table will be dropped automatically after the transaction is committed.
	createTempTableQuery := fmt.Sprintf(`
		CREATE TEMP TABLE %s (LIKE argo_cd_cluster_k8s_object INCLUDING ALL)
	`, tempTableName)
	if _, err := s.db.ExecContext(ctx, createTempTableQuery); err != nil {
		return fmt.Errorf("failed to create temp table: %w", err)
	}

	delimiter := ";"
	// Postgres cannot distinguish the empty string and NULL, so we use a marker to distinguish them.
	nullMarker := "null" + tempTableName
	r, w := io.Pipe()
	go func() {
		defer w.Close()
		for _, resource := range uniqueResources {
			obj, err := s.buildResource(s.organizationID, instanceID, clusterID, resource)
			if err != nil {
				w.CloseWithError(err)
				return
			}

			// Handle NULL values with a special marker
			deletionTimestamp := nullMarker
			if !obj.DeletionTimestamp.IsZero() {
				deletionTimestamp = obj.DeletionTimestamp.Time.Format(time.RFC3339)
			}
			argocdApplicationInfo := nullMarker
			if !obj.ArgocdApplicationInfo.IsZero() {
				argocdApplicationInfo = string(obj.ArgocdApplicationInfo.JSON)
			}
			columns := nullMarker
			if !obj.Columns.IsZero() {
				// This is a workaround to avoid the delimiter is included in the column value.
				columns = strings.ReplaceAll(string(obj.Columns.JSON), delimiter, ".")
			}
			cols := []string{
				obj.ID,
				obj.ClusterID,
				obj.OrganizationID,
				obj.InstanceID,
				obj.Name,
				obj.Namespace.String,
				obj.Group.String,
				obj.Version.String,
				obj.Kind.String,
				argocdApplicationInfo,
				columns,
				obj.CreationTimestamp.Format(time.RFC3339),
				obj.OwnerID.String,
				fmt.Sprintf("%d", obj.ChildrenCount),
				deletionTimestamp,
			}
			if _, err := w.Write([]byte(strings.Join(cols, delimiter) + "\n")); err != nil {
				w.CloseWithError(err)
				return
			}
		}
	}()

	// Copy data to the temporary table.
	copyQuery := fmt.Sprintf(`COPY %s FROM STDIN WITH (FORMAT text, NULL '%s', DELIMITER '%s')`, tempTableName, nullMarker, delimiter)
	stmt, err := stmtPreparer.PrepareContext(ctx, copyQuery)
	if err != nil {
		return fmt.Errorf("failed to prepare copy query: %w", err)
	}

	reader := bufio.NewReader(r)
	for {
		line, err := reader.ReadString('\n')
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read line: %w", err)
		}

		if _, err = stmt.ExecContext(ctx, strings.TrimSuffix(line, "\n")); err != nil {
			return fmt.Errorf("failed to copy data: %w", err)
		}
	}

	if _, err = stmt.ExecContext(ctx); err != nil {
		return fmt.Errorf("failed to execute copy query: %w", err)
	}

	// Insert data from the temporary table into the argo_cd_cluster_k8s_object table with conflict handling.
	insertQuery := fmt.Sprintf(`
INSERT INTO argo_cd_cluster_k8s_object
SELECT * FROM %s
ON CONFLICT (id, cluster_id)
DO UPDATE SET
	argocd_application_info = COALESCE(argo_cd_cluster_k8s_object.argocd_application_info, '{}'::jsonb) || COALESCE(EXCLUDED.argocd_application_info, '{}'::jsonb),
	columns = COALESCE(argo_cd_cluster_k8s_object.columns, '{}'::jsonb) || COALESCE(EXCLUDED.columns, '{}'::jsonb),
	children_count = EXCLUDED.children_count,
	deletion_timestamp = EXCLUDED.deletion_timestamp;
	`, tempTableName)

	if _, err := s.db.ExecContext(ctx, insertQuery); err != nil {
		return fmt.Errorf("insert from temp table failed: %w", err)
	}
	return nil
}

func (s *Service) DeleteResources(ctx context.Context, instanceID, clusterID string, resourceIDs ...string) error {
	if len(resourceIDs) == 0 {
		return nil
	}
	if err := s.ArgoCDClusterK8sObjects(
		models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
		models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(clusterID),
		models.ArgoCDClusterK8SObjectWhere.ID.IN(resourceIDs),
	).DeleteAll(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil
		}
		return err
	}
	return nil
}

func (s *Service) GetResource(ctx context.Context, clusterID, instanceID, resourceID string) (*models.ArgoCDClusterK8SObject, error) {
	k8sResource, err := s.ArgoCDClusterK8sObjects(
		models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
		models.ArgoCDClusterK8SObjectWhere.Group.NEQ(null.StringFrom(Container.Group)), models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(clusterID),
	).GetByID(ctx, resourceID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Errorf(codes.NotFound, "resource %s not found", resourceID)
		}
		return nil, err
	}
	return k8sResource, nil
}

func (s *Service) GetResourceDetail(ctx context.Context, clusterID, instanceID, resourceID string) (resource *models.ArgoCDClusterK8SObject, ownerInfo []*organizationv1.ResourceReferenceInfo, childrenInfo []*k8sv1.ResourceType, err error) {
	resource, err = s.GetResource(ctx, clusterID, instanceID, resourceID)
	if err != nil {
		return nil, nil, nil, err
	}
	if resource.OwnerID.String != "" {
		// we set it to 8 to avoid infinity loop.
		maxDepth := 8
		ownerID := resource.OwnerID.String
		ancestors := make([]*models.ArgoCDClusterK8SObject, 0, maxDepth)
		for i := 0; i < maxDepth; i++ {
			owner, err := s.ArgoCDClusterK8sObjects(
				models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
				models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(clusterID),
			).GetByID(ctx, ownerID)
			if err != nil {
				return nil, nil, nil, err
			}
			ownerID = owner.OwnerID.String
			ancestors = append(ancestors, owner)
			if ownerID == "" {
				break
			}
		}
		ownerInfo = lo.Map(ancestors, func(a *models.ArgoCDClusterK8SObject, _ int) *organizationv1.ResourceReferenceInfo {
			return &organizationv1.ResourceReferenceInfo{
				Id:        a.ID,
				Name:      ptr.To(a.Name),
				Namespace: ptr.To(a.Namespace.String),
				GroupVersionKind: &k8sv1.GroupVersionKind{
					Group:   a.Group.String,
					Version: a.Version.String,
					Kind:    a.Kind.String,
				},
			}
		})
		mutable.Reverse(ownerInfo)
	}
	if resource.GetChildrenCount() > 0 {
		children, err := s.ArgoCDClusterK8sObjects(
			models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID),
			models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(clusterID),
			models.ArgoCDClusterK8SObjectWhere.OwnerID.EQ(null.StringFrom(resource.ID)),
			models.ArgoCDClusterK8SObjectWhere.Group.NEQ(null.StringFrom(Container.Group)),
		).ListAll(ctx)
		if err != nil {
			return nil, nil, nil, err
		}
		m := make(map[string]*k8sv1.GroupVersionKind)
		for _, child := range children {
			gvk := fmt.Sprintf("%s/%s/%s", child.Group.String, child.Version.String, child.Kind.String)
			m[gvk] = &k8sv1.GroupVersionKind{
				Group:   child.Group.String,
				Version: child.Version.String,
				Kind:    child.Kind.String,
			}
		}
		childrenInfo = make([]*k8sv1.ResourceType, 0, len(m))
		for _, gvk := range m {
			childrenInfo = append(childrenInfo, &k8sv1.ResourceType{
				GroupVersionKind: &k8sv1.GroupVersionKind{
					Group:   gvk.Group,
					Version: gvk.Version,
					Kind:    gvk.Kind,
				},
			})
		}
	}
	return
}

func (s *Service) ListResources(ctx context.Context, instanceID, ownerID, group, kind, version string, clusterIDs, namespaces []string,
	offset, limit int, nameContains, name, orderBy, where string, hasDeletionTimestamp *bool,
) ([]*models.ArgoCDClusterK8SObject, error) {
	mods, err := s.listResourceMods(ctx, instanceID, ownerID, group, kind, version, clusterIDs, namespaces, offset, limit, false, nameContains, name, orderBy, where, hasDeletionTimestamp)
	if err != nil {
		return nil, err
	}

	resources, err := s.ArgoCDClusterK8sObjects(mods...).ListAll(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, status.Errorf(codes.Internal, "failed to list resources: %v", err)
	}
	return resources, nil
}

func (s *Service) CountResources(ctx context.Context, instanceID, ownerID, group, kind, version string, clusterIDs, namespaces []string, nameContains, name, where string, hasDeletionTimestamp *bool) (int64, error) {
	mods, err := s.listResourceMods(ctx, instanceID, ownerID, group, kind, version, clusterIDs, namespaces, 0, 0, true, nameContains, name, "", where, hasDeletionTimestamp)
	if err != nil {
		return 0, err
	}
	count, err := s.ArgoCDClusterK8sObjects(mods...).Count(ctx)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s *Service) SpotlightSearchResources(ctx context.Context, instanceID string, clusterIDs []string, query string, offset, limit int, aiSearch bool) ([]*models.ArgoCDClusterK8SObject, error) {
	mods, err := s.listSpotlightSearchResourceMods(instanceID, clusterIDs, query, offset, limit, false, aiSearch)
	if err != nil {
		return nil, err
	}

	resources, err := s.ArgoCDClusterK8sObjects(mods...).ListAll(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, status.Errorf(codes.Internal, "failed to list resources: %v", err)
	}
	return resources, nil
}

func (s *Service) CountSpotlightSearchResources(ctx context.Context, instanceID string, clusterIDs []string, query string, aiSearch bool) (int64, error) {
	mods, err := s.listSpotlightSearchResourceMods(instanceID, clusterIDs, query, 0, 0, true, aiSearch)
	if err != nil {
		return 0, err
	}
	count, err := s.ArgoCDClusterK8sObjects(mods...).Count(ctx)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s *Service) listResourceMods(ctx context.Context, instanceID, ownerID, group, kind, version string, clusterIDs, namespaces []string,
	offset, limit int, count bool, nameContains, name, orderBy, where string, hasDeletionTimestamp *bool,
) ([]qm.QueryMod, error) {
	var mods []qm.QueryMod
	mods = append(mods, models.ArgoCDClusterK8SObjectWhere.Group.NEQ(null.StringFrom(Container.Group)))
	if instanceID != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID))
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.ClusterID.IN(clusterIDs))
	}
	if len(namespaces) > 0 {
		ns := make([]string, 0, len(namespaces))
		for _, n := range namespaces {
			if n != "" {
				ns = append(ns, n)
			}
		}
		if len(ns) != 0 {
			mods = append(mods, models.ArgoCDClusterK8SObjectWhere.Namespace.IN(ns))
		}
	}
	if ownerID != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.OwnerID.EQ(null.StringFrom(ownerID)))
	}
	if group != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom(group)))
	}
	if kind != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom(kind)))
	}
	if version != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.Version.EQ(null.StringFrom(version)))
	}
	if nameContains != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.Name.ILIKE("%"+nameContains+"%"))
	}
	if name != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.Name.EQ(name))
	}
	if where != "" {
		whereMods, err := buildWhereQueryMods(where)
		if err != nil {
			return nil, err
		}
		mods = append(mods, whereMods...)
	}
	if hasDeletionTimestamp != nil {
		if *hasDeletionTimestamp {
			mods = append(mods, models.ArgoCDClusterK8SObjectWhere.DeletionTimestamp.IsNotNull())
			// show the resources that are stuck in deleting state for more than 1 hour
			mods = append(mods, models.ArgoCDClusterK8SObjectWhere.DeletionTimestamp.LT(null.TimeFrom(time.Now().Add(-1*time.Hour))))
		} else {
			mods = append(mods, models.ArgoCDClusterK8SObjectWhere.DeletionTimestamp.IsNull())
		}
	}

	if !count {
		if limit > 0 {
			mods = append(mods, qm.Limit(limit))
		}
		if offset > 0 {
			mods = append(mods, qm.Offset(offset))
		}
		cfg := OrderByConfig{
			SupportedOrderByFields: map[string]string{
				"argocdApplicationInfo": fmt.Sprintf("%s is null or %s = '{}' or %s->>'name' = ''",
					models.ArgoCDClusterK8SObjectColumns.ArgocdApplicationInfo, models.ArgoCDClusterK8SObjectColumns.ArgocdApplicationInfo, models.ArgoCDClusterK8SObjectColumns.ArgocdApplicationInfo),
				"creationTimestamp": models.ArgoCDClusterK8SObjectColumns.CreationTimestamp,
				"name":              models.ArgoCDClusterK8SObjectColumns.Name,
				"deletionTimestamp": models.ArgoCDClusterK8SObjectColumns.DeletionTimestamp,
			},
			DefaultOrderBy: "creation_timestamp desc",
		}
		ob, err := parseOrderBy(orderBy, cfg)
		if err != nil {
			return nil, err
		}
		if ob != "" {
			mods = append(mods, qm.OrderBy(ob))
		}
	}
	if s.enforcer != nil {
		filter, err := s.enforcer.GetK8SResourceListFilter(ctx, instanceID)
		if err != nil {
			return nil, err
		}
		mods = append(mods, filter)
	}
	return mods, nil
}

func (s *Service) listSpotlightSearchResourceMods(instanceID string, clusterIDs []string, query string, offset, limit int, count, aiSearch bool) ([]qm.QueryMod, error) {
	var mods []qm.QueryMod
	if instanceID != "" {
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.InstanceID.EQ(instanceID))
		mods = append(mods, models.ArgoCDClusterK8SObjectWhere.ClusterID.IN(clusterIDs))
	}

	// if this is used for context search from AI support engineer, we need to exclude the container and image resources by filtering with the internal group name
	if aiSearch {
		mods = append(mods, qm.Expr(qm.Or2(
			models.ArgoCDClusterK8SObjectWhere.Group.EQ(null.StringFrom(""))),
			models.ArgoCDClusterK8SObjectWhere.Kind.EQ(null.StringFrom("Namespace")),
		))
	}

	q := strings.TrimSpace(query)
	if q != "" {
		likePattern := "%" + q + "%"
		mods = append(mods, qm.Expr(
			models.ArgoCDClusterK8SObjectWhere.Name.ILIKE(likePattern),
			qm.Or2(models.ArgoCDClusterK8SObjectWhere.Namespace.ILIKE(null.StringFrom(likePattern))),
			qm.Or("COALESCE(argocd_application_info->>'name', '') ILIKE ?", likePattern),
		))
	}

	if !count {
		if q != "" {
			likePattern := "%" + q + "%"
			mods = append(mods, qm.OrderBy(`
				CASE
					WHEN name = ? THEN 1
					WHEN name ILIKE ? THEN 2
					WHEN namespace = ? THEN 3
					WHEN namespace ILIKE ? THEN 4
					WHEN COALESCE(argocd_application_info->>'name', '') = ? THEN 5
					WHEN COALESCE(argocd_application_info->>'name', '') ILIKE ? THEN 6
					ELSE 7
				END
			`, q, likePattern, q, likePattern, q, likePattern))
		}

		mods = append(mods, qm.OrderBy(`
			CASE kind
				WHEN 'Pod' THEN 1
				WHEN 'Node' THEN 1
				WHEN 'Container' THEN 1
				WHEN 'Image' THEN 1
				WHEN 'ReplicaSet' THEN 2
				WHEN 'Deployment' THEN 3
				WHEN 'StatefulSet' THEN 4
				WHEN 'DaemonSet' THEN 5
				WHEN 'Job' THEN 6
				WHEN 'CronJob' THEN 7
				WHEN 'Service' THEN 8
				WHEN 'ConfigMap' THEN 9
				ELSE 10
			END, name, kind
		`))

		if limit > 0 {
			mods = append(mods, qm.Limit(limit))
		}
		if offset > 0 {
			mods = append(mods, qm.Offset(offset))
		}
	}
	return mods, nil
}

func (s *Service) UpsertKubernetesEvents(ctx context.Context, cluster *models.ArgoCDCluster, events []*agentv1.KubernetesEvent) error {
	query := `
INSERT INTO argo_cd_cluster_k8s_event (
    id, organization_id, instance_id, cluster_id, "group", kind, namespace, name, info, timestamp, severity, reason, count, last_timestamp
)
(
%s
)
ON CONFLICT (id) DO UPDATE SET
    info = COALESCE(argo_cd_cluster_k8s_event.info, '{}'::jsonb) || COALESCE(EXCLUDED.info, '{}'::jsonb),
    last_timestamp = EXCLUDED.last_timestamp::timestamptz,
    "count" = EXCLUDED."count"::integer
`

	values := make([]any, 0, len(events)*eventColNum)
	placeholders := make([]string, 0, len(events))

	var instance *models.ArgoCDInstance
	if cluster.R != nil && cluster.R.Instance != nil {
		instance = cluster.R.Instance
	}
	if instance == nil {
		return status.Error(codes.Internal, "instance not found")
	}

	for i, event := range events {
		obj, err := buildArgoCDClusterK8SEvent(event, instance, cluster)
		if err != nil {
			return err
		}

		singleSelect, vals := s.buildSingleEventSelectorQuery(i, obj)
		placeholders = append(placeholders, singleSelect)
		values = append(values, vals...)
	}

	if len(values) == 0 {
		return nil
	}

	fullQuery := fmt.Sprintf(query, strings.Join(placeholders, "UNION ALL"))
	if _, err := s.db.ExecContext(ctx, fullQuery, values...); err != nil {
		return err
	}
	return eventsutil.NotifyChannel(ctx, s.db, eventsutil.TimelineEventsChannel, eventsutil.Event{
		ID:    instance.ID,
		Shard: instance.Shard,
		Type:  eventsutil.TypeModified,
	})
}

func (s *Service) buildSingleEventSelectorQuery(i int, obj *models.ArgoCDClusterK8SEvent) (string, []any) {
	offset := i*eventColNum + 1
	nums := make([]any, 0, eventColNum)
	for j := offset; j < offset+eventColNum; j++ {
		nums = append(nums, j)
	}
	return fmt.Sprintf(`
SELECT $%d AS id, $%d AS organization_id, $%d AS instance_id, $%d AS cluster_id, $%d AS "group", $%d AS kind, $%d AS namespace, $%d AS name, $%d::jsonb AS info, $%d::timestamptz AS timestamp, $%d AS severity, $%d AS reason, $%d::integer AS "count", $%d::timestamptz AS last_timestamp
`, nums...),
		[]any{
			obj.ID, obj.OrganizationID, obj.InstanceID, obj.ClusterID, obj.Group, obj.Kind, obj.Namespace, obj.Name, obj.Info, obj.Timestamp, obj.Severity, obj.Reason, obj.Count, obj.LastTimestamp.Time,
		}
}

func buildArgoCDClusterK8SEvent(event *agentv1.KubernetesEvent, instance *models.ArgoCDInstance, cluster *models.ArgoCDCluster) (*models.ArgoCDClusterK8SEvent, error) {
	ts, err := time.Parse(time.RFC3339, event.Timestamp)
	if err != nil {
		return nil, err
	}
	lts, err := time.Parse(time.RFC3339, event.LastTimestamp)
	if err != nil {
		return nil, err
	}

	infoData, err := json.Marshal(event.Info)
	if err != nil {
		return nil, err
	}
	return &models.ArgoCDClusterK8SEvent{
		ID:             event.Id,
		OrganizationID: instance.OrganizationOwner,
		InstanceID:     instance.ID,
		ClusterID:      cluster.ID,
		Group:          null.StringFrom(event.GetGroup()),
		Kind:           null.StringFrom(event.GetKind()),
		Namespace:      null.StringFrom(event.GetNamespace()),
		Name:           null.StringFrom(event.GetName()),
		Info:           null.JSONFrom(infoData),
		Timestamp:      ts,
		Severity:       null.StringFrom(event.GetSeverity()),
		Reason:         null.StringFrom(event.GetReason()),
		Count:          null.IntFrom(int(event.GetCount())),
		LastTimestamp:  null.TimeFrom(lts),
	}, nil
}

func ClusterK8SObjectToAPI(res *models.ArgoCDClusterK8SObject, deprecatedGVKs *DeprecatedGVKs, clusterK8sVersion string) (*organizationv1.ClusterResource, error) {
	var appInfo *agentv1.ArgoCDApplicationInfo
	if err := res.ArgocdApplicationInfo.Unmarshal(&appInfo); err != nil {
		return nil, err
	}
	columns := make(map[string]any)
	if err := res.Columns.Unmarshal(&columns); err != nil {
		return nil, err
	}
	for colName := range columns {
		if shouldBlock(colName) {
			delete(columns, colName)
		}
	}

	newColumns := translateColumnValues(res, columns)

	category, ok := gkToCategory[GroupKind{Group: res.Group.String, Kind: res.Kind.String}]
	if !ok {
		category = k8sv1.ResourceCategory_RESOURCE_CATEGORY_CUSTOM_RESOURCE
	}

	hasChildObject := res.GetChildrenCount() > 0
	item := &organizationv1.ClusterResource{
		InstanceId:            res.InstanceID,
		ClusterId:             res.ClusterID,
		Name:                  res.Name,
		Namespace:             res.Namespace.String,
		Group:                 res.Group.String,
		Version:               res.Version.String,
		Kind:                  res.Kind.String,
		ArgocdApplicationInfo: translateArgoAppInfo(appInfo, res),
		Columns:               newColumns,
		CreateTime:            res.CreationTimestamp.Format(time.RFC3339),
		OwnerId:               &res.OwnerID.String,
		Uid:                   res.ID,
		HasChildObjects:       &hasChildObject,
		Category:              &category,
	}

	if !res.DeletionTimestamp.IsZero() {
		deleteTime := res.DeletionTimestamp.Time.Format(time.RFC3339)
		item.DeleteTime = &deleteTime
	}
	apiInfo, ok := deprecatedGVKs.GetDeprecatedGVK(res.InstanceID, schema.GroupVersionKind{
		Group:   res.Group.String,
		Version: res.Version.String,
		Kind:    res.Kind.String,
	})
	if ok {
		item.DeprecatedInfo = buildDeprecatedInfo(apiInfo, clusterK8sVersion)
	}
	return item, nil
}

func translateArgoAppInfo(info *agentv1.ArgoCDApplicationInfo, obj *models.ArgoCDClusterK8SObject) *organizationv1.ArgoCDApplicationInfo {
	if info == nil {
		return nil
	}
	res := &organizationv1.ArgoCDApplicationInfo{
		Name: info.GetName(),
	}
	switch strings.ToLower(info.GetSyncStatus()) {
	case "unknown":
		res.SyncStatus = organizationv1.SyncStatus_SYNC_STATUS_UNKNOWN
	case "synced":
		res.SyncStatus = organizationv1.SyncStatus_SYNC_STATUS_SYNCED
	case "outofsync":
		res.SyncStatus = organizationv1.SyncStatus_SYNC_STATUS_OUT_OF_SYNC
	}
	switch strings.ToLower(info.GetHealthStatus()) {
	case "unknown":
		res.HealthStatus = organizationv1.HealthStatus_HEALTH_STATUS_UNKNOWN
	case "healthy":
		res.HealthStatus = organizationv1.HealthStatus_HEALTH_STATUS_HEALTHY
	case "degraded":
		res.HealthStatus = organizationv1.HealthStatus_HEALTH_STATUS_DEGRADED
	case "missing":
		res.HealthStatus = organizationv1.HealthStatus_HEALTH_STATUS_MISSING
	case "progressing":
		res.HealthStatus = organizationv1.HealthStatus_HEALTH_STATUS_PROGRESSING
	case "suspended":
		res.HealthStatus = organizationv1.HealthStatus_HEALTH_STATUS_SUSPENDED
	}

	if obj != nil && obj.R != nil && obj.R.Instance != nil {
		res.Link = buildArgoCDLink(obj.Group.String, obj.Kind.String, obj.Namespace.String, obj.Name, obj.R.Instance.StatusHostname.String, info.GetName())
	}
	return res
}

func buildArgoCDLink(group, kind, namespace, name, instanceHostName, argoAppName string) string {
	if instanceHostName == "" || argoAppName == "" {
		return ""
	}
	return fmt.Sprintf("https://%s/applications/argocd/%s?node=%s/%s/%s/%s",
		instanceHostName, argoAppName, group, kind, namespace, name)
}

func buildColumns(columns map[string]string, group, kind string) (map[string]any, error) {
	res := make(map[string]any)
	for k, v := range columns {
		if (strings.HasPrefix(k, "requests.") || strings.Contains(k, "limits.") ||
			strings.Contains(k, "usage.") || strings.Contains(k, "allocatable.")) && v != "" {
			q, err := k8sresource.ParseQuantity(v)
			if err != nil {
				return nil, err
			}
			res[k] = q.AsApproximateFloat64()
		} else {
			res[k] = v
		}
	}
	return res, nil
}

type whereCondition struct {
	field    string
	operator string
	value    string
}

// parseWhere parses the where string into a slice of where conditions
// The string value should follow SQL syntax: comma separated list of conditions. For example: "foo=bar,bar=baz".
func parseWhere(where string) ([]whereCondition, error) {
	if where == "" {
		return nil, nil
	}
	conditions := strings.Split(where, ",")
	operatorRegex := regexp.MustCompile(`^(.*?)(!=|=)(.*)$`)
	res := make([]whereCondition, 0, len(conditions))
	for _, c := range conditions {
		matches := operatorRegex.FindStringSubmatch(c)
		if len(matches) != 4 {
			return nil, status.Errorf(codes.InvalidArgument, "invalid where condition: %s", c)
		}
		res = append(res, whereCondition{
			field:    matches[1],
			operator: matches[2],
			value:    matches[3],
		})
	}
	return res, nil
}

func buildWhereQueryMods(where string) ([]qm.QueryMod, error) {
	conditions, err := parseWhere(where)
	if err != nil {
		return nil, err
	}
	mods := make([]qm.QueryMod, 0, len(conditions))

	for _, cond := range conditions {
		switch cond.operator {
		case "=":
			mods = append(mods, qm.Where("columns->>? = ?", cond.field, cond.value))
		case "!=":
			mods = append(mods, qm.Where("columns->>? != ?", cond.field, cond.value))
		}
	}
	return mods, nil
}
