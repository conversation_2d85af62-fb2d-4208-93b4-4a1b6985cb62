package instances

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"reflect"
	"sort"
	"strings"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/kustomize"
	"github.com/akuityio/akuity-platform/internal/utils/secret"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

const (
	orgClusterLimitError     = "organization has reached the maximum number of allowed clusters"
	licenseClusterLimitError = "product has reached the maximum number of allowed clusters as per license"
)

func ClustersWhereNameInMod(instanceID string, names ...string) ([]qm.QueryMod, error) {
	mods := []qm.QueryMod{models.ArgoCDClusterWhere.InstanceID.EQ(instanceID)}
	if len(names) == 0 || names[0] == accesscontrol.ResourceAny {
		return mods, nil
	}
	convertedNames := []interface{}{}
	for _, name := range names {
		_, id, clusterName, err := accesscontrol.ParseClusterResource(name)
		if err != nil {
			return nil, err
		}
		if id != instanceID {
			continue
		}
		if clusterName == accesscontrol.ResourceAny {
			return mods, nil
		}
		convertedNames = append(convertedNames, clusterName)
	}
	if len(convertedNames) != 0 {
		mods = append(mods, qm.WhereIn(fmt.Sprintf(`%s in ?`, models.ArgoCDClusterColumns.Name), convertedNames...))
	}
	return mods, nil
}

func (s *Service) GetInstanceClusters(ctx context.Context, instanceID string, mods ...qm.QueryMod) ([]*models.ArgoCDCluster, error) {
	mods = append(mods, models.ArgoCDClusterWhere.InstanceID.EQ(instanceID),
		qm.OrderBy(fmt.Sprintf("%s asc", models.ArgoCDClusterColumns.Name)))
	return s.ArgoCDClusters().Filter(mods...).ListAll(ctx, models.ArgoCDClusterColumnsWithoutManifest...)
}

func (s *Service) CountInstanceClusters(ctx context.Context, instanceID string, mods ...qm.QueryMod) (int64, error) {
	mods = append(mods, models.ArgoCDClusterWhere.InstanceID.EQ(instanceID))
	return s.ArgoCDClusters().Filter(mods...).Count(ctx)
}

func (s *Service) WatchClusters(
	ctx context.Context,
	existing []*models.ArgoCDCluster,
	subscribePredicate func(e events.ClusterEvent) bool,
	filterPredicate func(*models.ArgoCDCluster) bool,
) (<-chan database.Event[*models.ArgoCDCluster], error) {
	items := s.clustersWatcher.Subscribe(ctx, subscribePredicate)

	res := make(chan database.Event[*models.ArgoCDCluster])
	go func() {
		watchedIds := map[string]bool{}
		for _, cluster := range existing {
			res <- database.Event[*models.ArgoCDCluster]{Type: events.TypeAdded, Item: cluster}
			watchedIds[cluster.ID] = true
		}
		for e := range items {
			next := database.Event[*models.ArgoCDCluster]{Type: e.Type}
			if e.Type == events.TypeDeleted {
				next.Item = &models.ArgoCDCluster{ID: e.ID}
			} else {
				cluster, err := s.RepoSet.ArgoCDClusters(models.ArgoCDClusterNoStatusManifestMod).GetByID(ctx, e.ID)
				if err != nil {
					continue
				}
				next.Item = cluster
			}
			if ok := filterPredicate(next.Item); ok {
				watchedIds[next.Item.ID] = true
				res <- next
			} else if _, ok := watchedIds[next.Item.ID]; ok && e.Type == events.TypeDeleted {
				delete(watchedIds, next.Item.ID)
				res <- database.Event[*models.ArgoCDCluster]{Type: events.TypeDeleted, Item: next.Item}
			}
		}
		close(res)
	}()
	return res, nil
}

func (s *Service) GetClusterManifestByID(ctx context.Context, clusterID string, start, chunkSize int) (string, error) {
	query := fmt.Sprintf("SELECT SUBSTRING(COALESCE(%s, '') FROM %d FOR %d) FROM argo_cd_cluster WHERE %s=$1", models.ArgoCDClusterColumns.StatusManifests, start, chunkSize, models.ArgoCDClusterColumns.ID)
	row := s.db.QueryRowContext(ctx, query, clusterID)
	var chunk string
	if err := row.Scan(&chunk); err != nil {
		return "", err
	}

	return chunk, nil
}

func (s *Service) GetClusterByID(ctx context.Context, clusterID string) (*models.ArgoCDCluster, error) {
	cluster, err := s.ArgoCDClusters(models.ArgoCDClusterNoStatusManifestMod).GetByID(ctx, clusterID)
	if err != nil {
		return nil, err
	}
	return cluster, nil
}

func (s *Service) GetClusterByName(ctx context.Context, instanceID, clusterName string) (*models.ArgoCDCluster, error) {
	cluster, err := s.ArgoCDClusters(models.ArgoCDClusterNoStatusManifestMod).
		Filter(
			qm.Where(models.ArgoCDClusterColumns.InstanceID+"=?", instanceID),
			qm.And(models.ArgoCDClusterColumns.Name+"=?", clusterName)).
		One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return cluster, nil
}

func (s *Service) DeleteCluster(ctx context.Context, clusterID string) error {
	cluster, err := s.GetClusterByID(ctx, clusterID)
	if err != nil {
		return err
	}
	instance, err := s.GetInstanceByID(ctx, cluster.InstanceID)
	if err != nil {
		return err
	}
	spec, err := instance.GetSpec()
	if err != nil {
		return err
	}
	if spec.IsGitDelegateInManagedCluster() && spec.RepoServerDelegate.ManagedCluster.ClusterName == cluster.Name {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, "cluster selected as repo server delegate, please either select a new cluster to delegate or all available clusters.")
	}

	if spec.IsAppSetInManagedCluster() && spec.AppSetDelegate.ManagedCluster.ClusterName == cluster.Name {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, "cluster selected as app set delegate, please either select a new app set delegate or disable app set delegate.")
	}

	if spec.IsImageUpdaterInManagedCluster() && spec.ImageUpdaterDelegate.ManagedCluster.ClusterName == cluster.Name {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, "cluster selected as image updater delegate, please either select a new image updater delegate or disable image updater delegate.")
	}

	cluster.DeletionTimestamp = null.TimeFrom(time.Now())
	return s.ArgoCDClusters().Update(ctx, cluster, models.ArgoCDClusterColumns.DeletionTimestamp)
}

func (s *Service) validateDirectClusterSpec(ctx context.Context, spec *models.DirectClusterSpec) error {
	if spec == nil {
		return nil
	}
	if spec.Type.IsEmpty() {
		return errorsutil.NewAPIStatus(http.StatusBadRequest, "direct cluster type is invalid")
	}
	if spec.Type.IsKargo() {
		clusters := []*models.ArgoCDCluster{}
		mods := []qm.QueryMod{
			qm.Select("*"),
			qm.From("argo_cd_cluster"),
			qm.Where("spec->'directClusterSpec'->>'kargoInstanceID' = ?", spec.KargoInstanceID),
		}
		if err := models.NewQuery(mods...).Bind(ctx, s.db, &clusters); err != nil {
			return err
		}
		if len(clusters) > 0 {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "kargo instance is already registered with another argocd instance as a target cluster")
		}
		if _, err := s.RepoSet.KargoInstances(models.KargoInstanceWhere.ID.EQ(spec.KargoInstanceID), models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(s.organizationID))).One(ctx); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return errorsutil.NewAPIStatus(http.StatusForbidden, fmt.Sprintf("kargo instance %s not found", spec.KargoInstanceID))
			}
			return err
		}
	} else {
		// upbound cluster
		if spec.Upbound.Server == "" {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "upbound cluster server address is invalid")
		}

		if spec.Upbound.Organization == "" {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "upbound organization is invalid")
		}

		if spec.Upbound.Token == "" {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "upbound token is invalid")
		}

		if len(spec.Upbound.CAData) == 0 {
			return errorsutil.NewAPIStatus(http.StatusBadRequest, "upbound ca data is invalid")
		}

		// if not self hosted validate upbound server URL
		if !config.IsSelfHosted {
			upboundURL, err := url.Parse(spec.Upbound.Server)
			if err != nil {
				return errorsutil.NewAPIStatus(http.StatusBadRequest, "upbound cluster server address is malformed")
			}
			upboundURL.Scheme = "https" // force scheme to https for upbound
			if !strings.Contains(upboundURL.Host, "upbound.io") {
				return errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("upbound cluster server address %v doesn't appear to be an upbound.io URL", upboundURL.Host))
			}
			spec.Upbound.Server = upboundURL.String()
		}
	}
	return nil
}

func (s *Service) CreateCluster(
	ctx context.Context,
	instanceID string,
	clusterName string,
	namespace string,
	size models.ClusterSize,
	labels map[string]string,
	annotations map[string]string,
	description string,
	namespaceScoped bool,
	autoUpgradeDisabled bool,
	targetVersion string,
	kustomization kustomize.Kustomization,
	appReplication bool,
	redisTunneling bool,
	directClusterSpec *models.DirectClusterSpec,
	force bool,
	datadogAnnotationsEnabled bool,
	eksAddonEnabled bool,
	managedClusterConfig *models.ManagedClusterConfig,
	multiClusterK8SDashboardEnabled bool,
	autoscalerConfig *common.AutoScalerConfig,
	project string,
	compatibility models.ClusterCompatibility,
	argocdNotifications models.ClusterArgoCDNotificationsControllerSpec,
) (*models.ArgoCDCluster, error) {
	if err := validator.ValidateResourceName(clusterName, s.nameConfig.MinClusterNameLength, maxClusterNameLength); err != nil {
		return nil, err
	}
	if err := validator.ValidateResourceNamespace(namespace, 3, maxNamespaceNameLength); err != nil {
		return nil, err
	}
	if err := s.validateClusterName(clusterName); err != nil {
		return nil, err
	}
	if err := validator.ValidateLabelsAndAnnotations(labels, true); err != nil {
		return nil, err
	}
	if err := validator.ValidateLabelsAndAnnotations(annotations, false); err != nil {
		return nil, err
	}
	if err := validator.ValidateResourceDescription(description, 255); err != nil {
		return nil, err
	}
	if !force {
		if err := validator.ValidateTargetVersion(targetVersion); err != nil {
			return nil, err
		}
	}

	if err := s.validateClusterSize(size); err != nil {
		return nil, err
	}

	if directClusterSpec != nil {
		// validate direct cluster spec
		if err := s.validateDirectClusterSpec(ctx, directClusterSpec); err != nil {
			return nil, err
		}
	}

	if managedClusterConfig != nil && managedClusterConfig.SecretName == "" {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "secret name must be provided for managed cluster config")
	}

	if appReplication {
		if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetArgocdAgentStateReplication().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "argo cd agent state replication feature is not enabled")
		}
	}
	if multiClusterK8SDashboardEnabled {
		if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetMultiClusterK8SDashboard().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "multi-cluster k8s dashboard feature is not enabled")
		}
		instance, err := s.GetInstanceByID(ctx, instanceID)
		if err != nil {
			return nil, err
		}
		instanceSpec, err := instance.GetSpec()
		if err != nil {
			return nil, err
		}
		if !instanceSpec.MultiClusterK8sDashboardEnabled {
			return nil, errorsutil.NewAPIStatus(http.StatusPreconditionFailed, "Kubernetes Vision cannot be enabled for cluster if it is disabled for Argo CD instance. Please enable the feature for Argo CD instance")
		}
	}

	cnt, err := s.ArgoCDClusters().Filter(
		models.ArgoCDClusterWhere.Name.EQ(clusterName),
		models.ArgoCDClusterWhere.InstanceID.EQ(instanceID)).Count(ctx)
	if err != nil {
		return nil, err
	}
	if cnt > 0 {
		return nil, errorsutil.NewAPIStatus(http.StatusConflict, fmt.Sprintf("cluster %s already exists", clusterName))
	}

	if config.IsSelfHosted {
		licenseData := config.GetLicense()
		count, err := s.ArgoCDClusters().Count(ctx)
		if err != nil {
			return nil, err
		}
		if licenseData.Clusters > 0 && licenseData.Clusters < uint64(count)+1 {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, licenseClusterLimitError)
		}
	} else {
		summary, err := s.instancesSource.GetSummary(ctx)
		if err != nil {
			return nil, err
		}

		org, err := s.Organizations().GetByID(ctx, s.organizationID)
		if err != nil {
			return nil, err
		}

		quota, err := s.featSvc.GetOrgQuotas(ctx, s.organizationID)
		if err != nil {
			return nil, err
		}
		maxClusters := quota.MaxClusters
		if org.MaxClusters == 0 || org.MaxClusters > int(maxClusters) {
			maxClusters = int64(org.MaxClusters) // take the more permissive limit
		}
		if maxClusters > 0 && maxClusters < int64(summary.ClustersCount)+1 {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, orgClusterLimitError)
		}

		status, err := org.GetOrgStatus()
		if err != nil {
			return nil, err
		}
		// TODO: currently trial accounts are not monitored for expiry
		if status != nil && !status.Trial && status.ExpiryTime > 0 &&
			time.Now().After(time.Unix(status.ExpiryTime, 0).Add(s.gracePeriodDuration)) {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, OrgPlanExpiredError)
		}
	}

	password, err := database.RandomAlphabetString()
	if err != nil {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "failed to generate agent password")
	}

	cluster := &models.ArgoCDCluster{Name: clusterName, NamespaceScoped: namespaceScoped, InstanceID: instanceID, Namespace: namespace, AutoUpgradeDisabled: autoUpgradeDisabled, Description: null.StringFrom(description)}
	if err := cluster.SetSpec(models.ClusterSpec{
		Kustomization:                   kustomization,
		AgentRotationCount:              1,
		Size:                            size,
		SizeVersion:                     models.ClientSizeSchemaVersion,
		Labels:                          labels,
		Annotations:                     annotations,
		TargetVersion:                   targetVersion,
		AppReplication:                  appReplication,
		RedisTunneling:                  redisTunneling,
		DirectClusterSpec:               directClusterSpec,
		DatadogAnnotationsEnabled:       datadogAnnotationsEnabled,
		EKSAddonEnabled:                 eksAddonEnabled,
		ManagedClusterConfig:            managedClusterConfig,
		MultiClusterK8SDashboardEnabled: multiClusterK8SDashboardEnabled,
		AutoscalerConfig:                autoscalerConfig,
		Project:                         project,
		Compatibility:                   compatibility,
		ArgoCDNotifications:             argocdNotifications,
	}); err != nil {
		return nil, err
	}
	if err := cluster.SetPrivateSpec(models.ClusterPrivateSpec{AgentPassword: password}); err != nil {
		return nil, err
	}
	// make sure we don't retry forever
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()
	for {
		instanceClusters, err := s.ArgoCDClusters().Filter(models.ArgoCDClusterWhere.InstanceID.EQ(instanceID)).ListAll(ctx, models.ArgoCDClusterColumnsWithoutManifest...)
		if err != nil {
			return nil, err
		}
		sort.Slice(instanceClusters, func(i, j int) bool {
			return instanceClusters[i].SequenceID < instanceClusters[j].SequenceID
		})

		var seqId *int
		var prevId *int
		for _, cluster := range instanceClusters {
			if (prevId != nil && cluster.SequenceID > *prevId+1) || (prevId == nil && cluster.SequenceID > 0) {
				seqId = ptr.To(cluster.SequenceID - 1)
				break
			}
			prevId = &cluster.SequenceID
		}
		if seqId == nil {
			seqId = ptr.To(len(instanceClusters))
		}

		if *seqId > MaxClusterSequenceID {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "maximum number of clusters is exceeded")
		}
		cluster.SequenceID = *seqId
		if err := s.ArgoCDClusters().Create(ctx, cluster); err == nil {
			break
		} else if !errorsutil.IsUniqueViolation(err, "unique_cluster_seq_id") {
			return nil, err
		} else {
			duration := wait.Jitter(100*time.Millisecond, 0.2)
			s.log.Info(fmt.Sprintf("Retrying cluster creation in %v", duration), "clusterName", clusterName)
			time.Sleep(duration)
		}
	}

	return s.ArgoCDClusters(models.ArgoCDClusterNoStatusManifestMod).GetByID(ctx, cluster.ID)
}

func (s *Service) UpdateCluster(ctx context.Context,
	clusterID string,
	clusterDescription string,
	namespace string,
	namespaceScoped bool,
	autoUpgradeDisabled bool,
	force bool,
	spec models.ClusterSpec,
) (*models.ArgoCDCluster, error) {
	if err := validator.ValidateResourceNamespace(namespace, 3, maxNamespaceNameLength); err != nil {
		return nil, err
	}
	if err := validator.ValidateLabelsAndAnnotations(spec.Labels, true); err != nil {
		return nil, err
	}
	if err := validator.ValidateLabelsAndAnnotations(spec.Annotations, false); err != nil {
		return nil, err
	}
	if err := s.validateClusterSize(spec.Size); err != nil {
		return nil, err
	}
	if err := validator.ValidateResourceDescription(clusterDescription, 255); err != nil {
		return nil, err
	}

	if spec.ManagedClusterConfig != nil && spec.ManagedClusterConfig.SecretName == "" {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "secret name must be provided for managed cluster config")
	}

	cluster, err := s.ArgoCDClusters(models.ArgoCDClusterNoStatusManifestMod).GetByID(ctx, clusterID)
	if err != nil {
		return nil, err
	}

	currentSpec, err := cluster.GetSpec()
	if err != nil {
		return nil, err
	}

	if spec.AppReplication && spec.AppReplication != currentSpec.AppReplication {
		if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetArgocdAgentStateReplication().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "argo cd agent state replication feature is not enabled")
		}
	}

	if spec.MultiClusterK8SDashboardEnabled && spec.MultiClusterK8SDashboardEnabled != currentSpec.MultiClusterK8SDashboardEnabled {
		if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetMultiClusterK8SDashboard().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "multi-cluster k8s dashboard feature is not enabled")
		}
	}

	if currentSpec.MultiClusterK8SDashboardEnabled && !spec.MultiClusterK8SDashboardEnabled {
		if err = s.ArgoCDClusterK8sObjects(models.ArgoCDClusterK8SObjectWhere.ClusterID.EQ(clusterID)).DeleteAll(ctx); err != nil {
			return nil, err
		}
		cluster.StatusK8SInfo = null.JSONFrom(nil)
		if err = s.ArgoCDClusters().Update(ctx, cluster, models.ArgoCDClusterColumns.StatusK8SInfo); err != nil {
			return nil, err
		}
	}
	if !currentSpec.MultiClusterK8SDashboardEnabled && spec.MultiClusterK8SDashboardEnabled {
		instance, err := s.GetInstanceByID(ctx, cluster.InstanceID)
		if err != nil {
			return nil, err
		}
		instanceSpec, err := instance.GetSpec()
		if err != nil {
			return nil, err
		}
		if !instanceSpec.MultiClusterK8sDashboardEnabled {
			return nil, errorsutil.NewAPIStatus(http.StatusPreconditionFailed, "Kubernetes Vision cannot be enabled for cluster if it is disabled for Argo CD instance. Please enable the feature for Argo CD instance")
		}
	}

	if !reflect.DeepEqual(spec.DirectClusterSpec, currentSpec.DirectClusterSpec) {
		return nil, errorsutil.NewAPIStatus(http.StatusPreconditionFailed, "modifying direct cluster spec is not allowed.")
	}

	// unmodifiable fields
	spec.AgentRotationCount = currentSpec.AgentRotationCount
	spec.DirectClusterSpec = currentSpec.DirectClusterSpec

	if currentSpec.DirectClusterSpec != nil && !currentSpec.DirectClusterSpec.Type.IsEmpty() {
		// auto update the agent version to latest for direct cluster
		spec.TargetVersion = version.GetLatestAgentVersion()
	} else {
		// if version is not set use existing version or latest if no existing version is present
		if spec.TargetVersion == "" {
			if currentSpec.TargetVersion != "" {
				spec.TargetVersion = currentSpec.TargetVersion
			} else {
				spec.TargetVersion = version.GetLatestAgentVersion()
			}
		}
	}

	// validate that the version selected is compatible with the current platform
	if !force {
		if err := validator.ValidateTargetVersion(spec.TargetVersion); err != nil {
			return nil, err
		}
	}

	spec.SizeVersion = models.ClientSizeSchemaVersion

	if !spec.Equals(*currentSpec) ||
		cluster.Description.String != clusterDescription ||
		cluster.Namespace != namespace ||
		cluster.NamespaceScoped != namespaceScoped ||
		cluster.AutoUpgradeDisabled != autoUpgradeDisabled {

		if err := cluster.SetSpec(spec); err != nil {
			return nil, err
		}
		cluster.Namespace = namespace
		cluster.NamespaceScoped = namespaceScoped
		cluster.Description = null.StringFrom(clusterDescription)
		cluster.AutoUpgradeDisabled = autoUpgradeDisabled
		return cluster, s.ArgoCDClusters().Update(ctx, cluster, models.ArgoCDClusterColumnsWithoutManifest...)
	}
	return cluster, nil
}

func (s *Service) BatchRotateClusterCredentials(ctx context.Context, mods ...qm.QueryMod) ([]string, error) {
	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	tx, err := s.txBeginner.Begin(txCtx)
	if err != nil {
		return nil, err
	}

	clusters, err := s.ArgoCDClusters().Filter(mods...).ListAll(txCtx, "id", "name", "spec", "private_spec", "status_observed_cred_rotation")
	if err != nil {
		return nil, err
	}

	skipped := []string{}
	for _, cluster := range clusters {
		spec, err := cluster.GetSpec()
		if err != nil {
			return nil, err
		}
		status, err := cluster.GetStatus()
		if err != nil {
			return nil, err
		}
		// if observed rotation count doesn't match required rotation count skip cluster
		if status.ObservedRotationCount.Int != spec.AgentRotationCount {
			skipped = append(skipped, cluster.Name)
			continue
		}
		spec.AgentRotationCount++

		privateSpec, err := cluster.GetPrivateSpec()
		if err != nil {
			return nil, err
		}

		newPass, err := secret.GenerateNewPassword(privateSpec.AgentPassword, 5)
		if err != nil {
			return nil, err
		}
		privateSpec.AgentPassword = newPass
		if err := cluster.SetSpec(*spec); err != nil {
			return nil, err
		}
		if err := cluster.SetPrivateSpec(*privateSpec); err != nil {
			return nil, err
		}

		if err := s.ArgoCDClusters().Update(ctx, cluster, "spec", "private_spec"); err != nil {
			return nil, err
		}
	}
	if err := tx.Commit(); err != nil {
		return nil, err
	}
	return skipped, nil
}

func (s *Service) BatchUpdateClustersVersion(
	ctx context.Context,
	// empty to update to latest
	newVersion string,
	mods ...qm.QueryMod,
) error {
	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	tx, err := s.txBeginner.Begin(txCtx)
	if err != nil {
		return err
	}

	clusters, err := s.ArgoCDClusters().Filter(mods...).ListAll(txCtx, "id", "name", "spec")
	if err != nil {
		return err
	}

	targetVersion := version.GetLatestAgentVersion()

	if newVersion != "" {
		if err := validator.ValidateTargetVersion(newVersion); err != nil {
			return err
		}

		targetVersion = newVersion
	}

	for _, cluster := range clusters {
		spec, err := cluster.GetSpec()
		if err != nil {
			return err
		}
		spec.TargetVersion = targetVersion

		if err := cluster.SetSpec(*spec); err != nil {
			return err
		}

		if err := s.ArgoCDClusters().Update(ctx, cluster, "spec"); err != nil {
			return err
		}
	}
	if err := tx.Commit(); err != nil {
		return err
	}
	return nil
}

func (s *Service) BatchUpdateClusters(ctx context.Context,
	instanceID string,
	customization *argocdv1.ClusterCustomization,
	multiClusterK8SDashboardEnabled *bool,
) ([]string, error) {
	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := s.txBeginner.Begin(txCtx)
	if err != nil {
		return nil, err
	}

	// load only cluster customization and id fields
	clusters, err := s.ArgoCDClusters().Filter(models.ArgoCDClusterWhere.InstanceID.EQ(instanceID)).ListAll(ctx, "id", "name", "spec", "auto_upgrade_disabled")
	if err != nil {
		return nil, err
	}

	skipped := []string{}
	for _, cluster := range clusters {
		spec, err := cluster.GetSpec()
		if err != nil {
			return nil, err
		}

		if spec.AppReplication && customization != nil && customization.AppReplication != spec.AppReplication {
			if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetArgocdAgentStateReplication().Enabled() {
				return nil, status.Error(codes.PermissionDenied, "argo cd agent state replication feature is not enabled")
			}
		}

		if multiClusterK8SDashboardEnabled != nil && *multiClusterK8SDashboardEnabled != spec.MultiClusterK8SDashboardEnabled {
			if !s.featSvc.GetFeatureStatuses(ctx, &s.organizationID).GetMultiClusterK8SDashboard().Enabled() {
				return nil, status.Error(codes.PermissionDenied, "multi-cluster k8s dashboard feature is not enabled")
			}
		}

		if spec.TargetVersion == "" {
			spec.TargetVersion = version.GetLatestAgentVersion()
		} else if err := validator.ValidateTargetVersion(spec.TargetVersion); err != nil {
			return nil, status.Error(codes.PermissionDenied, fmt.Sprintf("cluster %v is running an unsupported agent version %v, please update version before proceeding", cluster.Name, spec.TargetVersion))
		}
		updatedColumnsMap := map[string]bool{}
		if customization != nil {
			spec.RedisTunneling = customization.GetRedisTunneling()
			spec.Kustomization = customization.GetKustomization().AsMap()
			if err := cluster.SetSpec(*spec); err != nil {
				return nil, err
			}
			updatedColumnsMap["spec"] = true
			cluster.AutoUpgradeDisabled = customization.GetAutoUpgradeDisabled()
			if spec.Size == models.ClusterSizeAuto && cluster.AutoUpgradeDisabled {
				skipped = append(skipped, cluster.Name)
				continue
			}
			updatedColumnsMap["auto_upgrade_disabled"] = true
		}
		if multiClusterK8SDashboardEnabled != nil {
			spec.MultiClusterK8SDashboardEnabled = *multiClusterK8SDashboardEnabled
			if err := cluster.SetSpec(*spec); err != nil {
				return nil, err
			}
			updatedColumnsMap["spec"] = true
		}

		if len(updatedColumnsMap) > 0 {
			updatedColumns := make([]string, 0, len(updatedColumnsMap))
			for k := range updatedColumnsMap {
				updatedColumns = append(updatedColumns, k)
			}
			if err := s.ArgoCDClusters().Update(ctx, cluster, updatedColumns...); err != nil {
				return nil, err
			}
		}
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return skipped, nil
}
