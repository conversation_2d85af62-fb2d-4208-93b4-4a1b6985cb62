package functions

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/samber/lo"

	"github.com/akuityio/akuity-platform/internal/services/ai/util"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/models/models"
)

func (fc *Controller) addRunbookFunctions() error {
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name: "get-runbooks",
			Description: `Retrieves runbooks by their names for execution and testing.
			Returns complete runbook content with General, Symptoms, Root cause, and Solution sections.`,
		},
		DisplayName: "Get Runbooks",
	}, fc.getRunbooks); err != nil {
		return err
	}

	return nil
}

func (fc *Controller) getRunbooks(ctx context.Context, args struct {
	Runbooks []models.ConversationRunbook `json:"runbooks" description:"Array of runbook instance ids and names to retrieve" required:"true"`
},
) (string, error) {
	// Get conversation to extract instance ID
	conv := GetConversation(ctx)
	if conv == nil {
		return "", fmt.Errorf("context has no conversation")
	}

	if len(args.Runbooks) == 0 {
		return "", &util.InvalidArgsError{Message: "At least one runbook is required"}
	}

	instanceRunbooks := make(map[string][]models.Runbook)
	instanceSpecs := make(map[string]models.InstanceConfigSpec)

	// First loop: collect unique instance IDs and load their specs
	uniqueInstanceIDs := make(map[string]struct{})
	for _, runbook := range args.Runbooks {
		uniqueInstanceIDs[runbook.InstanceID] = struct{}{}
	}

	for instanceID := range uniqueInstanceIDs {
		// Get the instance configuration directly from the repo
		instanceConfig, err := fc.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
		if err != nil {
			return "", fmt.Errorf("failed to get instance configuration: %w", err)
		}

		spec, err := instanceConfig.GetSpec()
		if err != nil {
			return "", fmt.Errorf("failed to get instance spec: %w", err)
		}

		instanceSpecs[instanceID] = spec
	}

	// Second loop: filter runbooks by name for each requested runbook
	for _, runbook := range args.Runbooks {
		spec := instanceSpecs[runbook.InstanceID]
		foundRunbooks := lo.Filter(spec.KubeVisionConfig.AIConfig.Runbooks, func(item models.Runbook, index int) bool {
			return item.Name == runbook.Name
		})
		instanceRunbooks[runbook.InstanceID] = append(instanceRunbooks[runbook.InstanceID], foundRunbooks...)
	}

	if len(instanceRunbooks) == 0 {
		return "No runbooks found with the specified names", nil
	}

	runbookMsg, err := json.Marshal(instanceRunbooks)
	if err != nil {
		return "", fmt.Errorf("failed to marshal runbooks: %w", err)
	}

	return fmt.Sprintf(`The following runbooks are available for execution (key is instance ID, value is array of runbooks):
	Runbooks:
	%s

	Each runbook contains:
	- **General**: Processing principles and workflow guidelines
	- **Symptoms**: Observable issues that indicate this runbook applies
	- **Root cause**: Underlying causes to confirm diagnosis
	- **Solution**: Step-by-step remediation actions

	Match Symptoms against current issues to select the most relevant runbook.`, string(runbookMsg)), nil
}
