package functions

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/akuityio/akuity-platform/internal/services/ai/util"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/models/models"
)

func (fc *Controller) addRunbookFunctions() error {
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name: "get-runbooks",
			Description: `Retrieves runbooks by their names for execution and testing.
			Returns complete runbook content with General, Symptoms, Root cause, and Solution sections.`,
		},
		DisplayName: "Get Runbooks",
	}, fc.getRunbooks); err != nil {
		return err
	}

	return nil
}

func (fc *Controller) getRunbooks(ctx context.Context, args struct {
	Runbooks []models.ConversationRunbook `json:"runbooks" description:"Array of runbook instance ids and names to retrieve" required:"true"`
},
) (string, error) {
	// Get conversation to extract instance ID
	conv := GetConversation(ctx)
	if conv == nil {
		return "", fmt.Errorf("context has no conversation")
	}

	if len(args.Runbooks) == 0 {
		return "", &util.InvalidArgsError{Message: "At least one runbook is required"}
	}

	instanceRunbooks := make(map[string][]models.Runbook)

	// First loop: collect unique instance IDs, load specs, and build runbook lookup map
	uniqueInstanceIDs := make(map[string]struct{})
	for _, runbook := range args.Runbooks {
		uniqueInstanceIDs[runbook.InstanceID] = struct{}{}
	}

	runbookLookup := make(map[string]map[string]models.Runbook)
	for instanceID := range uniqueInstanceIDs {
		// Get the instance configuration directly from the repo
		instanceConfig, err := fc.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
		if err != nil {
			return "", fmt.Errorf("failed to get instance configuration: %w", err)
		}

		spec, err := instanceConfig.GetSpec()
		if err != nil {
			return "", fmt.Errorf("failed to get instance spec: %w", err)
		}

		// Build runbook lookup map for this instance
		runbookLookup[instanceID] = make(map[string]models.Runbook)
		for _, runbook := range spec.KubeVisionConfig.AIConfig.Runbooks {
			runbookLookup[instanceID][runbook.Name] = runbook
		}
	}

	// Second loop: lookup requested runbooks directly from the map
	for _, runbook := range args.Runbooks {
		if instanceMap, exists := runbookLookup[runbook.InstanceID]; exists {
			if foundRunbook, exists := instanceMap[runbook.Name]; exists {
				instanceRunbooks[runbook.InstanceID] = append(instanceRunbooks[runbook.InstanceID], foundRunbook)
			}
		}
	}

	if len(instanceRunbooks) == 0 {
		return "No runbooks found with the specified names", nil
	}

	runbookMsg, err := json.Marshal(instanceRunbooks)
	if err != nil {
		return "", fmt.Errorf("failed to marshal runbooks: %w", err)
	}

	return fmt.Sprintf(`The following runbooks are available for execution (key is instance ID, value is array of runbooks):
	Runbooks:
	%s

	Each runbook contains:
	- **General**: Processing principles and workflow guidelines
	- **Symptoms**: Observable issues that indicate this runbook applies
	- **Root cause**: Underlying causes to confirm diagnosis
	- **Solution**: Step-by-step remediation actions

	Match Symptoms against current issues to select the most relevant runbook.`, string(runbookMsg)), nil
}
