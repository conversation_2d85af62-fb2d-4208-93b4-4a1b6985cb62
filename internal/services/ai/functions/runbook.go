package functions

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/samber/lo"

	"github.com/akuityio/akuity-platform/internal/services/ai/util"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/models/models"
)

func (fc *Controller) addRunbookFunctions() error {
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name: "get-runbooks",
			Description: `Retrieves runbooks by their names for execution and testing.
			Returns complete runbook content with General, Symptoms, Root cause, and Solution sections.`,
		},
		DisplayName: "Get Runbooks",
	}, fc.getRunbooks); err != nil {
		return err
	}

	return nil
}

func (fc *Controller) getRunbooks(ctx context.Context, args struct {
	Runbooks []models.ConversationRunbook `json:"runbooks" description:"Array of runbook instance ids and names to retrieve" required:"true"`
},
) (string, error) {
	// Get conversation to extract instance ID
	conv := GetConversation(ctx)
	if conv == nil {
		return "", fmt.Errorf("context has no conversation")
	}

	if len(args.Runbooks) == 0 {
		return "", &util.InvalidArgsError{Message: "At least one runbook is required"}
	}

	// Group runbooks by instance ID to avoid duplicate instance config fetches
	runbooksByInstance := lo.GroupBy(args.Runbooks, func(rb models.ConversationRunbook) string {
		return rb.InstanceID
	})

	instanceRunbooks := make(map[string][]models.Runbook)

	// Process each instance once
	for instanceID, requestedRunbooks := range runbooksByInstance {
		// Get the instance configuration once per instance
		instanceConfig, err := fc.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
		if err != nil {
			return "", fmt.Errorf("failed to get instance configuration: %w", err)
		}

		spec, err := instanceConfig.GetSpec()
		if err != nil {
			return "", fmt.Errorf("failed to get instance spec: %w", err)
		}

		// Extract requested runbook names for this instance
		requestedNames := lo.Map(requestedRunbooks, func(rb models.ConversationRunbook, _ int) string {
			return rb.Name
		})

		// Filter runbooks by requested names in one pass
		foundRunbooks := lo.Filter(spec.KubeVisionConfig.AIConfig.Runbooks, func(item models.Runbook, _ int) bool {
			return lo.Contains(requestedNames, item.Name)
		})

		if len(foundRunbooks) > 0 {
			instanceRunbooks[instanceID] = foundRunbooks
		}
	}

	if len(instanceRunbooks) == 0 {
		return "No runbooks found with the specified names", nil
	}

	runbookMsg, err := json.Marshal(instanceRunbooks)
	if err != nil {
		return "", fmt.Errorf("failed to marshal runbooks: %w", err)
	}

	return fmt.Sprintf(`The following runbooks are available for execution (key is instance ID, value is array of runbooks):
	Runbooks:
	%s

	Each runbook contains:
	- **General**: Processing principles and workflow guidelines
	- **Symptoms**: Observable issues that indicate this runbook applies
	- **Root cause**: Underlying causes to confirm diagnosis
	- **Solution**: Step-by-step remediation actions

	Match Symptoms against current issues to select the most relevant runbook.`, string(runbookMsg)), nil
}
