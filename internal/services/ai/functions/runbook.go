package functions

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/samber/lo"

	"github.com/akuityio/akuity-platform/internal/services/ai/util"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/models/models"
)

func (fc *Controller) addRunbookFunctions() error {
	if err := addFunction(fc, Function{
		Tool: ai.Tool{
			Name:        "get-runbooks",
			Description: `Retrieves runbooks by their names for execution and testing. Returns complete runbook content with General, Symptoms, Root cause, and Solution sections.`,
		},
		DisplayName: "Get Runbooks",
	}, fc.getRunbooks); err != nil {
		return err
	}

	return nil
}

func (fc *Controller) getRunbooks(ctx context.Context, args struct {
	Runbooks []models.ConversationRunbook `json:"runbooks" description:"Array of runbook instance ids and names to retrieve" required:"true"`
},
) (string, error) {
	// Get conversation to extract instance ID
	conv := GetConversation(ctx)
	if conv == nil {
		return "", fmt.Errorf("context has no conversation")
	}

	if len(args.Runbooks) == 0 {
		return "", &util.InvalidArgsError{Message: "At least one runbook is required"}
	}

	instanceRunbooks := make(map[string][]models.Runbook)
	instanceAllRunbooks := make(map[string]map[string]models.Runbook)
	for _, runbook := range args.Runbooks {
		instanceID := runbook.InstanceID
		runbookName := runbook.Name

		if _, ok := instanceAllRunbooks[instanceID]; !ok {
			// Get the instance configuration directly from the repo
			instanceConfig, err := fc.RepoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
			if err != nil {
				return "", fmt.Errorf("failed to get instance configuration: %w", err)
			}

			spec, err := instanceConfig.GetSpec()
			if err != nil {
				return "", fmt.Errorf("failed to get instance spec: %w", err)
			}

			instanceAllRunbooks[instanceID] = lo.SliceToMap(spec.KubeVisionConfig.AIConfig.Runbooks, func(item models.Runbook) (string, models.Runbook) {
				return item.Name, item
			})
		}

		allRunbooks := instanceAllRunbooks[instanceID]
		if runbook, ok := allRunbooks[runbookName]; ok {
			instanceRunbooks[instanceID] = append(instanceRunbooks[instanceID], runbook)
		}
	}

	if len(instanceRunbooks) == 0 {
		return "No runbooks found with the specified names", nil
	}

	runbookMsg, err := json.Marshal(instanceRunbooks)
	if err != nil {
		return "", fmt.Errorf("failed to marshal runbooks: %w", err)
	}

	return fmt.Sprintf(`The following runbooks are available for execution (key is instance ID, value is array of runbooks):
	Runbooks:
	%s

	Match Symptoms against current issues to select the most relevant runbook.`, string(runbookMsg)), nil
}
