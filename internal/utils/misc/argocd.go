package misc

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/semver"
	"github.com/go-logr/logr"
	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gopkg.in/yaml.v2"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
	statusmodel "github.com/akuityio/akuity-platform/models/util/status"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	healthv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/health/v1"
	reconciliationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/reconciliation/v1"
)

const (
	argoprojGroup                             = "argoproj.io"
	argoprojVersion                           = "v1alpha1"
	userProvidedArgoCDVersionsVolumeMountPath = "/etc/argocd-versions/versions"
)

var (
	ApplicationGVR    = schema.GroupVersionResource{Group: argoprojGroup, Version: argoprojVersion, Resource: "applications"}
	ApplicationsetGVR = schema.GroupVersionResource{Group: argoprojGroup, Version: argoprojVersion, Resource: "applicationsets"}
	AppprojectsGVR    = schema.GroupVersionResource{Group: argoprojGroup, Version: argoprojVersion, Resource: "appprojects"}
)

func NewImageUpdaterDelegate(spec *models.ImageUpdaterDelegate) *argocdv1.ImageUpdaterDelegate {
	var imageUpdaterDelegate *argocdv1.ImageUpdaterDelegate
	if spec != nil {
		imageUpdaterDelegate = &argocdv1.ImageUpdaterDelegate{}
		if spec.ControlPlane != nil {
			imageUpdaterDelegate.ControlPlane = true
		} else if spec.ManagedCluster != nil {
			imageUpdaterDelegate.ManagedCluster = &argocdv1.ManagedCluster{ClusterName: spec.ManagedCluster.ClusterName}
		}
	}
	return imageUpdaterDelegate
}

func NewAppSetDelegate(spec *models.AppSetDelegate) *argocdv1.AppSetDelegate {
	var appSetDelegate *argocdv1.AppSetDelegate
	if spec != nil {
		appSetDelegate = &argocdv1.AppSetDelegate{}
		if spec.ManagedCluster != nil {
			appSetDelegate.ManagedCluster = &argocdv1.ManagedCluster{ClusterName: spec.ManagedCluster.ClusterName}
		}
	}
	return appSetDelegate
}

func NewRepoServerDelegate(spec *models.RepoServerDelegate) *argocdv1.RepoServerDelegate {
	var delegate *argocdv1.RepoServerDelegate
	if spec != nil {
		delegate = &argocdv1.RepoServerDelegate{}
		if spec.ControlPlane != nil {
			delegate.ControlPlane = true
		} else if spec.ManagedCluster != nil {
			delegate.ManagedCluster = &argocdv1.ManagedCluster{ClusterName: spec.ManagedCluster.ClusterName}
		}
	}
	return delegate
}

func NewKubeVisionConfig(spec models.KubeVisionConfig) *argocdv1.KubeVisionConfig {
	var runbooks []*argocdv1.Runbook
	for _, runbook := range spec.AIConfig.Runbooks {
		runbooks = append(runbooks, &argocdv1.Runbook{
			Name:    runbook.Name,
			Content: runbook.Content,
			AppliedTo: &argocdv1.TargetSelector{
				ArgocdApplications: runbook.AppliedTo.ArgoCDApplications,
				K8SNamespaces:      runbook.AppliedTo.K8SNamespaces,
				Clusters:           runbook.AppliedTo.Clusters,
			},
		})
	}
	return &argocdv1.KubeVisionConfig{
		CveScanConfig: &argocdv1.CveScanConfig{
			ScanEnabled:    spec.CveScanConfig.ScanEnabled,
			RescanInterval: spec.CveScanConfig.RescanInterval,
		},
		AiConfig: &argocdv1.AIConfig{
			Runbooks: runbooks,
			Incidents: &argocdv1.IncidentsConfig{
				Triggers: lo.Map(spec.AIConfig.Incidents.Triggers, func(item models.TargetSelector, index int) *argocdv1.TargetSelector {
					return &argocdv1.TargetSelector{
						ArgocdApplications: item.ArgoCDApplications,
						K8SNamespaces:      item.K8SNamespaces,
						Clusters:           item.Clusters,
					}
				}),
				Webhooks: lo.Map(spec.AIConfig.Incidents.Webhooks, func(item models.IncidentWebhookConfig, index int) *argocdv1.IncidentWebhookConfig {
					return &argocdv1.IncidentWebhookConfig{
						Name:                      item.Name,
						K8SNamespacePath:          item.K8SNamespacePath,
						DescriptionPath:           item.DescriptionPath,
						ArgocdApplicationNamePath: item.ArgoCDApplicationNamePath,
						ClusterPath:               item.ClusterPath,
					}
				}),
			},
		},
	}
}

func NewApplicationSetExtension(spec models.ApplicationSetExtension) *argocdv1.ApplicationSetExtension {
	return &argocdv1.ApplicationSetExtension{
		Enabled: spec.Enabled,
	}
}

func NewAppReconciliationsRateLimiting(spec *models.AppReconciliationsRateLimiting) *argocdv1.AppReconciliationsRateLimiting {
	bucketDefaultValues := models.NewBucketRateLimitingWithDefaults()
	itemDefaultValues := models.NewItemRateLimitingWithDefaults()
	itemRateLimiting := &argocdv1.ItemRateLimiting{
		BackoffFactor:   float32(itemDefaultValues.BackoffFactor),
		FailureCooldown: uint32(itemDefaultValues.FailureCooldown.Milliseconds()),
		BaseDelay:       uint32(itemDefaultValues.BaseDelay.Milliseconds()),
		MaxDelay:        uint32(itemDefaultValues.MaxDelay.Milliseconds()),
	}
	bucketRateLimiting := &argocdv1.BucketRateLimiting{
		BucketQps:  bucketDefaultValues.BucketQps,
		BucketSize: bucketDefaultValues.BucketSize,
	}
	if spec != nil {
		if spec.BucketRateLimiting.Enabled {
			bucketRateLimiting.Enabled = spec.BucketRateLimiting.Enabled
			bucketRateLimiting.BucketSize = spec.BucketRateLimiting.BucketSize
			bucketRateLimiting.BucketQps = spec.BucketRateLimiting.BucketQps
		}
		if spec.ItemRateLimiting.Enabled {
			itemRateLimiting.Enabled = spec.ItemRateLimiting.Enabled
			itemRateLimiting.FailureCooldown = uint32(spec.ItemRateLimiting.FailureCooldown.Milliseconds())
			itemRateLimiting.BaseDelay = uint32(spec.ItemRateLimiting.BaseDelay.Milliseconds())
			itemRateLimiting.MaxDelay = uint32(spec.ItemRateLimiting.MaxDelay.Milliseconds())
			itemRateLimiting.BackoffFactor = float32(spec.ItemRateLimiting.BackoffFactor)
		}
	}
	return &argocdv1.AppReconciliationsRateLimiting{
		BucketRateLimiting: bucketRateLimiting,
		ItemRateLimiting:   itemRateLimiting,
	}
}

func processSecret(instance instances.ArgoCDInstance) (map[string]string, error) {
	argoCDSecret, err := instance.GetArgocdSecret()
	if err != nil {
		return nil, err
	}
	for k := range argoCDSecret {
		if instances.IsManagedArgoCDSecretKey(k) {
			delete(argoCDSecret, k)
		} else {
			argoCDSecret[k] = ""
		}
	}
	return argoCDSecret, nil
}

func NewArgoCDInstanceV1(
	instance instances.ArgoCDInstance,
	instanceProgressingDeadline time.Duration,
	unsupportedVersion bool,
) (*argocdv1.Instance, error) {
	spec, err := instance.GetSpec()
	if err != nil {
		return nil, err
	}
	modelsConfigMap, err := instance.GetArgoCDConfigMap()
	if err != nil {
		return nil, err
	}
	configMap := NewArgoCDConfigMapV1(modelsConfigMap)
	modelsRbacConfigMap, err := instance.GetArgoCDRbacConfigMap()
	if err != nil {
		return nil, err
	}
	rbacConfigMap := NewArgoCDRBACConfigMapV1(modelsRbacConfigMap)

	ipAllowList, _ := types.MapSlice(spec.IpAllowlist, func(in models.IpAllowlistEntry) (*argocdv1.IPAllowListEntry, error) {
		return &argocdv1.IPAllowListEntry{
			Ip:          in.Ip,
			Description: in.Description,
		}, nil
	})
	hostAliases, _ := types.MapSlice(spec.HostAliases, func(in models.HostAliases) (*argocdv1.HostAliases, error) {
		return &argocdv1.HostAliases{
			Ip:        in.Ip,
			Hostnames: in.Hostnames,
		}, nil
	})
	extensions, _ := types.MapSlice(spec.Extensions, func(in models.ArgoCDExtensionInstallEntry) (*argocdv1.ArgoCDExtensionInstallEntry, error) {
		return &argocdv1.ArgoCDExtensionInstallEntry{
			Id:      in.ID,
			Version: in.Version,
		}, nil
	})
	instanceStatus, err := instance.GetStatus()
	if err != nil {
		return nil, err
	}
	if instanceStatus.Health.Code == statusmodel.HealthStatusCodeUnknown {
		instanceStatus.Health.Message = "failed to get tenant status"
	}
	reconciliationStatus, err := instance.GetReconciliationStatus(instanceProgressingDeadline)
	if err != nil {
		return nil, err
	}

	argoCDSecret, err := processSecret(instance)
	if err != nil {
		return nil, err
	}

	var deleteTime *timestamppb.Timestamp
	if !instance.DeletionTimestamp.IsZero() {
		deleteTime = timestamppb.New(instance.DeletionTimestamp.Time)
	}

	delegate := NewRepoServerDelegate(spec.RepoServerDelegate)

	imageUpdaterDelegate := NewImageUpdaterDelegate(spec.ImageUpdaterDelegate)
	appSetDelegate := NewAppSetDelegate(spec.AppSetDelegate)

	kustomization, err := structpb.NewStruct(spec.ClusterCustomizationDefaults.Kustomization)
	if err != nil {
		return nil, err
	}

	processedEventInfo, err := instance.GetRecentProcessedEventInfo()
	if err != nil {
		return nil, err
	}

	agentPermissionsRules, _ := types.MapSlice(spec.AgentPermissionsRules, func(in models.AgentPermissionsRule) (*argocdv1.AgentPermissionsRule, error) {
		return &argocdv1.AgentPermissionsRule{
			ApiGroups: in.APIGroups,
			Resources: in.Resources,
			Verbs:     in.Verbs,
		}, nil
	})

	customDeprecatedApis, _ := types.MapSlice(spec.CustomDeprecatedAPIs, func(in models.CustomDeprecatedAPI) (*argocdv1.CustomDeprecatedAPI, error) {
		return &argocdv1.CustomDeprecatedAPI{
			ApiVersion:                     in.APIVersion,
			NewApiVersion:                  in.NewAPIVersion,
			DeprecatedInKubernetesVersion:  in.DeprecatedInKubernetesVersion,
			UnavailableInKubernetesVersion: in.UnavailableInKubernetesVersion,
		}, nil
	})

	var certStatus *argocdv1.CertificateStatus
	msg := "Certificate is up to date and has not expired"
	if !instanceStatus.Info.CertificateStatus.IsCNameSet {
		msg = "Verifying DNS Record"
	} else if !instanceStatus.Info.CertificateStatus.IsIssued {
		msg = "Provisioning Certificate"
	}

	if !config.IsSelfHosted && instance.FQDN.String != "" {
		certStatus = &argocdv1.CertificateStatus{
			IsCnameSet: instanceStatus.Info.CertificateStatus.IsCNameSet,
			IsIssued:   instanceStatus.Info.CertificateStatus.IsIssued,
			Message:    msg,
		}
	}
	appInAnyNamespaceCfg := argocdv1.AppInAnyNamespaceConfig{}
	if spec.AppInAnyNamespace != nil {
		appInAnyNamespaceCfg.Enabled = spec.AppInAnyNamespace.Enabled
	}

	appsetPlugins := []*argocdv1.AppsetPlugins{}
	for _, plugin := range spec.AppsetPlugins {
		timeout := 0
		if plugin.RequestTimeout != "" {
			timeout, err = strconv.Atoi(plugin.RequestTimeout)
			if err != nil {
				return nil, fmt.Errorf("invalid request timeout value: %w", err)
			}
		}
		appsetPlugins = append(appsetPlugins, &argocdv1.AppsetPlugins{
			Name:           plugin.Name,
			Token:          plugin.Token,
			BaseUrl:        plugin.BaseUrl,
			RequestTimeout: int32(timeout),
		})
	}

	return &argocdv1.Instance{
		Id:                     instance.ID,
		Shard:                  instance.Shard,
		Name:                   instance.Name,
		Hostname:               instance.StatusHostname.String,
		ClusterCount:           uint32(instance.ClusterCount),
		Secrets:                argoCDSecret,
		Generation:             uint32(instance.Generation),
		RecentProcessedEventId: uint32(processedEventInfo.EventId),
		HealthStatus:           NewInstanceHealthStatusV1(instanceStatus),
		ReconciliationStatus:   NewReconciliationStatusV1(reconciliationStatus),
		DeleteTime:             deleteTime,
		OwnerOrganizationName:  instance.OwnerOrganizationName,
		Description:            instance.Description.String,
		Version:                instance.Version.String,
		UnsupportedVersion:     &unsupportedVersion,
		Spec: &argocdv1.InstanceSpec{
			IpAllowList:                  ipAllowList,
			HostAliases:                  hostAliases,
			Subdomain:                    instance.Subdomain,
			Fqdn:                         instance.FQDN.String,
			DeclarativeManagementEnabled: spec.DeclarativeManagementEnabled,
			Extensions:                   extensions,
			ClusterCustomizationDefaults: &argocdv1.ClusterCustomization{
				Kustomization:       kustomization,
				AutoUpgradeDisabled: spec.ClusterCustomizationDefaults.AutoUpgradeDisabled,
				AppReplication:      spec.ClusterCustomizationDefaults.AppReplication,
				RedisTunneling:      spec.ClusterCustomizationDefaults.RedisTunneling,
			},
			ImageUpdaterEnabled:         instance.ArgocdImageUpdaterEnable.Bool,
			BackendIpAllowListEnabled:   spec.BackendIpAllowlistEnabled,
			AuditExtensionEnabled:       spec.AuditExtensionEnabled,
			SyncHistoryExtensionEnabled: spec.SyncHistoryExtensionEnabled,
			AssistantExtensionEnabled:   spec.AssistantExtensionEnabled,
			CrossplaneExtension:         NewCrossplaneExtensionV1(spec.CrossplaneExtension),
			RepoServerDelegate:          delegate,
			ImageUpdaterDelegate:        imageUpdaterDelegate,
			AppSetDelegate:              appSetDelegate,
			AppsetPolicy: &argocdv1.AppsetPolicy{
				Policy:         spec.AppsetPolicy.Policy,
				OverridePolicy: spec.AppsetPolicy.PolicyOverrideEnabled,
			},
			AppsetPlugins:                   appsetPlugins,
			AppsetProgressiveSyncsEnabled:   spec.AppsetProgressiveSyncsEnabled,
			AgentPermissionsRules:           agentPermissionsRules,
			MultiClusterK8SDashboardEnabled: spec.MultiClusterK8sDashboardEnabled,
			AkuityIntelligenceExtension:     NewAkuityIntelligenceExtension(spec.AkuityIntelligenceExtension),
			ImageUpdaterVersion:             spec.ImageUpdaterVersion,
			CustomDeprecatedApis:            customDeprecatedApis,
			Secrets:                         NewSecretManagementConfigV1(spec.Secrets),
			KubeVisionConfig:                NewKubeVisionConfig(spec.KubeVisionConfig),
			AppInAnyNamespaceConfig:         &appInAnyNamespaceCfg,
			Basepath:                        instance.Basepath,
			ApplicationSetExtension:         NewApplicationSetExtension(spec.ApplicationSetExtension),
			AppReconciliationsRateLimiting:  NewAppReconciliationsRateLimiting(spec.AppReconciliationsRateLimiting),
		},
		Config:     configMap,
		RbacConfig: rbacConfigMap,
		Info: &argocdv1.InstanceInfo{
			ApplicationsStatus: NewApplicationsStatusV1(instanceStatus),
			CertificateStatus:  certStatus,
		},
		WorkspaceId:                instance.WorkspaceID.String,
		NotIntegrationClusterCount: uint32(instance.NotIntegrationClusterCount),
	}, nil
}

func NewClusterSecretMappingV1(in models.ClusterSecretMapping) *argocdv1.ClusterSecretMapping {
	return &argocdv1.ClusterSecretMapping{
		Clusters: NewObjectSelectorV1(in.Clusters),
		Secrets:  NewObjectSelectorV1(in.Secrets),
	}
}

func NewSecretManagementConfigV1(in models.SecretsManagementConfig) *argocdv1.SecretsManagementConfig {
	out := &argocdv1.SecretsManagementConfig{}
	for i := range in.Sources {
		out.Sources = append(out.Sources, NewClusterSecretMappingV1(in.Sources[i]))
	}

	for i := range in.Destinations {
		out.Destinations = append(out.Destinations, NewClusterSecretMappingV1(in.Destinations[i]))
	}
	return out
}

func NewObjectSelectorV1(in models.ObjectSelector) *argocdv1.ObjectSelector {
	out := &argocdv1.ObjectSelector{
		MatchLabels: in.MatchLabels,
	}
	for _, expr := range in.MatchExpressions {
		out.MatchExpressions = append(out.MatchExpressions, &argocdv1.LabelSelectorRequirement{
			Key:      &expr.Key,
			Operator: ptr.To(string(expr.Operator)),
			Values:   expr.Values,
		})
	}
	return out
}

func NewCrossplaneExtensionV1(ext models.CrossplaneExtension) *argocdv1.CrossplaneExtension {
	newExt := &argocdv1.CrossplaneExtension{
		Resources: []*argocdv1.CrossplaneExtensionResource{},
	}

	if ext.Resources != nil {
		for _, extRes := range ext.Resources {
			newExt.Resources = append(newExt.Resources, &argocdv1.CrossplaneExtensionResource{
				Group: extRes.Group,
			})
		}
	}

	return newExt
}

func NewAkuityIntelligenceExtension(ext models.AkuityIntelligenceExtension) *argocdv1.AkuityIntelligenceExtension {
	return &argocdv1.AkuityIntelligenceExtension{
		Enabled:                  ext.Enabled,
		AllowedUsernames:         ext.AllowedUsernames,
		AllowedGroups:            ext.AllowedGroups,
		AiSupportEngineerEnabled: ext.AISupportEngineerEnabled,
	}
}

func NewInstanceHealthStatusV1(instanceStatus models.ArgoCDInstanceStatus) *healthv1.Status {
	switch {
	// if instance never been healthy yet then show health status reported by agent
	case instanceStatus.Info.ReachedGeneration == 0:
		return NewHealthStatusV1(instanceStatus.Health)
	// if instance settings were updated and instance is not healthy yet then show progressing status
	case instanceStatus.ObservedGeneration.Int != instanceStatus.Info.ReachedGeneration:
		return &healthv1.Status{
			Code:    healthv1.StatusCode_STATUS_CODE_PROGRESSING,
			Message: "Instance is progressing",
		}
	default:
		return &healthv1.Status{
			Code:    healthv1.StatusCode_STATUS_CODE_HEALTHY,
			Message: "Instance is healthy",
		}
	}
}

func NewApplicationsStatusV1(instanceStatus models.ArgoCDInstanceStatus) *argocdv1.ApplicationsStatus {
	as := instanceStatus.Info.ApplicationsStatus
	return &argocdv1.ApplicationsStatus{
		AppOfAppCount:       uint32(as.AppOfAppCount),
		ApplicationCount:    uint32(as.Count),
		ResourcesCount:      uint32(as.ResourcesCount),
		SyncInProgressCount: uint32(as.InProgressSyncCount),
		WarningCount:        uint32(as.Warnings),
		ErrorCount:          uint32(as.Errors),
		Health: &argocdv1.ApplicationsHealth{
			HealthyCount:     uint32(as.Health.Healthy),
			DegradedCount:    uint32(as.Health.Degraded),
			ProgressingCount: uint32(as.Health.Progressing),
			UnknownCount:     uint32(as.Health.Unknown),
			SuspendedCount:   uint32(as.Health.Suspended),
			MissingCount:     uint32(as.Health.Missing),
		},
		SyncStatus: &argocdv1.ApplicationsSyncStatus{
			SyncedCount:    uint32(as.SyncStatus.Synced),
			OutOfSyncCount: uint32(as.SyncStatus.OutOfSync),
			UnknownCount:   uint32(as.SyncStatus.Unknown),
		},
	}
}

// newArgoCDConfigMapV1 converts a models.ArgoCDConfigMap to an argocdv1.ArgoCDConfigMap.
func NewArgoCDConfigMapV1(configMap *models.ArgoCDConfigMap) *argocdv1.ArgoCDConfigMap {
	return &argocdv1.ArgoCDConfigMap{
		AdminEnabled:    ptr.To(configMap.AdminEnabled()),
		LogsRbacEnabled: ptr.To(configMap.LogsRBACEnabled == "true"),
		StatusBadge: &argocdv1.ArgoCDStatusBadgeConfig{
			Enabled: ptr.To(configMap.StatusBadgeEnabled == "true"),
			Url:     configMap.StatusBadgeUrl,
		},
		GoogleAnalytics: &argocdv1.ArgoCDGoogleAnalyticsConfig{
			TrackingId:     configMap.GoogleAnalyticsTrackingID,
			AnonymizeUsers: ptr.To(configMap.GoogleAnalyticsAnonymizeUsers == "true"),
		},
		AllowAnonymousUser: ptr.To(configMap.AnonymousEnabled == "true"),
		Banner: &argocdv1.ArgoCDBannerConfig{
			Message:   configMap.BannerContent,
			Url:       configMap.BannerUrl,
			Permanent: ptr.To(configMap.BannerPermanent == "true"),
		},
		Chat: &argocdv1.ArgoCDAlertConfig{
			Message: configMap.ChatMessage,
			Url:     configMap.ChatUrl,
		},
		InstanceLabelKey: configMap.InstanceLabelKey,
		KustomizeSettings: &argocdv1.ArgoCDKustomizeSettings{
			Enabled:      ptr.To(configMap.KustomizeEnabled == "true"),
			BuildOptions: configMap.KustomizeBuildOptions,
		},
		HelmSettings: &argocdv1.ArgoCDHelmSettings{
			Enabled:          ptr.To(configMap.HelmEnabled == "true"),
			ValueFileSchemas: configMap.HelmValueFileSchemas,
		},
		ResourceSettings: &argocdv1.ArgoCDResourceSettings{
			Inclusions:     configMap.ResourceInclusions,
			Exclusions:     configMap.ResourceExclusions,
			CompareOptions: configMap.ResourceCompareOptions,
		},
		UsersSessionDuration: configMap.UsersSessionDuration,
		OidcConfig:           configMap.OidcConfig,
		DexConfig:            configMap.DexConfig,
		WebTerminal: &argocdv1.ArgoCDWebTerminalConfig{
			Enabled: ptr.To(configMap.ExecEnabled == "true"),
			Shells:  configMap.ExecShells,
		},
		DeepLinks: &argocdv1.ArgoCDDeepLinks{
			ApplicationLinks: *NewDeepLinkV1Array(&configMap.ApplicationLinks),
			ProjectLinks:     *NewDeepLinkV1Array(&configMap.ProjectLinks),
			ResourceLinks:    *NewDeepLinkV1Array(&configMap.ResourceLinks),
		},
	}
}

// newArgoCDRBACConfigMapV1 converts a models.ArgoCDRbacConfigMap to a argocdv1.ArgoCDRBACConfigMap.
func NewArgoCDRBACConfigMapV1(configMap *models.ArgoCDRbacConfigMap) *argocdv1.ArgoCDRBACConfigMap {
	if configMap == nil {
		return nil
	}

	var overlayPolicies []*argocdv1.OverlayPolicy
	for _, policy := range configMap.OverlayPolicies {
		overlayPolicies = append(overlayPolicies, &argocdv1.OverlayPolicy{Name: policy.Name, Policy: policy.Policy})
	}

	return &argocdv1.ArgoCDRBACConfigMap{
		DefaultPolicy:   configMap.DefaultPolicy,
		PolicyCsv:       configMap.PolicyCSV,
		Scopes:          configMap.Scopes,
		OverlayPolicies: overlayPolicies,
	}
}

func NewHealthStatusV1(s statusmodel.HealthStatus) *healthv1.Status {
	var code healthv1.StatusCode
	switch s.Code {
	case statusmodel.HealthStatusCodeHealthy:
		code = healthv1.StatusCode_STATUS_CODE_HEALTHY
	case statusmodel.HealthStatusCodeProgressing:
		code = healthv1.StatusCode_STATUS_CODE_PROGRESSING
	case statusmodel.HealthStatusCodeDegraded:
		code = healthv1.StatusCode_STATUS_CODE_DEGRADED
	case statusmodel.HealthStatusCodeUnknown:
		code = healthv1.StatusCode_STATUS_CODE_UNKNOWN
	default:
		code = healthv1.StatusCode_STATUS_CODE_UNSPECIFIED
	}
	return &healthv1.Status{
		Code:    code,
		Message: s.Message,
	}
}

// newDeepLinksV1 converts an array of models.DeepLink to an  array of argocdv1.DeepLink.
func NewDeepLinkV1Array(deepLinks *[]models.DeepLink) *[]*argocdv1.DeepLink {
	var deepLinkV1Array []*argocdv1.DeepLink
	for _, link := range *deepLinks {
		deepLinkV1Array = append(deepLinkV1Array, NewDeepLinkV1(&link))
	}
	return &deepLinkV1Array
}

// newDeepLinkV1 converts a models.DeepLink to an argocdv1.DeepLink.
func NewDeepLinkV1(deepLink *models.DeepLink) *argocdv1.DeepLink {
	return &argocdv1.DeepLink{
		Title:       deepLink.Title,
		Url:         deepLink.URL,
		IconClass:   &deepLink.IconClass,
		Description: &deepLink.Description,
		If:          &deepLink.If,
	}
}

func NewArgoCDClusterV1(cluster models.ArgoCDCluster, progressingDeadline time.Duration, autoscalerConfig common.AutoScalerConfig) (*argocdv1.Cluster, error) {
	clusterSpec, err := cluster.GetSpec()
	if err != nil {
		return nil, err
	}
	clusterStatus, err := cluster.GetStatus()
	if err != nil {
		return nil, err
	}

	agentState := newArgoCDAgentStateV1(&cluster, clusterSpec, &clusterStatus, progressingDeadline*5)

	var clusterSize argocdv1.ClusterSize
	switch clusterSpec.Size {
	case models.ClusterSizeSmall:
		clusterSize = argocdv1.ClusterSize_CLUSTER_SIZE_SMALL
	case models.ClusterSizeMedium:
		clusterSize = argocdv1.ClusterSize_CLUSTER_SIZE_MEDIUM
	case models.ClusterSizeLarge:
		clusterSize = argocdv1.ClusterSize_CLUSTER_SIZE_LARGE
	case models.ClusterSizeAuto:
		clusterSize = argocdv1.ClusterSize_CLUSTER_SIZE_AUTO
	default:
		clusterSize = argocdv1.ClusterSize_CLUSTER_SIZE_UNSPECIFIED
	}

	var deleteTime *timestamppb.Timestamp
	if !cluster.DeletionTimestamp.IsZero() {
		deleteTime = timestamppb.New(cluster.DeletionTimestamp.Time)
	}

	observedGeneration := uint64(cluster.Generation)
	reconciliationStatus, err := cluster.GetReconciliationStatus(progressingDeadline, false)
	if err != nil {
		return nil, err
	}

	kustomization, err := structpb.NewStruct(clusterSpec.Kustomization)
	if err != nil {
		return nil, err
	}

	targetVersion := clusterSpec.TargetVersion

	var directClusterSpec *argocdv1.DirectClusterSpec
	if clusterSpec.DirectClusterSpec != nil {
		clusterType, err := modelToApiClusterType(clusterSpec.DirectClusterSpec.Type)
		if err != nil {
			return nil, err
		}
		directClusterSpec = &argocdv1.DirectClusterSpec{
			ClusterType: clusterType,
		}
		if clusterSpec.DirectClusterSpec.Type.IsKargo() {
			directClusterSpec.KargoInstanceId = ptr.To(clusterSpec.DirectClusterSpec.KargoInstanceID)
		} else {

			directClusterSpec.Server = ptr.To(clusterSpec.DirectClusterSpec.Upbound.Server)
			directClusterSpec.Organization = ptr.To(clusterSpec.DirectClusterSpec.Upbound.Organization)
			directClusterSpec.CaData = ptr.To(string(clusterSpec.DirectClusterSpec.Upbound.CAData))
			// token is never sent back to the user
		}
		targetVersion = ""
	}
	var managedClusterConfig *argocdv1.ManagedClusterConfig
	if clusterSpec.ManagedClusterConfig != nil {
		managedClusterConfig = &argocdv1.ManagedClusterConfig{
			SecretName: clusterSpec.ManagedClusterConfig.SecretName,
			SecretKey:  clusterSpec.ManagedClusterConfig.SecretKey,
		}
	}
	if clusterSpec.AutoscalerConfig != nil {
		autoscalerConfig = *clusterSpec.AutoscalerConfig
	}
	autoscalerConfigV1 := &argocdv1.AutoScalerConfig{
		ApplicationController: &argocdv1.AppControllerAutoScalingConfig{
			ResourceMinimum: &argocdv1.Resources{
				Cpu: autoscalerConfig.ApplicationController.ResourceMinimum.CPU.String(),
				Mem: formatMemQuantity(autoscalerConfig.ApplicationController.ResourceMinimum.Memory),
			},
			ResourceMaximum: &argocdv1.Resources{
				Cpu: autoscalerConfig.ApplicationController.ResourceMaximum.CPU.String(),
				Mem: formatMemQuantity(autoscalerConfig.ApplicationController.ResourceMaximum.Memory),
			},
		},
		RepoServer: &argocdv1.RepoServerAutoScalingConfig{
			ResourceMinimum: &argocdv1.Resources{
				Cpu: autoscalerConfig.RepoServer.ResourceMinimum.CPU.String(),
				Mem: formatMemQuantity(autoscalerConfig.RepoServer.ResourceMinimum.Memory),
			},
			ResourceMaximum: &argocdv1.Resources{
				Cpu: autoscalerConfig.RepoServer.ResourceMaximum.CPU.String(),
				Mem: formatMemQuantity(autoscalerConfig.RepoServer.ResourceMaximum.Memory),
			},
			ReplicaMaximum: autoscalerConfig.RepoServer.ReplicaMaximum,
			ReplicaMinimum: autoscalerConfig.RepoServer.ReplicaMinimum,
		},
	}

	isClusterInMaintenanceMode := clusterSpec != nil && clusterSpec.MaintenanceMode

	info, err := cluster.GetStatusK8sInfo()
	if err != nil {
		return nil, err
	}

	return &argocdv1.Cluster{
		Id:              cluster.GetID(),
		Name:            cluster.Name,
		Description:     cluster.Description.String,
		Namespace:       cluster.Namespace,
		NamespaceScoped: cluster.NamespaceScoped,
		Data: &argocdv1.ClusterData{
			Size:                            clusterSize,
			AutoUpgradeDisabled:             ptr.To(cluster.AutoUpgradeDisabled),
			Kustomization:                   kustomization,
			Labels:                          clusterSpec.Labels,
			Annotations:                     clusterSpec.Annotations,
			TargetVersion:                   targetVersion,
			AppReplication:                  ptr.To(clusterSpec.AppReplication),
			RedisTunneling:                  ptr.To(clusterSpec.RedisTunneling),
			DirectClusterSpec:               directClusterSpec,
			DatadogAnnotationsEnabled:       &clusterSpec.DatadogAnnotationsEnabled,
			Namespace:                       cluster.Namespace,
			NamespaceScoped:                 cluster.NamespaceScoped,
			EksAddonEnabled:                 &clusterSpec.EKSAddonEnabled,
			ManagedClusterConfig:            managedClusterConfig,
			MaintenanceMode:                 &isClusterInMaintenanceMode,
			MultiClusterK8SDashboardEnabled: &clusterSpec.MultiClusterK8SDashboardEnabled,
			AutoscalerConfig:                autoscalerConfigV1,
			Project:                         strings.TrimSpace(clusterSpec.Project),
			Compatibility: &argocdv1.ClusterCompatibility{
				Ipv6Only: clusterSpec.Compatibility.IPV6Only,
			},
			ArgocdNotificationsSettings: &argocdv1.ClusterArgoCDNotificationsSettings{
				InClusterSettings: clusterSpec.ArgoCDNotifications.InClusterSettings,
			},
		},
		DeleteTime:                        deleteTime,
		ObservedGeneration:                &observedGeneration,
		CredentialRotationAllowed:         ptr.To(cluster.StatusObservedCredRotation.Int == clusterSpec.AgentRotationCount),
		AgentState:                        agentState,
		HealthStatus:                      NewHealthStatusV1(clusterStatus.GetHealth()),
		ReconciliationStatus:              NewReconciliationStatusV1(reconciliationStatus),
		ReadonlySettingsChangedGeneration: ptr.To(uint64(cluster.ReadonlySettingsChangedGeneration.Int)),
		K8SStatus: &argocdv1.ClusterKubernetesStatus{
			KubernetesVersion: info.KubernetesVersion,
			ApiResourceCount:  info.ApiResourceCount,
			ObjectCount:       info.ObjectCount,
		},
	}, nil
}

func newArgoCDAgentStateV1(cluster *models.ArgoCDCluster, spec *models.ClusterSpec, clusterStatus *models.ArgoCDClusterStatus, autoUpdateDeadline time.Duration) *argocdv1.AgentState {
	if clusterStatus.GetAgentState() == nil {
		return &argocdv1.AgentState{}
	}

	state := clusterStatus.AgentState
	var observeTime *timestamppb.Timestamp
	if !state.ObservedAt.IsZero() {
		observeTime = timestamppb.New(state.ObservedAt.Time)
	}
	var agentResources *argocdv1.AgentResources
	if spec.Size == models.ClusterSizeAuto {
		agentResources = &argocdv1.AgentResources{}
		for name, res := range state.Resources {
			switch name {
			case common.ArgoCDAppController:
				agentResources.ApplicationController = &argocdv1.ApplicationControllerResources{
					Requests: &argocdv1.Resources{
						Mem: formatMemQuantity(*res.Requirements.Requests.Memory()),
						Cpu: res.Requirements.Requests.Cpu().AsDec().String(),
					},
				}
			case common.ArgoCDRepoServer:
				agentResources.RepoServer = &argocdv1.RepoServerResources{
					Requests: &argocdv1.Resources{
						Mem: formatMemQuantity(*res.Requirements.Requests.Memory()),
						Cpu: res.Requirements.Requests.Cpu().AsDec().String(),
					},
					Replicas: res.Replicas,
				}
			}
		}
	}
	updateStatus := reconciliationv1.AgentUpdateStatus_AGENT_UPDATE_STATUS_UPDATED
	if cluster.DeletionTimestamp.IsZero() && !cluster.AutoUpgradeDisabled && int64(clusterStatus.ObservedGeneration.Int) != state.Status.MinObservedGeneration && state.Status.MinObservedGeneration > -1 {
		if !state.ObservedGenerationAppliedAt.IsZero() && time.Since(state.ObservedGenerationAppliedAt.Time) > autoUpdateDeadline {
			updateStatus = reconciliationv1.AgentUpdateStatus_AGENT_UPDATE_STATUS_DELAYED
		} else {
			updateStatus = reconciliationv1.AgentUpdateStatus_AGENT_UPDATE_STATUS_IN_PROGRESS
		}
	}
	return &argocdv1.AgentState{
		Version:        state.Version,
		ArgoCdVersion:  state.ArgoCDVersion,
		ObserveTime:    observeTime,
		AgentResources: agentResources,
		Status: &healthv1.AgentAggregatedHealthResponse{
			MinObservedGeneration: uint64(state.Status.MinObservedGeneration),
			Healthy:               NewAggregatedHealthStatus(state.Status.Healthy),
			Progressing:           NewAggregatedHealthStatus(state.Status.Progressing),
			Degraded:              NewAggregatedHealthStatus(state.Status.Degraded),
			Unknown:               NewAggregatedHealthStatus(state.Status.Unknown),
			PriorityStatus:        MapTenantPhaseToRpcPhase(state.Status.PriorityStatus),
		},
		AgentIds:                  state.AgentIDs,
		LastUserAppliedGeneration: uint64(state.LastUserAppliedGeneration),
		UpdateStatus:              &updateStatus,
	}
}

func GetArgoCDVersions(log logr.Logger) ([]client.ComponentVersion, error) {
	argoCDVersions, err := client.ListArgoCDVersions()
	if err != nil {
		return nil, fmt.Errorf("failed to list the ArgoCD versions: %w", err)
	}

	argoCDVersions, err = SortVersions(argoCDVersions)
	if err != nil {
		return nil, fmt.Errorf("failed to sort ArgoCD versions: %w", err)
	}

	userProvidedArgoCDVersionsStr, err := os.ReadFile(filepath.Clean(userProvidedArgoCDVersionsVolumeMountPath))
	if err != nil {
		log.Info("cannot read user provided ArgoCD versions", "error", err)
		return argoCDVersions, nil
	}

	var userProvidedArgoCDVersions []string
	if err := yaml.Unmarshal(userProvidedArgoCDVersionsStr, &userProvidedArgoCDVersions); err != nil {
		log.Info("cannot unmarshal user provided ArgoCD versions", "error", err)
		return argoCDVersions, nil
	}

	// This map tracks all major.minor versions coming from info.yaml.
	argoCDVersionMap := make(map[string]bool)
	for _, version := range argoCDVersions {
		if version.Version != "latest" {
			v, err := majorMinor(version.Version)
			if err != nil {
				log.Info("cannot parse default ArgoCD version", "error", err)
				continue
			}
			argoCDVersionMap[v] = true
		}
	}

	for _, version := range userProvidedArgoCDVersions {
		version = strings.TrimSpace(version)
		if version != "" {
			v, err := majorMinor(version)
			if err != nil {
				log.Info("cannot parse user-provided ArgoCD version", "error", err)
				continue
			}
			// Only add those user-provided patch versions for which corresponding major.minor version exists.
			if argoCDVersionMap[v] {
				argoCDVersions = append(argoCDVersions, client.ComponentVersion{Version: version})
			} else {
				log.Info("no major.minor version exists for user provided ArgoCD version", "version", version)
			}
		}
	}

	return SortVersions(argoCDVersions)
}

func majorMinor(version string) (string, error) {
	v, err := semver.NewVersion(version)
	if err != nil {
		return "", fmt.Errorf("cannot parse version %q: %w", version, err)
	}
	return fmt.Sprintf("%d.%d", v.Major(), v.Minor()), nil
}

func modelToApiClusterType(clusterType models.DirectClusterType) (argocdv1.DirectClusterType, error) {
	switch clusterType {
	case models.DirectClusterTypeUpbound:
		return argocdv1.DirectClusterType_DIRECT_CLUSTER_TYPE_UPBOUND, nil
	case models.DirectClusterTypeKargo:
		return argocdv1.DirectClusterType_DIRECT_CLUSTER_TYPE_KARGO, nil
	}
	return argocdv1.DirectClusterType_DIRECT_CLUSTER_TYPE_KARGO, fmt.Errorf("invalid direct cluster type")
}

// SortVersions sorts the versions in descending order.
func SortVersions(versions []client.ComponentVersion) ([]client.ComponentVersion, error) {
	type tempComponentVersion struct {
		version *semver.Version
		client.ComponentVersion
	}
	versionList := []tempComponentVersion{}
	for i := range versions {
		if versions[i].Version != "latest" {
			v, err := semver.NewVersion(versions[i].Version)
			if err != nil {
				return nil, fmt.Errorf("failed to parse version %q: %w", versions[i].Version, err)
			}
			versionList = append(versionList, tempComponentVersion{version: v, ComponentVersion: versions[i]})
		} else {
			versionList = append(versionList, tempComponentVersion{version: nil, ComponentVersion: versions[i]})
		}
	}

	sort.Slice(versionList, func(i, j int) bool {
		if versionList[i].Version == "latest" {
			return true
		}
		if versionList[j].Version == "latest" {
			return false
		}
		return versionList[i].version.GreaterThan(versionList[j].version)
	})

	sortedVersions := []client.ComponentVersion{}
	for i := range versionList {
		sortedVersions = append(sortedVersions, versionList[i].ComponentVersion)
	}
	return sortedVersions, nil
}

// GetAKPVersion shared between kargo adn argocd, this checks if the image version used is a custom akp image build
func GetAKPVersion(version string) (bool, int64) {
	akVersionPattern := regexp.MustCompile(`v\d+.\d+.\d+-ak.(\d+)`)
	if matched := akVersionPattern.MatchString(version); matched {
		ver, err := strconv.ParseInt(akVersionPattern.FindStringSubmatch(version)[1], 10, 64)
		if err != nil {
			panic(err)
		}
		return true, ver
	}
	return false, 0
}

func formatMemQuantity(mem resource.Quantity) string {
	return fmt.Sprintf("%0.2fGi", mem.AsApproximateFloat64()/1024/1024/1024)
}
