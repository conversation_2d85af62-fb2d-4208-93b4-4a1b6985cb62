{"metadata": {"resourceVersion": "157048"}, "items": [{"metadata": {"name": "kargo-demo-prod", "namespace": "argocd", "uid": "0377726c-fd3b-4a42-81fb-3f72751bfc46", "resourceVersion": "156989", "generation": 182, "creationTimestamp": "2025-08-16T16:23:36Z", "labels": {"cluster": "test-cluster"}, "annotations": {"kargo.akuity.io/authorized-stage": "kargo-demo:prod"}, "ownerReferences": [{"apiVersion": "argoproj.io/v1alpha1", "kind": "ApplicationSet", "name": "kargo-demo", "uid": "37e3b321-30bb-47fc-88e1-9c75a65e2fd2", "controller": true, "blockOwnerDeletion": true}], "finalizers": ["resources-finalizer.argocd.argoproj.io"], "managedFields": [{"manager": "argocd-applicationset-controller", "operation": "Update", "apiVersion": "argoproj.io/v1alpha1", "time": "2025-08-16T16:23:36Z", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {".": {}, "f:kargo.akuity.io/authorized-stage": {}}, "f:finalizers": {".": {}, "v:\"resources-finalizer.argocd.argoproj.io\"": {}}, "f:ownerReferences": {".": {}, "k:{\"uid\":\"37e3b321-30bb-47fc-88e1-9c75a65e2fd2\"}": {}}}, "f:spec": {".": {}, "f:destination": {".": {}, "f:namespace": {}, "f:server": {}}, "f:project": {}, "f:source": {".": {}, "f:path": {}, "f:repoURL": {}, "f:targetRevision": {}}, "f:syncPolicy": {".": {}, "f:syncOptions": {}}}, "f:status": {".": {}, "f:health": {}, "f:sourceHydrator": {}, "f:summary": {}, "f:sync": {".": {}, "f:comparedTo": {".": {}, "f:destination": {}, "f:source": {}}}}}}, {"manager": "argocd-application-controller", "operation": "Update", "apiVersion": "argoproj.io/v1alpha1", "time": "2025-08-18T03:29:43Z", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:controllerNamespace": {}, "f:health": {"f:lastTransitionTime": {}, "f:status": {}}, "f:reconciledAt": {}, "f:resourceHealthSource": {}, "f:sourceType": {}, "f:sync": {"f:comparedTo": {"f:destination": {"f:namespace": {}, "f:server": {}}, "f:source": {"f:path": {}, "f:repoURL": {}, "f:targetRevision": {}}}, "f:revision": {}, "f:status": {}}}}}]}, "spec": {"source": {"repoURL": "https://github.com/hanxiaop/kargo-demo.git", "path": ".", "targetRevision": "stage/prod"}, "destination": {"server": "http://cluster-test-cluster:8001", "namespace": "kargo-demo-prod"}, "project": "default", "syncPolicy": {"syncOptions": ["CreateNamespace=true"]}}, "status": {"sync": {"status": "Synced", "comparedTo": {"source": {"repoURL": "https://github.com/hanxiaop/kargo-demo.git", "path": ".", "targetRevision": "stage/prod"}, "destination": {"server": "http://cluster-test-cluster:8001", "namespace": "kargo-demo-prod"}}, "revision": "1941a1e213fda1913ce399c7948f44a597a97502"}, "health": {"status": "Healthy", "lastTransitionTime": "2025-08-16T16:23:39Z"}, "reconciledAt": "2025-08-18T03:29:43Z", "sourceType": "Directory", "summary": {}, "resourceHealthSource": "appTree", "controllerNamespace": "argocd", "sourceHydrator": {}}}, {"metadata": {"name": "kargo-demo-uat", "namespace": "argocd", "uid": "*************-4792-a4ef-e37847cc1a01", "resourceVersion": "156768", "generation": 500, "creationTimestamp": "2025-08-15T10:58:09Z", "labels": {"cluster": "test-cluster"}, "annotations": {"kargo.akuity.io/authorized-stage": "kargo-demo:uat"}, "ownerReferences": [{"apiVersion": "argoproj.io/v1alpha1", "kind": "ApplicationSet", "name": "kargo-demo", "uid": "37e3b321-30bb-47fc-88e1-9c75a65e2fd2", "controller": true, "blockOwnerDeletion": true}], "finalizers": ["resources-finalizer.argocd.argoproj.io"], "managedFields": [{"manager": "argocd-applicationset-controller", "operation": "Update", "apiVersion": "argoproj.io/v1alpha1", "time": "2025-08-15T10:58:09Z", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {".": {}, "f:kargo.akuity.io/authorized-stage": {}}, "f:finalizers": {".": {}, "v:\"resources-finalizer.argocd.argoproj.io\"": {}}, "f:ownerReferences": {".": {}, "k:{\"uid\":\"37e3b321-30bb-47fc-88e1-9c75a65e2fd2\"}": {}}}, "f:spec": {".": {}, "f:destination": {".": {}, "f:namespace": {}, "f:server": {}}, "f:project": {}, "f:source": {".": {}, "f:path": {}, "f:repoURL": {}, "f:targetRevision": {}}, "f:syncPolicy": {".": {}, "f:syncOptions": {}}}, "f:status": {".": {}, "f:health": {}, "f:sourceHydrator": {}, "f:summary": {}, "f:sync": {".": {}, "f:comparedTo": {".": {}, "f:destination": {}, "f:source": {}}}}}}, {"manager": "argocd-application-controller", "operation": "Update", "apiVersion": "argoproj.io/v1alpha1", "time": "2025-08-18T03:27:05Z", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:controllerNamespace": {}, "f:health": {"f:lastTransitionTime": {}, "f:status": {}}, "f:reconciledAt": {}, "f:resourceHealthSource": {}, "f:resources": {}, "f:sourceType": {}, "f:sync": {"f:comparedTo": {"f:destination": {"f:namespace": {}, "f:server": {}}, "f:source": {"f:path": {}, "f:repoURL": {}, "f:targetRevision": {}}}, "f:revision": {}, "f:status": {}}}}}]}, "spec": {"source": {"repoURL": "https://github.com/hanxiaop/kargo-demo.git", "path": ".", "targetRevision": "stage/uat"}, "destination": {"server": "http://cluster-test-cluster:8001", "namespace": "kargo-demo-uat"}, "project": "default", "syncPolicy": {"syncOptions": ["CreateNamespace=true"]}}, "status": {"resources": [{"version": "v1", "kind": "ConfigMap", "namespace": "kargo-demo-uat", "name": "kargo-demo-content", "status": "OutOfSync"}, {"version": "v1", "kind": "Service", "namespace": "kargo-demo-uat", "name": "kargo-demo", "status": "OutOfSync"}, {"group": "apps", "version": "v1", "kind": "Deployment", "namespace": "kargo-demo-uat", "name": "kargo-demo", "status": "Unknown"}], "sync": {"status": "OutOfSync", "comparedTo": {"source": {"repoURL": "https://github.com/hanxiaop/kargo-demo.git", "path": ".", "targetRevision": "stage/uat"}, "destination": {"server": "http://cluster-test-cluster:8001", "namespace": "kargo-demo-uat"}}, "revision": "760231b6ceb9f6bde8c16ad26edbe2fcabd57de7"}, "health": {"status": "Missing", "lastTransitionTime": "2025-08-17T09:08:01Z"}, "reconciledAt": "2025-08-18T03:27:05Z", "sourceType": "Directory", "summary": {}, "resourceHealthSource": "appTree", "controllerNamespace": "argocd", "sourceHydrator": {}}}], "stats": {"total": 2, "totalBySyncStatus": {"OutOfSync": 1, "Synced": 1}, "totalByHealthStatus": {"Healthy": 1, "Missing": 1}, "autoSyncEnabledCount": 0, "destinations": [{"server": "http://cluster-test-cluster:8001"}], "namespaces": ["kargo-demo-prod", "kargo-demo-uat"], "labels": [{"key": "cluster", "values": ["test-cluster"]}]}}