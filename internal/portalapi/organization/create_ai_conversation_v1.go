package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) CreateAIConversation(
	ctx context.Context,
	req *organizationv1.CreateAIConversationRequest,
) (*organizationv1.CreateAIConversationResponse, error) {
	actor, err := s.checkAISupportEngineerPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionCreateAISupportEngineer(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	featureStatuses := s.featSvc.GetFeatureStatuses(ctx, &orgID)
	aiSvc, err := ai.NewService(s.Db, s.RepoSet, featureStatuses, orgID, s.Cfg.AI, s.hostRestConfig, logging.Extract(ctx))
	if err != nil {
		return nil, err
	}
	var conversation *organizationv1.AIConversation
	if req.Incident {
		incident, runbooks, err := aiSvc.IncidentFromContexts(ctx, req.GetInstanceId(), req.GetContexts())
		if err != nil {
			return nil, err
		}
		conversation, err = aiSvc.CreateIncident(ctx, actor, req.GetInstanceId(), req.GetOrganizationId(), incident, runbooks)
		if err != nil {
			return nil, err
		}
	} else if req.KargoPromotionAnalysis != nil {
		conversation, err = aiSvc.CreatePromotionAnalysis(ctx, actor, req.GetContexts(), req.GetInstanceId(), req.GetOrganizationId(), req.GetKargoPromotionAnalysis().GetProject(), req.GetKargoPromotionAnalysis().GetFreight(), req.GetKargoPromotionAnalysis().GetStage(), req.GetKargoPromotionAnalysis().GetReAnalyze())
		if err != nil {
			return nil, err
		}
	} else {
		conversation, err = aiSvc.CreateConversation(ctx, actor, req.GetInstanceId(), req.Contexts)
		if err != nil {
			return nil, err
		}
	}

	return &organizationv1.CreateAIConversationResponse{
		Conversation: conversation,
	}, nil
}
