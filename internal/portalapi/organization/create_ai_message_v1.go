package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) CreateAIMessage(
	ctx context.Context,
	req *organizationv1.CreateAIMessageRequest,
) (*organizationv1.CreateAIMessageResponse, error) {
	actor, err := s.checkAISupportEngineerPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionCreateAISupportEngineer(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	featureStatuses := s.featSvc.GetFeatureStatuses(ctx, &orgID)
	aiSvc, err := ai.NewService(s.Db, s.RepoSet, featureStatuses, orgID, s.Cfg.AI, s.hostRestConfig, logging.Extract(ctx))
	if err != nil {
		return nil, err
	}

	if err := aiSvc.CreateMessage(ctx, req.GetConversationId(), actor, req.GetContent(), req.GetContexts(), req.GetRunbooks()); err != nil {
		return nil, err
	}
	return &organizationv1.CreateAIMessageResponse{}, nil
}
