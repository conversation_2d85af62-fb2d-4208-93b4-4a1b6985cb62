package argocd

import (
	"context"
	"strings"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/apimachinery/pkg/api/resource"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstanceCluster(
	ctx context.Context,
	req *argocdv1.UpdateInstanceClusterRequest,
) (*argocdv1.UpdateInstanceClusterResponse, error) {
	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	// TODO: let's switch from clusterID to clusterName if it's possible.
	cluster, err := instanceSvc.GetClusterByID(ctx, req.GetId())
	if err != nil {
		// return permission denied so we not expose if cluster exists or not
		// before running enforcer
		return nil, status.Error(codes.PermissionDenied, "")
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateInstanceClusters(req.GetWorkspaceId(), cluster.InstanceID, cluster.Name)); err != nil {
		return nil, err
	}

	data := req.GetData()
	if data == nil {
		return nil, status.Error(codes.InvalidArgument, "missing required field `data`")
	}

	var managedClusterConfig *models.ManagedClusterConfig
	if data.ManagedClusterConfig != nil {
		managedClusterConfig = &models.ManagedClusterConfig{
			SecretName: data.ManagedClusterConfig.GetSecretName(),
			SecretKey:  data.ManagedClusterConfig.GetSecretKey(),
		}
	}

	var autoscalerConfig *common.AutoScalerConfig
	var clusterSize models.ClusterSize
	switch data.GetSize() {
	case argocdv1.ClusterSize_CLUSTER_SIZE_SMALL:
		clusterSize = models.ClusterSizeSmall
	case argocdv1.ClusterSize_CLUSTER_SIZE_MEDIUM:
		clusterSize = models.ClusterSizeMedium
	case argocdv1.ClusterSize_CLUSTER_SIZE_LARGE:
		clusterSize = models.ClusterSizeLarge
	case argocdv1.ClusterSize_CLUSTER_SIZE_AUTO:
		clusterSize = models.ClusterSizeAuto
		orgID := req.GetOrganizationId()
		if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetClusterAutoscaler().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "cluster autoscaler feature is not enabled")
		}
		// check if we can disable auto upgrade
		if data.GetAutoUpgradeDisabled() {
			return nil, status.Error(codes.InvalidArgument, "cluster size Auto can't be used when Auto Upgrade disabled")
		}

		if data.AutoscalerConfig != nil {
			autoscalerConfig, err = parseAutoscalerConfig(data.AutoscalerConfig)
			if err != nil {
				return nil, err
			}
		}
	default:
		return nil, status.Error(codes.InvalidArgument, "cluster size param is mandatory")
	}

	namespace := strings.TrimSpace(data.GetNamespace())
	// if namespace is empty, use the current cluster's namespace.
	if namespace == "" {
		namespace = cluster.Namespace
	}

	var compatibility models.ClusterCompatibility
	if val := data.GetCompatibility(); val != nil {
		compatibility = models.ClusterCompatibility{IPV6Only: val.Ipv6Only}
	}
	var argoCDNotifications models.ClusterArgoCDNotificationsControllerSpec
	if val := data.GetArgocdNotificationsSettings(); val != nil {
		argoCDNotifications.InClusterSettings = val.InClusterSettings
	}
	var directClusterSpec *models.DirectClusterSpec
	if val := data.GetDirectClusterSpec(); val != nil {
		switch val.ClusterType {
		case argocdv1.DirectClusterType_DIRECT_CLUSTER_TYPE_KARGO:
			directClusterSpec = &models.DirectClusterSpec{
				Type:            models.DirectClusterTypeKargo,
				KargoInstanceID: val.GetKargoInstanceId(),
			}
		case argocdv1.DirectClusterType_DIRECT_CLUSTER_TYPE_UPBOUND:
			directClusterSpec = &models.DirectClusterSpec{
				Type: models.DirectClusterTypeUpbound,
				Upbound: models.DirectClusterSpecUpboundConfig{
					Server:       val.GetServer(),
					Organization: val.GetOrganization(),
					Token:        val.GetToken(),
					CAData:       []byte(val.GetCaData()),
				},
			}
		}
	}
	updatedClusterModel, err := instanceSvc.UpdateCluster(ctx,
		req.GetId(),
		req.GetDescription(),
		namespace,
		data.GetNamespaceScoped(),
		data.GetAutoUpgradeDisabled(),
		req.Force || s.cfg.DisableOptionalClusterValidation,
		models.ClusterSpec{
			Size:                            clusterSize,
			Labels:                          data.GetLabels(),
			Annotations:                     data.GetAnnotations(),
			Kustomization:                   data.Kustomization.AsMap(),
			TargetVersion:                   data.GetTargetVersion(),
			RedisTunneling:                  data.GetRedisTunneling(),
			DatadogAnnotationsEnabled:       data.GetDatadogAnnotationsEnabled(),
			AppReplication:                  data.GetAppReplication(),
			EKSAddonEnabled:                 data.GetEksAddonEnabled(),
			DirectClusterSpec:               directClusterSpec,
			ManagedClusterConfig:            managedClusterConfig,
			MultiClusterK8SDashboardEnabled: data.GetMultiClusterK8SDashboardEnabled(),
			AutoscalerConfig:                autoscalerConfig,
			Project:                         strings.TrimSpace(data.GetProject()),
			Compatibility:                   compatibility,
			ArgoCDNotifications:             argoCDNotifications,
		},
	)
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, shared.ErrNotFound
		}
		return nil, err
	}
	updatedCluster, err := argocdutil.NewArgoCDClusterV1(*updatedClusterModel, s.cfg.ClusterProgressingDeadline, s.clusterAutoscalerConfig)
	if err != nil {
		return nil, err
	}
	return &argocdv1.UpdateInstanceClusterResponse{
		Cluster: updatedCluster,
	}, nil
}

func parseAutoscalerConfig(config *argocdv1.AutoScalerConfig) (*common.AutoScalerConfig, error) {
	var autoscalerConfig *common.AutoScalerConfig
	appCtrlrMinCpuQty, err := resource.ParseQuantity(config.ApplicationController.ResourceMinimum.Cpu)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "can't parse %s: %s", "application controller resource minimum CPU", err.Error())
	}
	appCtrlrMinMemQty, err := resource.ParseQuantity(config.ApplicationController.ResourceMinimum.Mem)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "can't parse %s: %s", "application controller resource minimum Memory", err.Error())
	}
	appCtrlrMaxCpuQty, err := resource.ParseQuantity(config.ApplicationController.ResourceMaximum.Cpu)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "can't parse %s: %s", "application controller resource maximum CPU", err.Error())
	}
	appCtrlrMaxMemQty, err := resource.ParseQuantity(config.ApplicationController.ResourceMaximum.Mem)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "can't parse %s: %s", "application controller resource maximum Memory", err.Error())
	}
	repoServerMinCpuQty, err := resource.ParseQuantity(config.RepoServer.ResourceMinimum.Cpu)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "can't parse %s: %s", "reposerver resource minimum CPU", err.Error())
	}
	repoServerMinMemQty, err := resource.ParseQuantity(config.RepoServer.ResourceMinimum.Mem)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "can't parse %s: %s", "reposerver resource minimum Memory", err.Error())
	}
	repoServerMaxCpuQty, err := resource.ParseQuantity(config.RepoServer.ResourceMaximum.Cpu)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "can't parse %s: %s", "reposerver resource maximum CPU", err.Error())
	}
	repoServerMaxMemQty, err := resource.ParseQuantity(config.RepoServer.ResourceMaximum.Mem)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "can't parse %s: %s", "reposerver resource maximum Memory", err.Error())
	}

	autoscalerConfig = &common.AutoScalerConfig{
		ApplicationController: &common.AppControllerAutoScalingConfig{
			ResourceMinimum: common.WorkloadResourceValues{
				CPU:    appCtrlrMinCpuQty,
				Memory: appCtrlrMinMemQty,
			},
			ResourceMaximum: common.WorkloadResourceValues{
				CPU:    appCtrlrMaxCpuQty,
				Memory: appCtrlrMaxMemQty,
			},
		},
		RepoServer: &common.RepoServerAutoScalingConfig{
			ResourceMinimum: common.WorkloadResourceValues{
				CPU:    repoServerMinCpuQty,
				Memory: repoServerMinMemQty,
			},
			ResourceMaximum: common.WorkloadResourceValues{
				CPU:    repoServerMaxCpuQty,
				Memory: repoServerMaxMemQty,
			},
			ReplicaMaximum: config.RepoServer.ReplicaMaximum,
			ReplicaMinimum: config.RepoServer.ReplicaMinimum,
		},
	}
	return autoscalerConfig, nil
}
