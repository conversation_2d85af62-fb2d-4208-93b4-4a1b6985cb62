package kargo

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/volatiletech/null/v8"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) DeleteInstanceAgent(
	ctx context.Context,
	req *kargov1.DeleteInstanceAgentRequest,
) (*kargov1.DeleteInstanceAgentResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	instance, err := s.instances.Filter(
		models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())),
		models.KargoInstanceWhere.ID.EQ(req.GetInstanceId()),
	).One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errorsutil.NewAPIStatus(http.StatusForbidden, fmt.Sprintf("instance %s not found", req.GetInstanceId()))
		}
		return nil, err
	}
	// check if cluster is default shard
	spec, errr := instance.Config.GetSpec()
	if errr != nil {
		return nil, errr
	}
	if req.GetId() == "" {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "agent ID is required")
	}
	if spec.DefaultShardAgentID == req.GetId() {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "cannot delete default shard agent, change default shard before deleting")
	}
	agent, err := s.repoSet.KargoAgents(
		models.KargoAgentNoStatusManifestMod,
		models.KargoAgentWhere.ID.EQ(req.GetId()),
		models.KargoAgentWhere.InstanceID.EQ(req.GetInstanceId())).One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errorsutil.NewAPIStatus(http.StatusNotFound, fmt.Sprintf("agent %s not found", req.GetId()))
		}
		return nil, err
	}

	// Check permissions
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionDeleteKargoInstanceAgents(
			req.GetWorkspaceId(),
			req.GetInstanceId(),
			agent.Name,
		)); err != nil {
		return nil, err
	}

	agent.DeletionTimestamp = null.TimeFrom(time.Now())

	return &kargov1.DeleteInstanceAgentResponse{}, s.repoSet.KargoAgents().Update(ctx, agent, models.KargoAgentColumns.DeletionTimestamp)
}
