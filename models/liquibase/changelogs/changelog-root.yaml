databaseChangeLog:
  - changeSet:
      id: 1
      author: remington
      changes:
        createTable:
          tableName: organization
          columns:
            - column:
                name: id
                type: varchar(50)
                constraints:
                  nullable: false
                  primaryKey: true
            - column:
                name: name
                type: var<PERSON>r(50)
                constraints:
                  nullable: false
                  unique: true
            - column:
                name: memberCount
                type: int
            - column:
                name: max_instances
                type: int
                constraints:
                  nullable: false
            - column:
                name: max_clusters
                type: int
                constraints:
                  nullable: false
  - changeSet:
      id: 2
      author: remington
      changes:
        createTable:
          tableName: akuity_user
          columns:
            - column:
                name: id
                type: varchar(50)
                constraints:
                  primaryKey: true
                  nullable: false
            - column:
                name: email
                type: varchar(254)
                constraints:
                  unique: true
                  nullable: false
            - column:
                name: max_instances
                type: int
                constraints:
                  nullable: false
            - column:
                name: max_clusters
                type: int
                constraints:
                  nullable: false
  # Argo CD Instance Spec Fields
  - changeSet:
      id: 3
      author: remington
      changes:
        createTable:
          tableName: argo_cd_instance
          columns:
            - column:
                name: id
                type: varchar(22)
                constraints:
                  primaryKey: true
                  nullable: false
            - column:
                name: name
                type: var<PERSON><PERSON>(50)
                constraints:
                  nullable: false
            - column:
                name: user_owner
                type: var<PERSON><PERSON>(50)
                constraints:
                  foreignKeyName: fk_argo_cd_instance_user_owner
                  references: akuity_user(id)
            - column:
                name: organization_owner
                type: varchar(50)
                constraints:
                  foreignKeyName: fk_argo_cd_instance_organization_owner
                  references: organization(id)
            - column:
                name: generation
                type: int
                constraints:
                  nullable: false
            - column:
                name: status_observed_generation
                type: int
            - column:
                name: status_shard_id
                type: varchar(50)
            - column:
                name: status_hostname
                type: varchar(255)
            - column:
                name: status_health
                type: jsonb
            - column:
                name: status_conditions
                type: jsonb
            - column:
                name: deletion_timestamp
                type: timestamptz
            - column:
                name: favorite
                type: boolean
        sql: |
          CREATE UNIQUE INDEX argo_cd_instance_name_user_owner ON argo_cd_instance (user_owner, name)
          WHERE argo_cd_instance.organization_owner IS NULL;

          CREATE UNIQUE INDEX argo_cd_instance_name_organization_owner ON argo_cd_instance (organization_owner, name)
          WHERE argo_cd_instance.user_owner IS NULL;
  - changeSet:
      id: 4
      author: remington
      changes:
        sql: |
          ALTER TABLE argo_cd_instance ADD CONSTRAINT argo_cd_instance_one_owner
          CHECK ((user_owner IS NULL AND organization_owner IS NOT NULL)
          OR (user_owner IS NOT NULL AND organization_owner IS NULL))
  - changeSet:
      id: 5
      author: remington
      changes:
        createTable:
          tableName: instance_role
          columns:
            - column:
                name: instance_id
                type: varchar(50)
                constraints:
                  foreignKeyName: fk_instance_role_instance_id
                  references: argo_cd_instance(id)
            - column:
                name: user_id
                type: varchar(50)
                constraints:
                  foreignKeyName: fk_instance_role_user_id
                  references: akuity_user(id)
            - column:
                name: role
                type: varchar(50)
                constraints:
                  nullable: false
  - changeSet:
      id: 6
      author: remington
      changes:
        createTable:
          tableName: organization_user
          columns:
            - column:
                name: id
                type: varchar(22)
                constraints:
                  primaryKey: true
                  nullable: false
            - column:
                name: user_id
                type: varchar(50)
                constraints:
                  foreignKeyName: fk_organization_user_user_id
                  references: akuity_user(id)
                  primaryKey: true
                  nullable: false
            - column:
                name: organization_id
                type: varchar(50)
                constraints:
                  foreignKeyName: fk_organization_user_organization_id
                  references: organization(id)
                  primaryKey: true
                  nullable: false
            - column:
                name: organization_role
                type: varchar(50)
                constraints:
                  nullable: false
  - changeSet:
      id: 7
      author: remington
      changes:
        createTable:
          tableName: organization_invite
          columns:
            - column:
                name: id
                type: varchar(50)
                constraints:
                  primaryKey: true
                  nullable: false
            - column:
                name: expiration_time
                type: timestamptz
                constraints:
                  nullable: false
            - column:
                name: invitee_email
                type: varchar(254)
                constraints:
                  nullable: false
            - column:
                name: organization_id
                type: varchar(50)
                constraints:
                  foreignKeyName: fk_organization_invite_organization_id
                  references: organization(id)
                  nullable: false
            - column:
                name: organization_role
                type: varchar(50)
                constraints:
                  nullable: false
            - column:
                name: invited_by_email
                type: varchar(254)
                constraints:
                  nullable: false
            - column:
                name: active
                type: boolean
  - changeSet:
      id: 8
      author: remington
      changes:
        createTable:
          tableName: argo_cd_cluster
          columns:
            - column:
                name: id
                type: varchar(50)
                constraints:
                  primaryKey: true
                  nullable: false
            - column:
                name: instance_id
                type: varchar(50)
                constraints:
                  foreignKeyName: fk_argo_cd_cluster_instance_id
                  references: argo_cd_instance(id)
                  nullable: false
            - column:
                name: name
                type: varchar(50)
                constraints:
                  nullable: false
            - column:
                name: sequence_id
                type: int
                constraints:
                  nullable: false
            - column:
                name: private_spec
                type: text
            - column:
                name: status_manifests
                type: text
            - column:
                name: status_conditions
                type: jsonb
            - column:
                name: status_agent_state
                type: jsonb
            - column:
                name: deletion_timestamp
                type: timestamptz
        addUniqueConstraint:
          columnNames: instance_id, name
          constraintName: unique_cluster_instance
          tableName: argo_cd_cluster
  # Argo CD Cluster Sequence ID constraint
  - changeSet:
      id: 9
      author: terrytangyuan
      changes:
        addUniqueConstraint:
          columnNames: instance_id, sequence_id
          constraintName: unique_cluster_seq_id
          tableName: argo_cd_cluster
  - changeSet:
      id: 10
      author: terrytangyuan
      changes:
        sqlFile:
          path: argo_cd_instance_triggers.sql
          relativeToChangelogFile: true
          splitStatements: false
        rollback: |
          DROP TRIGGER on_inserted_argo_cd_instance ON argo_cd_instance;
  - changeSet:
      id: 11
      author: terrytangyuan
      changes:
        sqlFile:
          path: argo_cd_cluster_triggers.sql
          relativeToChangelogFile: true
          splitStatements: false
        rollback: |
          DROP TRIGGER on_inserted_argo_cd_cluster ON argo_cd_cluster;
          DROP TRIGGER on_updated_argo_cd_cluster ON argo_cd_cluster;
  - changeSet:
      id: 12
      author: terrytangyuan
      changes:
        createTable:
          tableName: argo_cd_instance_config
          columns:
            - column:
                name: instance_id
                type: varchar(50)
                constraints:
                  primaryKey: true
                  foreignKeyName: fk_argo_cd_instance_config_instance_id
                  references: argo_cd_instance(id)
                  nullable: false
            - column:
                name: version
                type: varchar(50)
            - column:
                name: subdomain
                type: varchar(50)
                constraints:
                  unique: true
                  nullable: false
            - column:
                name: argocd_cm
                type: jsonb
            - column:
                name: argocd_secret
                type: text
            - column:
                name: argocd_rbac_cm
                type: jsonb
            - column:
                name: spec
                type: jsonb
                # ip allow list
            - column:
                name: private_spec
                type:
                  text
                  # - column:
                  #     name: k3s_username
                  #     type: varchar(50)
                  # - column:
                  #     name: k3s_password
                  #     type: varchar(100)
                  # - column:
                  #     name: k3s_token
                  #     type: varchar(100)
                  # - column:
                  #     name: fqdn_version
                  #     type: varchar(255)
                  # - column:
                  #     name: webhook_key
                  #     type: varchar
                  # - column:
                  #     name: webhook_cert
                  #     type: varchar
                  # - column:
                #     name: redis_password
                #     type: varchar(100)
  - changeSet:
      id: 13
      author: alexander
      changes:
        sqlFile:
          path: argo_cd_instance_config_triggers.sql
          relativeToChangelogFile: true
          splitStatements: false
        rollback: |
          DROP TRIGGER on_inserted_argo_cd_instance_config ON argo_cd_cluster;
          DROP TRIGGER on_updated_argo_cd_instance_config ON argo_cd_cluster;
  - changeSet:
      id: 14
      author: gdsoumya
      changes:
        sql: |
          ALTER TABLE argo_cd_cluster ADD CONSTRAINT seq_id_check CHECK (sequence_id >= 0 AND sequence_id <= 4811)
  - changeSet:
      id: 15
      author: gdsoumya
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
        - addColumn:
            tableName: akuity_user
            columns:
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
        - addColumn:
            tableName: argo_cd_instance
            columns:
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
  - changeSet:
      id: 16
      author: gdsoumya
      changes:
        - addColumn:
            tableName: akuity_user
            columns:
              - column:
                  name: max_orgs
                  type: int
                  defaultValueNumeric: 5
                  constraints:
                    nullable: false
  - changeSet:
      id: 17
      author: gdsoumya
      changes:
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: generation
                  type: int
                  defaultValueNumeric: 1
                  constraints:
                    nullable: false
              - column:
                  name: status_observed_generation
                  type: int
        - sqlFile:
            path: argo_cd_instance_config_triggers_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
            DROP TRIGGER on_updated_argo_cd_instance_config ON argo_cd_instance_config;
  - changeSet:
      id: 18
      author: sunghoonkang
      changes:
        createTable:
          tableName: api_key
          columns:
            - column:
                name: id
                type: varchar(16)
                constraints:
                  nullable: false
                  primaryKey: true
            - column:
                name: secret_hash
                type: varchar(64)
                constraints:
                  nullable: false
            - column:
                name: secret_salt
                type: varchar(32)
                constraints:
                  nullable: false
            - column:
                name: description
                type: varchar(255)
            - column:
                name: organization
                type: varchar(50)
                constraints:
                  foreignKeyName: fk_akuity_api_key_organization
                  references: organization(id)
            - column:
                name: role
                type: jsonb
                # e.g.
                # {
                #   "some_action": ["read", "write"]
                # }
            - column:
                name: creation_timestamp
                type: timestamptz
                defaultValueComputed: current_timestamp
                constraints:
                  nullable: false
            - column:
                name: expiration_timestamp
                type: timestamptz
                constraints:
                  nullable: true
  - changeSet:
      id: 19
      author: alexander
      changes:
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: spec
                  type: jsonb
  - changeSet:
      id: 20
      author: gdsoumya
      changes:
        - sqlFile:
            path: argo_cd_cluster_triggers_gen_bump.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER bump_gen_on_updated_argo_cd_cluster ON argo_cd_cluster;

  - changeSet:
      id: 21
      author: pavel
      changes:
        - addColumn:
            tableName: argo_cd_instance_config
            columns:
              - column:
                  name: argocd_notifications_cm
                  type: jsonb
              - column:
                  name: argocd_notifications_secret
                  type: text
  - changeSet:
      id: 22
      author: sunghoonkang
      changes:
        - renameColumn:
            tableName: api_key
            oldColumnName: role
            newColumnName: permissions
  - changeSet:
      id: 23
      author: remington
      changes:
        - addColumn:
            tableName: argo_cd_instance
            columns:
              - column:
                  name: status_info
                  type: jsonb
  - changeSet:
      id: 24
      author: alexander
      changes:
        - dropColumn:
            tableName: argo_cd_instance
            columnName: favorite
  - changeSet:
      id: 25
      author: alexander
      changes:
        - createTable:
            tableName: argo_cd_sync_operation
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_instance_role_instance_id
                    references: argo_cd_instance(id)
                    nullable: false
              - column:
                  name: application_name
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: start_time
                  type: timestamptz
                  constraints:
                    nullable: false
              - column:
                  name: end_time
                  type: timestamptz
                  constraints:
                    nullable: false
              - column:
                  name: result_phase
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: result_message
                  type: varchar(500)
                  constraints:
                    nullable: false
              - column:
                  name: details
                  type: jsonb
        - addColumn:
            tableName: argo_cd_instance
            columns:
              - column:
                  name: status_recent_processed_event_id
                  type: int
                  defaultValueNumeric: 0
                  constraints:
                    nullable: false
  - changeSet:
      id: 26
      author: gdsoumya
      changes:
        - createTable:
            tableName: audit_log
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              # instance id for which an argocd event was triggered (only applicable to argocd events)
              - column:
                  name: instance_id
                  type: varchar(50)
              # org id to which the event belongs to
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_audit_log_organization_id
                    references: organization(id)
              - column:
                  name: timestamp
                  type: timestamptz
                  constraints:
                    nullable: false
              - column:
                  name: action
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: object
                  type: jsonb
                  constraints:
                    nullable: false
              - column:
                  name: details
                  type: jsonb
              - column:
                  name: actor
                  type: jsonb
                  constraints:
                    nullable: false
  - changeSet:
      id: 27
      author: jiachengxu
      changes:
        - addColumn:
            tableName: akuity_user
            columns:
              - column:
                  name: user_info
                  type: jsonb
  - changeSet:
      id: 28
      author: pavel
      changes:
        - addColumn:
            tableName: akuity_user
            columns:
              - column:
                  name: max_applications
                  type: int
                  defaultValueNumeric: 20
                  constraints:
                    nullable: false
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: max_applications
                  type: int
                  defaultValueNumeric: 20
                  constraints:
                    nullable: false
  - changeSet:
      id: 29
      author: gdsoumya
      changes:
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: namespace
                  type: varchar(63)
                  defaultValue: "akuity"
                  constraints:
                    nullable: false
  - changeSet:
      id: 30
      validCheckSum:
        - "9:f48bbe0b9339f242531e2fcdbc6922da"
        - "9:9801893f79999439b0da143d7c67b559"
      author: jiachengxu
      changes:
        - sql: |
            SELECT 1;
        - modifyDataType:
            tableName: akuity_user
            columnName: email
            newDataType: varchar(254)
        - modifyDataType:
            tableName: organization_invite
            columnName: invitee_email
            newDataType: varchar(254)
        - modifyDataType:
            tableName: organization_invite
            columnName: invited_by_email
            newDataType: varchar(254)
  - changeSet:
      id: 31
      author: gdsoumya
      changes:
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: auto_upgrade_disabled
                  type: boolean
                  defaultValue: false
                  constraints:
                    nullable: false
        - sqlFile:
            path: argo_cd_cluster_triggers_gen_bump_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER bump_gen_on_updated_argo_cd_cluster ON argo_cd_cluster;
  - changeSet:
      id: 32
      author: alexmt
      changes:
        - addColumn:
            tableName: argo_cd_instance_config
            columns:
              - column:
                  name: internal_spec
                  type: jsonb
  - changeSet:
      id: 33
      author: gdsoumya
      changes:
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: namespace_scoped
                  type: boolean
                  defaultValue: false
                  constraints:
                    nullable: false
  - changeSet:
      id: 34
      author: alexmt
      changes:
        - addColumn:
            tableName: akuity_user
            columns:
              - column:
                  name: ui_preferences
                  type: jsonb
  - changeSet:
      id: 35
      author: Marvin9
      changes:
        - addColumn:
            tableName: argo_cd_instance
            columns:
              - column:
                  name: description
                  type: varchar(255)
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: description
                  type: varchar(255)
  - changeSet:
      id: 36
      author: alex
      changes:
        - dropDefaultValue:
            columnName: max_applications
            tableName: akuity_user
        - dropDefaultValue:
            columnName: max_applications
            tableName: organization
  - changeSet:
      id: 37
      author: pavel
      changes:
        - dropColumn:
            tableName: audit_log
            columnName: instance_id
        - sql: |
            CREATE INDEX audit_log_action ON audit_log (action);
            CREATE INDEX audit_log_timestamp ON audit_log (timestamp);
            CREATE INDEX audit_log_organization_id ON audit_log(organization_id);
  - changeSet:
      id: 38
      author: gdsoumya
      validCheckSum:
        - "9:c7515b0c3674565de2267a36608c1037"
        - "9:7502183ae2f8ac43debce6db12d53e5b"
      changes:
        - createTable:
            tableName: billing
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: customer_id
                  type: varchar(100)
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: billing_email
                  type: varchar(254)
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_audit_log_organization_id
                    references: organization(id)
                    nullable: false
                    # 1:1 mapping between customer_id and org is expected,
                    # if the customer changes we should delete the old entry and create new entry
                    unique: true
              - column:
                  name: billing_authority
                  type: varchar(10)
                  constraints:
                    nullable: false
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: generation
                  type: int
                  defaultValueNumeric: 1
                  constraints:
                    nullable: false
          addColumn:
            tableName: organization
            columns:
              - column:
                  name: org_status
                  type: jsonb
          sqlFile:
            path: billing_triggers.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER on_updated_billing ON billing;
  - changeSet:
      id: 39
      author: alexander
      changes:
        - dropColumn:
            tableName: organization
            columnName: memberCount
  - changeSet:
      id: 40
      author: remington
      changes:
        - addColumn:
            tableName: billing
            columns:
              - column:
                  name: billing_metadata
                  type: text
  - changeSet:
      id: 41
      author: evgeny
      changes:
        - addColumn:
            tableName: akuity_user
            columns:
              - column:
                  name: user_info_private
                  type: text
  - changeSet:
      id: 42
      author: alexmt
      changes:
        - sqlFile:
            path: argo_cd_instance_triggers_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: argo_cd_instance_config_triggers_v3.sql
            relativeToChangelogFile: true
            splitStatements: false
        - dropColumn:
            tableName: argo_cd_instance
            columnName: user_owner
        - dropColumn:
            tableName: akuity_user
            columnName: max_instances
        - dropColumn:
            tableName: akuity_user
            columnName: max_clusters
        - dropColumn:
            tableName: akuity_user
            columnName: max_applications
        - addNotNullConstraint:
            tableName: argo_cd_instance
            columnName: organization_owner
  - changeSet:
      id: 43
      author: evgeny
      changes:
        - sql: |
            CREATE INDEX argo_cd_sync_operation_instance_id ON argo_cd_sync_operation (instance_id);
  - changeSet:
      id: 44
      author: pavel
      changes:
        - addColumn:
            tableName: argo_cd_instance_config
            columns:
              - column:
                  name: argocd_image_updater_enable
                  type: bool
              - column:
                  name: argocd_image_updater_cm
                  type: jsonb
              - column:
                  name: argocd_image_updater_ssh_cm
                  type: jsonb
              - column:
                  name: argocd_image_updater_secret
                  type: text
        - sqlFile:
            path: argo_cd_instance_config_triggers_v4.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
            DROP TRIGGER on_updated_argo_cd_instance_config ON argo_cd_instance_config;
  - changeSet:
      id: 45
      author: evgeny
      changes:
        - sql: |
            DROP INDEX argo_cd_sync_operation_instance_id;
        - addColumn:
            tableName: akuity_user
            columns:
              - column:
                  name: last_activity_timestamp
                  type: timestamptz
              - column:
                  name: last_location
                  type: varchar(64)
  - changeSet:
      id: 46
      author: pavel
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v5.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER on_inserted_argo_cd_instance_config ON argo_cd_instance_config;
            DROP TRIGGER on_updated_argo_cd_instance_config ON argo_cd_instance_config;
  - changeSet:
      id: 47
      author: remington
      changes:
        - dropNotNullConstraint:
            tableName: billing
            columnName: billing_email
        - addColumn:
            tableName: billing
            columns:
              - column:
                  name: manual
                  type: bool
                  defaultValue: false
  - changeSet:
      id: 48
      author: alex
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v6.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 49
      author: sunghoonkang
      changes:
        - createTable:
            tableName: organization_sso_realm
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_organization_sso_realm_organization_id
                    references: organization(id)
                    nullable: false
              - column:
                  name: domain
                  type: varchar(255)
                  constraints:
                    nullable: false
                    unique: true
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: sso_connection_strategy
                  type: varchar(64)
                  constraints:
                    nullable: true
              - column:
                  name: sso_auto_add_member
                  type: boolean
  - changeSet:
      id: 50
      author: remington
      changes:
        - addColumn:
            tableName: billing
            columns:
              - column:
                  name: inactive
                  type: bool
                  defaultValue: false
  - changeSet:
      id: 51
      author: sunghoonkang
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: feature_gates
                  type: jsonb
  - changeSet:
      id: 52
      author: remington
      changes:
        - addColumn:
            tableName: argo_cd_instance_config
            columns:
              - column:
                  name: argocd_appset_secret
                  type: text
  - changeSet:
      id: 53
      author: alex
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v7.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 54
      author: evgeny
      changes:
        - addColumn:
            tableName: argo_cd_instance
            columns:
              - column:
                  name: status_recent_processed_event_info
                  type: jsonb
  - changeSet:
      id: 55
      author: evgeny
      changes:
        - dropColumn:
            tableName: argo_cd_instance
            columns:
              - column:
                  name: status_recent_processed_event_id
                  type: jsonb
  - changeSet:
      id: 56
      author: pavel
      changes:
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: status_info
                  type: jsonb
  - changeSet:
      id: 56
      author: gdsoumya
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v8.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 56
      author: alex
      changes:
        - sqlFile:
            path: argo_cd_instance_triggers_v3.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 57
      author: alex
      changes:
        - sqlFile:
            path: argo_cd_instance_triggers_v4.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 58
      author: goldin
      changes:
        - modifyDataType:
            tableName: argo_cd_sync_operation
            columnName: application_name
            newDataType: varchar(253)
  - changeSet:
      id: 59
      author: goldin
      changes:
        - dropColumn:
            tableName: akuity_user
            columnName: user_info
  - changeSet:
      id: 60
      author: jiachengxu
      changes:
        - addColumn:
            tableName: argo_cd_instance_config
            columns:
              - column:
                  name: argocd_config_management_plugins
                  type: jsonb
        - sqlFile:
            path: argo_cd_instance_config_triggers_v9.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 61
      author: gdsoumya
      changes:
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: status_observed_cred_rotation
                  type: int
  - changeSet:
      id: 62
      author: Marvin9
      changes:
        - addColumn:
            tableName: akuity_user
            columns:
              - column:
                  name: internal_metadata
                  type: jsonb
        - sql: |
            UPDATE akuity_user SET internal_metadata = '{ "added_in_free_trial_list": true }'
  - changeSet:
      id: 63
      author: jiachengxu
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v10.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 64
      author: gdsoumya
      changes:
        - sqlFile:
            path: argo_cd_cluster_fix_target_version.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER bump_gen_on_updated_argo_cd_cluster ON argo_cd_cluster;
  - changeSet:
      id: 65
      author: bikramnehra
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v11.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 66
      author: aborilov
      changes:
        - addColumn:
            tableName: argo_cd_instance
            columns:
              - column:
                  name: shard
                  type: varchar(255)
        - sqlFile:
            path: argo_cd_instance_config_triggers_v12.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: argo_cd_instance_triggers_v5.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: argo_cd_cluster_triggers_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 67
      author: aborilov
      changes:
        - addNotNullConstraint:
            columnDataType: varchar(255)
            columnName: shard
            constraintName: shard_not_null
            defaultNullValue: ""
            tableName: argo_cd_instance
            validate: true
  - changeSet:
      id: 68
      author: evgeny
      changes:
        - dropColumn:
            tableName: akuity_user
            columnName: internal_metadata
  - changeSet:
      id: 69
      author: alexander
      changes:
        - addDefaultValue:
            tableName: argo_cd_instance
            columnName: shard
            defaultValue: ""
  - changeSet:
      id: 70
      author: alex/gdsoumya
      changes:
        - createTable:
            tableName: kargo_instance
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: name
                  type: varchar(50)
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: organization_owner
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_argo_cd_instance_organization_owner
                    references: organization(id)
              - column:
                  name: generation
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: status_observed_generation
                  type: int
              - column:
                  name: status_hostname
                  type: varchar(255)
              - column:
                  name: status_health
                  type: jsonb
              - column:
                  name: status_conditions
                  type: jsonb
              - column:
                  name: deletion_timestamp
                  type: timestamptz
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: status_info
                  type: jsonb
        - createTable:
            tableName: kargo_instance_config
            columns:
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    foreignKeyName: fk_kargo_instance_config_instance_id
                    references: kargo_instance(id)
                    nullable: false
              - column:
                  name: version
                  type: varchar(50)
              - column:
                  name: spec
                  type: jsonb
              - column:
                  name: private_spec
                  type: text
              - column:
                  name: internal_spec
                  type: jsonb
        - createTable:
            tableName: kargo_cluster
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_kargo_cluster_instance_id
                    references: kargo_instance(id)
                    nullable: false
              - column:
                  name: name
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(255)
              - column:
                  name: private_spec
                  type: text
              - column:
                  name: status_manifests
                  type: text
              - column:
                  name: status_conditions
                  type: jsonb
              - column:
                  name: status_agent_state
                  type: jsonb
              - column:
                  name: deletion_timestamp
                  type: timestamptz
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: generation
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: status_observed_generation
                  type: int
              - column:
                  name: spec
                  type: jsonb
              - column:
                  name: namespace
                  type: varchar(63)
                  defaultValue: "akuity"
                  constraints:
                    nullable: false
              - column:
                  name: auto_upgrade_disabled
                  type: boolean
                  defaultValue: false
                  constraints:
                    nullable: false
              - column:
                  name: status_observed_cred_rotation
                  type: int
        - sqlFile:
            path: kargo_instance_config_triggers.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: kargo_instance_triggers.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: kargo_cluster_triggers.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: kargo_cluster_triggers_gen_bump.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 71
      author: gdsoumya
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v13.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 72
      author: aborilov
      changes:
        - sqlFile:
            path: argo_cd_instance_k3s_version_upgrade.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 73
      author: remington
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: oidc_map
                  type: jsonb
  - changeSet:
      id: 74
      author: evgeny
      changes:
        - createTable:
            tableName: audit_log_archive
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_audit_log_archives_organization_id
                    references: organization(id)
                    nullable: false
              - column:
                  name: start_date
                  type: timestamptz
                  constraints:
                    nullable: false
              - column:
                  name: end_date
                  type: timestamptz
                  constraints:
                    nullable: false
              - column:
                  name: start_record
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_audit_log_archives_start_record
                    references: audit_log(id)
                    nullable: false
              - column:
                  name: end_record
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_audit_log_archives_end_record
                    references: audit_log(id)
                    nullable: false
              - column:
                  name: records
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: days
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: path
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: checksum
                  type: varchar(44)
                  constraints:
                    nullable: false
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
  - changeSet:
      id: 75
      author: gdsoumya
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v14.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 76
      author: evgeny
      changes:
        sql: |
          CREATE UNIQUE INDEX audit_log_archive_path ON audit_log_archive (path)
  - changeSet:
      id: 77
      author: bikramnehra
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v15.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 78
      author: gdsoumya
      changes:
        - dropTable:
            cascadeConstraints: true
            tableName: kargo_cluster
        - dropTable:
            cascadeConstraints: true
            tableName: kargo_instance_config
        - dropTable:
            cascadeConstraints: true
            tableName: kargo_instance
        - createTable:
            tableName: kargo_instance
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: name
                  type: varchar(50)
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: shard
                  type: varchar(255)
                  constraints:
                    nullable: false
                  defaultValue: ""
              - column:
                  name: organization_owner
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_argo_cd_instance_organization_owner
                    references: organization(id)
              - column:
                  name: generation
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: status_observed_generation
                  type: int
              - column:
                  name: status_hostname
                  type: varchar(255)
              - column:
                  name: status_health
                  type: jsonb
              - column:
                  name: status_conditions
                  type: jsonb
              - column:
                  name: deletion_timestamp
                  type: timestamptz
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: status_info
                  type: jsonb
              - column:
                  name: description
                  type: varchar(255)
        - createTable:
            tableName: kargo_instance_config
            columns:
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    foreignKeyName: fk_kargo_instance_config_instance_id
                    references: kargo_instance(id)
                    nullable: false
              - column:
                  name: version
                  type: varchar(50)
              - column:
                  name: spec
                  type: jsonb
              - column:
                  name: private_spec
                  type: text
              - column:
                  name: internal_spec
                  type: jsonb
              - column:
                  name: controller_cm
                  type: jsonb
              - column:
                  name: webhook_cm
                  type: jsonb
              - column:
                  name: oidc_config
                  type: jsonb
              - column:
                  name: api_secret
                  type: text
              - column:
                  name: api_cm
                  type: jsonb
        - createTable:
            tableName: kargo_agent
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_kargo_agent_instance_id
                    references: kargo_instance(id)
                    nullable: false
              - column:
                  name: remote_argocd_instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_remote_argocd_instance_id
                    references: argo_cd_instance(id)
                    nullable: true
              - column:
                  name: name
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(255)
              - column:
                  name: private_spec
                  type: text
              - column:
                  name: status_manifests
                  type: text
              - column:
                  name: status_conditions
                  type: jsonb
              - column:
                  name: status_agent_state
                  type: jsonb
              - column:
                  name: deletion_timestamp
                  type: timestamptz
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: generation
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: status_observed_generation
                  type: int
              - column:
                  name: spec
                  type: jsonb
              - column:
                  name: namespace
                  type: varchar(63)
                  defaultValue: "akuity"
                  constraints:
                    nullable: false
              - column:
                  name: auto_upgrade_disabled
                  type: boolean
                  defaultValue: false
                  constraints:
                    nullable: false
              - column:
                  name: status_observed_cred_rotation
                  type: int
        - sqlFile:
            path: kargo_instance_config_triggers_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: kargo_instance_triggers_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: kargo_agent_triggers_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: kargo_agent_triggers_gen_bump.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 79
      author: goldin
      changes:
        - addColumn:
            tableName: audit_log
            columns:
              - column:
                  name: is_deleted
                  type: boolean
                  defaultValue: false
                  constraints:
                    nullable: false
  - changeSet:
      id: 80
      author: sunghoonkang
      changes:
        - createTable:
            tableName: organization_plan
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: name
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: default_plan
                  type: boolean
                  defaultValueBoolean: false
              - column:
                  name: features
                  type: jsonb
              - column:
                  name: quota
                  type: jsonb
        - sql: |
            CREATE UNIQUE INDEX organization_plan_default_plan ON organization_plan (default_plan)
            WHERE organization_plan.default_plan = true;
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: plan
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_organization_plan
                    references: organization_plan(id)
                    nullable: true
  - changeSet:
      id: 81
      author: jiachengxu
      changes:
        - createTable:
            tableName: team
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_team_organization_id
                    references: organization(id)
                    nullable: false
              - column:
                  name: name
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(255)
        - createTable:
            tableName: team_user
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: team_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_team_user_team_id
                    references: team(id)
                    nullable: false
              - column:
                  name: user_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_team_user_user_id
                    references: akuity_user(id)
                    nullable: false
  - changeSet:
      id: 82
      author: gdsoumya
      changes:
        - addColumn:
            tableName: kargo_instance_config
            columns:
              - column:
                  name: subdomain
                  type: varchar(50)
                  constraints:
                    unique: true
                    nullable: true

  - changeSet:
      id: 83
      author: jiachengxu
      changes:
        - addColumn:
            tableName: team_user
            columns:
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_team_organization_id
                    references: organization(id)
                    nullable: false
  - changeSet:
      id: 84
      author: pavel
      changes:
        - createTable:
            tableName: custom_role
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: name
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(255)
              - column:
                  name: policy
                  type: text
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_organization_user_organization_id
                    references: organization(id)
                    primaryKey: true
                    nullable: false
  - changeSet:
      id: 85
      author: remington
      changes:
        - dropForeignKeyConstraint:
            baseTableName: organization
            constraintName: fk_organization_plan
        - dropPrimaryKey:
            tableName: organization_plan
        - addPrimaryKey:
            tableName: organization_plan
            columnNames: name
        - addUniqueConstraint:
            tableName: organization_plan
            columnNames: name
            constraintName: unique_organization_plan_name
        - dropNotNullConstraint:
            tableName: organization_plan
            columnName: id
        - addForeignKeyConstraint:
            baseColumnNames: plan
            baseTableName: organization
            constraintName: fk_organization_plan
            referencedColumnNames: name
            referencedTableName: organization_plan
        - addColumn:
            tableName: organization_plan
            columns:
              - column:
                  name: billing_authority
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: product_id
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: display_name
                  type: varchar(50)
                  constraints:
                    nullable: false
  - changeSet:
      id: 86
      author: remington
      changes:
        - createTable:
            tableName: addon_plan
            columns:
              - column:
                  name: included_with_plan
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_plan_addon_included_with_plan
                    references: organization_plan(name)
                    nullable: true
              - column:
                  name: included_quantity
                  type: int
                  defaultValueNumeric: 0
                  constraints:
                    nullable: true
              - column:
                  name: name
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: product_id
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: min_quantity
                  type: int
                  defaultValueNumeric: 0
                  constraints:
                    nullable: false
              - column:
                  name: max_quantity
                  type: int
                  defaultValueNumeric: 100
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(255)
              - column:
                  name: quota
                  type: jsonb
              - column:
                  name: display_name
                  type: varchar(50)
                  constraints:
                    nullable: false
  - changeSet:
      id: 87
      author: pavel
      validCheckSum:
        - "9:435d8a3f141f48fe4f734b92b7932194"
        - "9:7999ac44178f47b4a13c1110611c5444"
      changes:
        - addColumn:
            tableName: audit_log
            columns:
              - column:
                  name: count
                  type: int
                  defaultValue: 1
                  constraints:
                    nullable: false
              - column:
                  name: is_aggregated
                  type: bool
                  defaultValue: false
                  constraints:
                    nullable: false
              - column:
                  name: last_occurred_timestamp
                  type: timestamptz
                  constraints:
                    nullable: true
        - sql: |
            CREATE INDEX audit_log_timestamp_is_aggregated ON audit_log (timestamp, is_aggregated);
  - changeSet:
      id: 88
      author: gdsoumya
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: max_kargo_projects
                  type: int
                  defaultValueNumeric: 0
                  constraints:
                    nullable: false
              - column:
                  name: max_kargo_instances
                  type: int
                  defaultValueNumeric: 0
                  constraints:
                    nullable: false
              - column:
                  name: max_kargo_agents
                  type: int
                  defaultValueNumeric: 0
                  constraints:
                    nullable: false
  - changeSet:
      id: 89
      author: jiachengxu
      changes:
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: readonly_settings_changed_generation
                  type: int
        - sqlFile:
            path: argo_cd_cluster_triggers_gen_bump_v3.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: argo_cd_instance_config_triggers_v16.sql
            relativeToChangelogFile: true
            splitStatements: false
        - addColumn:
            tableName: kargo_agent
            columns:
              - column:
                  name: readonly_settings_changed_generation
                  type: int
        - sqlFile:
            path: kargo_agent_triggers_gen_bump_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 90
      author: sunghoonkang
      changes:
        - createTable:
            tableName: workspace
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_workspace_organization_id
                    references: organization(id)
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(255)
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
        - createTable:
            tableName: workspace_member
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: workspace_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_workspace_member_workspace_id
                    references: workspace(id)
                    nullable: false
              - column:
                  name: user_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_workspace_member_user_id
                    references: akuity_user(id)
                    nullable: true
              - column:
                  name: team_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_workspace_member_team_id
                    references: team(id)
                    nullable: true
              - column:
                  name: role
                  type: varchar(50)
                  constraints:
                    nullable: false
        - sql: |
            ALTER TABLE workspace_member ADD CONSTRAINT workspace_member_reference
            CHECK ((user_id IS NOT NULL AND team_id IS NULL)
            OR (team_id IS NOT NULL AND user_id IS NULL))
  - changeSet:
      id: 91
      author: jiachengxu
      changes:
        - addColumn:
            tableName: argo_cd_instance
            columns:
              - column:
                  name: workspace_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_argo_cd_instance_workspace_id
                    references: workspace(id)
                    nullable: true
        - addColumn:
            tableName: kargo_instance
            columns:
              - column:
                  name: workspace_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_kargo_instance_workspace_id
                    references: workspace(id)
                    nullable: true
  - changeSet:
      id: 92
      author: gdsoumya
      changes:
        - sqlFile:
            path: kargo_instance_config_triggers_v3.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 93
      author: remington
      changes:
        - addColumn:
            tableName: argo_cd_instance
            columns:
              - column:
                  name: backup
                  type: jsonb
  - changeSet:
      id: 94
      author: aborilov
      changes:
        - sqlFile:
            path: argo_cd_cluster_triggers_v3.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 95
      author: gdsoumya
      changes:
        - addColumn:
            tableName: kargo_instance_config
            columns:
              - column:
                  name: miscellaneous_secrets
                  type: text
  - changeSet:
      id: 96
      author: alexmt
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v17.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 97
      author: gdsoumya
      changes:
        - addColumn:
            tableName: kargo_instance
            columns:
              - column:
                  name: status_recent_processed_event_info
                  type: jsonb
  - changeSet:
      id: 98
      author: sunghoonkang
      changes:
        - addColumn:
            tableName: akuity_user
            columns:
              - column:
                  name: last_password_reset_timestamp
                  type: timestamptz
                  constraints:
                    nullable: true
  - changeSet:
      id: 99
      author: gdsoumya
      changes:
        - sql: |
            update
                audit_log
            set details = jsonb_set(details,'{patch}',to_jsonb(to_json((((details->'patch'#>>'{}')::jsonb) - 'status_manifests')::text)), false)
            where
                ((details->'patch'#>>'{}')::json)->>'status_manifests' is not null
  - changeSet:
      id: 100
      author: pavel
      changes:
        - addColumn:
            tableName: argo_cd_sync_operation
            columns:
              - column:
                  name: count
                  type: int
                  defaultValue: 1
                  constraints:
                    nullable: false
              - column:
                  name: is_aggregated
                  type: bool
                  defaultValue: false
                  constraints:
                    nullable: false
              - column:
                  name: last_occurred_timestamp
                  type: timestamptz
                  constraints:
                    nullable: true
              - column:
                  name: duration
                  type: int
                  constraints:
                    nullable: true
        - sql: |
            CREATE INDEX argo_cd_sync_operation_start_time_is_aggregated ON argo_cd_sync_operation (start_time, is_aggregated);
  - changeSet:
      id: 101
      author: gdsoumya
      changes:
        - createTable:
            tableName: kargo_promotions
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_kargo_role_instance_id
                    references: kargo_instance(id)
                    nullable: false
              - column:
                  name: project_name
                  type: varchar(253)
                  constraints:
                    nullable: false
              - column:
                  name: stage_name
                  type: varchar(253)
                  constraints:
                    nullable: false
              - column:
                  name: promotion_name
                  type: varchar(253)
                  constraints:
                    nullable: false
              - column:
                  name: start_time
                  type: timestamptz
                  constraints:
                    nullable: false
              - column:
                  name: end_time
                  type: timestamptz
                  constraints:
                    nullable: false
              - column:
                  name: result_phase
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: result_message
                  type: varchar(500)
                  constraints:
                    nullable: false
              - column:
                  name: details
                  type: jsonb
  - changeSet:
      id: 102
      author: hanxiaop
      changes:
        addUniqueConstraint:
          columnNames: organization_id, name
          constraintName: unique_organization_workspace_name
          tableName: workspace
  - changeSet:
      id: 103
      author: jiachengxu
      changes:
        - addColumn:
            tableName: akuity_user
            columns:
              - column:
                  name: notification_settings
                  type: jsonb
        - createTable:
            tableName: event
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: event_type
                  type: varchar(50)
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_event_organization_id
                    references: organization(id)
                    # Organization can be null if this event is not tied to any organization, e.g., marketing/sales events, platform events, etc.
                    nullable: true
              - column:
                  name: metadata
                  type: jsonb
        - createTable:
            tableName: notification
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: event_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_notification_event_id
                    references: event(id)
                    nullable: false
                    # Set the cascade delete to true so that when an event is deleted, all notifications tied to that event are also deleted.
                    # This will be used to make the garbage collection of events and notifications easier.
                    deleteCascade: true
              - column:
                  name: target_id
                  type: varchar(50)
              - column:
                  name: delivery_method
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: status_is_delivered
                  type: boolean
                  defaultValue: false
                  constraints:
                    nullable: false
        - sqlFile:
            path: notification_triggers.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sql: |
            CREATE INDEX notification_target_id ON notification (target_id);
            CREATE INDEX notification_delivery_method ON notification (delivery_method);
            CREATE INDEX notification_status_is_delivered ON notification (status_is_delivered);
  - changeSet:
      id: 104
      author: Marvin9
      changes:
        createTable:
          tableName: internal_audit
          columns:
            - column:
                name: id
                type: varchar(50)
                constraints:
                  nullable: false
                  primaryKey: true
            - column:
                name: timestamp
                type: timestamptz
                constraints:
                  nullable: false
            - column:
                name: action
                type: varchar(50)
                constraints:
                  nullable: false
            - column:
                name: object
                type: jsonb
                constraints:
                  nullable: false
            - column:
                name: details
                type: jsonb
            - column:
                name: actor
                type: jsonb
                constraints:
                  nullable: false
  - changeSet:
      id: 105
      author: jiachengxu
      changes:
        - addColumn:
            tableName: workspace
            columns:
              - column:
                  name: is_default
                  type: boolean
                  defaultValue: false
                  constraints:
                    nullable: false
        - addColumn:
            tableName: workspace_member
            columns:
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_workspace_member_organization_id
                    references: organization(id)
                    nullable: false
        - sqlFile:
            path: argo_cd_instance_config_triggers_v18.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: argo_cd_instance_triggers_v6.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: kargo_instance_config_triggers_v4.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: kargo_instance_triggers_v3.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 106
      author: jiachengxu
      validCheckSum:
        - "9:4f7d69f81d41d99e8a1428516799fcc0"
        - "9:6cdc8f5d52b61fa02387d9a08c0534af"
      changes:
        - addColumn:
            tableName: api_key
            columns:
              - column:
                  name: workspace_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_api_key_workspace_id
                    references: workspace(id)
                    nullable: true
        - addColumn:
            tableName: workspace_member
            columns:
              - column:
                  name: permissions
                  type: jsonb
        - sqlFile:
            path: workspace-migration.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 107
      author: hanxiaop
      changes:
        - createTable:
            tableName: argo_cd_cluster_k8s_object
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: cluster_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_argo_cd_cluster_id
                    references: argo_cd_cluster(id)
                    nullable: false
                    deleteCascade: true
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_argo_cd_cluster_k8s_object_organization_id
                    references: organization(id)
                    nullable: false
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_argo_cd_cluster_k8s_object_instance_id
                    references: argo_cd_instance(id)
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: namespace
                  type: varchar(255)
              - column:
                  name: group
                  type: varchar(255)
              - column:
                  name: version
                  type: varchar(255)
              - column:
                  name: kind
                  type: varchar(255)
              - column:
                  name: argocd_application_info
                  type: jsonb
              - column:
                  name: columns
                  type: jsonb
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  constraints:
                    nullable: false
        - createIndex:
            tableName: argo_cd_cluster_k8s_object
            indexName: idx_k8s_object_org_instance_kind
            columns:
              - column:
                  name: organization_id
              - column:
                  name: instance_id
              - column:
                  name: group
              - column:
                  name: kind
  - changeSet:
      id: 108
      author: pavel
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: max_workspaces
                  type: int
                  defaultValueNumeric: 0
                  constraints:
                    nullable: false
        - sql: |
            UPDATE ORGANIZATION SET max_kargo_agents=3 WHERE max_kargo_agents=0;
            UPDATE ORGANIZATION SET max_kargo_projects=5 WHERE max_kargo_projects=0;
            UPDATE ORGANIZATION SET max_kargo_instances=1 WHERE max_kargo_instances=0;
            UPDATE ORGANIZATION SET max_workspaces=1;
  - changeSet:
      id: 109
      author: pavel
      changes:
        - sqlFile:
            path: workspace-migration_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 110
      author: gdsoumya
      changes:
        - addColumn:
            tableName: event
            columns:
              - column:
                  name: status_processed
                  type: boolean
                  defaultValue: false
                  constraints:
                    nullable: false
        - addColumn:
            tableName: notification
            columns:
              - column:
                  name: status_metadata
                  type: jsonb
              - column:
                  # in case we want the ability to re-trigger notifications for eg. webhooks
                  name: generation
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: status_failed_delivery
                  type: boolean
                  defaultValue: false
                  constraints:
                    nullable: false
        - sqlFile:
            path: notification_triggers_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: event_triggers.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 111
      author: yiwei
      changes:
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: status_k8s_info
                  type: jsonb
  - changeSet:
      id: 112
      author: sunghoonkang
      changes:
        - createTable:
            tableName: organization_notification_config
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_organization_webhook_config_organization_id
                    references: organization(id)
                    nullable: false
              - column:
                  name: delivery_method
                  type: varchar(50)
                  # e.g. "webhook", "email"
                  constraints:
                    nullable: false
              - column:
                  name: events
                  type: jsonb
                  # e.g.
                  # All events: ["*"]
                  # Specific events: ["event_type"]
              - column:
                  name: config
                  type: jsonb
  - changeSet:
      id: 113
      author: yiwei
      changes:
        - addColumn:
            tableName: argo_cd_cluster_k8s_object
            columns:
              - column:
                  name: owner_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_argo_cd_cluster_k8s_object_id
                    references: argo_cd_cluster_k8s_object(id)
                    deleteCascade: true
              - column:
                  name: children_count
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
  - changeSet:
      id: 114
      author: yiwei
      changes:
        - dropForeignKeyConstraint:
            baseTableName: argo_cd_cluster_k8s_object
            constraintName: fk_argo_cd_cluster_k8s_object_id
        - createIndex:
            tableName: argo_cd_cluster_k8s_object
            indexName: idx_argo_cd_cluster_k8s_object_owner_id
            columns:
              - column:
                  name: owner_id
  - changeSet:
      id: 115
      author: sunghoonkang
      changes:
        - addColumn:
            tableName: organization_notification_config
            columns:
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
  - changeSet:
      id: 116
      author: sunghoonkang
      changes:
        - addColumn:
            tableName: organization_notification_config
            columns:
              - column:
                  name: name
                  type: varchar(50)
                  constraints:
                    nullable: false
                  defaultValue: ""
        - addUniqueConstraint:
            tableName: organization_notification_config
            columnNames: organization_id, delivery_method, name
            constraintName: unique_organization_notification_config_name_by_delivery_method
  - changeSet:
      id: 117
      author: sunghoonkang
      changes:
        - addColumn:
            tableName: notification
            columns:
              - column:
                  name: last_delivery_timestamp
                  type: timestamptz
                  constraints:
                    nullable: true
  - changeSet:
      id: 118
      author: pavel
      changes:
        - addColumn:
            tableName: argo_cd_instance_config
            columns:
              - column:
                  name: fqdn
                  type: varchar(253)
                  constraints:
                    unique: true
                    nullable: true
        - sql: |
            UPDATE argo_cd_instance_config
            SET fqdn = COALESCE(NULLIF(spec->>'fqdn', ''), NULL)
  - changeSet:
      id: 119
      author: jiachengxu
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v19.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 120
      author: gdsoumya
      changes:
        - dropUniqueConstraint:
            constraintName: kargo_instance_name_key
            tableName: kargo_instance
            uniqueColumns: name
        - sql: |
            CREATE UNIQUE INDEX kargo_instance_name_organization_owner ON kargo_instance (organization_owner, name);
        - addUniqueConstraint:
            columnNames: instance_id, name
            constraintName: unique_agent_instance
            tableName: kargo_agent
  - changeSet:
      id: 121
      author: gdsoumya
      changes:
        - sqlFile:
            path: notification_triggers_v3.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 122
      author: jiachengxu
      changes:
        - sql: |
            CREATE INDEX idx_argo_cd_cluster_k8s_object_container_image_tag_2 ON argo_cd_cluster_k8s_object ((columns->>'image'), (columns->>'tag'))
            WHERE "group" = 'dashboard.akuity.io' AND kind = 'Container' AND columns->>'image' IS NOT NULL AND columns->>'tag' IS NOT NULL;
  - changeSet:
      id: 123
      author: pavel
      changes:
        - addColumn:
            tableName: custom_role
            columns:
              - column:
                  name: workspace_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_custom_role_workspace_id
                    references: workspace(id)
                    nullable: true
  - changeSet:
      id: 124
      author: pavel
      changes:
        - addColumn:
            tableName: kargo_instance_config
            columns:
              - column:
                  name: fqdn
                  type: varchar(253)
                  constraints:
                    unique: true
                    nullable: true
  - changeSet:
      id: 125
      author: remington
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: oidc_team_map
                  type: jsonb
  - changeSet:
      id: 126
      author: pavel
      changes:
        - addColumn:
            tableName: argo_cd_cluster
            columns:
              - column:
                  name: internal_spec
                  type: jsonb
  - changeSet:
      id: 127
      author: pavel
      validCheckSum:
        - "9:57d628f1c1ed94b92681e209058ba937"
        - "9:0a7ed49c904abc7110aa09324b49f106"
      changes:
        - sql: |
            SELECT 1;
  - changeSet:
      id: 128
      author: jiachengxu
      changes:
        - dropPrimaryKey:
            tableName: argo_cd_cluster_k8s_object
        - addPrimaryKey:
            tableName: argo_cd_cluster_k8s_object
            columnNames: id, cluster_id
  - changeSet:
      id: 129
      author: hanxiaop
      changes:
        - sql: |
            DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
            UPDATE argo_cd_instance_config
            SET spec = jsonb_set(spec, '{image_updater_version}', '"v0.12.2"', true)
            WHERE argocd_image_updater_enable = true AND (spec->>'image_updater_version' IS NULL OR spec->>'image_updater_version' = '');
        - sqlFile:
            path: argo_cd_instance_config_triggers_v20.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 130
      author: gdsoumya
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: max_kargo_stages
                  type: int
                  defaultValueNumeric: 0
                  constraints:
                    nullable: false
        - sql: |
            UPDATE organization SET max_kargo_stages = 20;
  - changeSet:
      id: 131
      author: hanxiaop
      changes:
        - createTable:
            tableName: argo_cd_cluster_k8s_event
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    primaryKey: true
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_argo_cd_cluster_k8s_event_organization_id
                    references: organization(id)
                    nullable: false
                    deleteCascade: true
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    foreignKeyName: fk_argo_cd_cluster_k8s_event_instance_id
                    references: argo_cd_instance(id)
                    deleteCascade: true
              - column:
                  name: cluster_id
                  type: varchar(50)
                  constraints:
                    nullable: false
                    foreignKeyName: fk_argo_cd_cluster_k8s_event_cluster_id
                    references: argo_cd_cluster(id)
                    deleteCascade: true
              - column:
                  name: group
                  type: varchar(255)
              - column:
                  name: kind
                  type: varchar(255)
              - column:
                  name: namespace
                  type: varchar(255)
              - column:
                  name: name
                  type: varchar(255)
              - column:
                  name: info
                  type: jsonb
              - column:
                  name: timestamp
                  type: timestamptz
                  constraints:
                    nullable: false
              - column:
                  name: last_timestamp
                  type: timestamptz
              - column:
                  name: severity
                  type: varchar(255)
              - column:
                  name: reason
                  type: varchar(255)
              - column:
                  name: count
                  type: int
        - createIndex:
            tableName: argo_cd_cluster_k8s_event
            indexName: idx_argo_cd_cluster_k8s_event_org_instance_group_kind
            columns:
              - column:
                  name: organization_id
              - column:
                  name: instance_id
              - column:
                  name: group
              - column:
                  name: kind
  - changeSet:
      id: 132
      author: jiachengxu
      changes:
        - addColumn:
            tableName: argo_cd_cluster_k8s_object
            columns:
              - column:
                  name: deletion_timestamp
                  type: timestamptz
  - changeSet:
      id: 133
      author: gdsoumya
      changes:
        - createTable:
            tableName: addon_repo
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_addon_repo_organization_id
                    references: organization(id)
                    nullable: false
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_addon_repo_instance_id
                    references: argo_cd_instance(id)
                    nullable: false
              - column:
                  name: spec
                  type: jsonb
              - column:
                  name: generation
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: status_processed_generation
                  type: int
              - column:
                  name: deletion_timestamp
                  type: timestamptz
              - column:
                  name: status_info
                  type: jsonb
        - createTable:
            tableName: addons
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_addons_organization_id
                    references: organization(id)
                    nullable: false
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_addons_instance_id
                    references: argo_cd_instance(id)
                    nullable: false
              - column:
                  name: repo_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_addons_repo_id
                    references: addon_repo(id)
                    nullable: false
              - column:
                  name: spec
                  type: jsonb
              - column:
                  name: status_operation
                  type: jsonb
              - column:
                  name: generation
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: status_processed_generation
                  type: int
              - column:
                  name: deletion_timestamp
                  type: timestamptz
              - column:
                  name: status_info
                  type: jsonb
        - createTable:
            tableName: cluster_addons
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_addons_organization_id
                    references: organization(id)
                    nullable: false
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_addons_instance_id
                    references: argo_cd_instance(id)
                    nullable: false
              - column:
                  name: cluster_name
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: addon_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_addons_id
                    references: addons(id)
                    nullable: false
              - column:
                  name: spec
                  type: jsonb
              - column:
                  name: status_operation
                  type: jsonb
              - column:
                  name: generation
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: status_processed_generation
                  type: int
              - column:
                  name: deletion_timestamp
                  type: timestamptz
              - column:
                  name: status_info
                  type: jsonb
        - sql: |
            CREATE UNIQUE INDEX addon_id_cluster_name ON cluster_addons (addon_id, cluster_name)
        - sqlFile:
            path: addon_repo_trigger_v1.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER on_deleted_addon_repo ON addon_repo;
            DROP TRIGGER on_inserted_addon_repo ON addon_repo;
            DROP TRIGGER on_updated_addon_repo ON addon_repo;
        - sqlFile:
            path: addons_trigger_v1.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER on_deleted_addons ON addons;
            DROP TRIGGER on_inserted_addons ON addons;
            DROP TRIGGER on_updated_addons ON addons;
        - sqlFile:
            path: addons_trigger_gen_bump_v1.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER gen_bump_addons_updated ON addons;
        - sqlFile:
            path: cluster_addons_trigger_v1.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER on_deleted_cluster_addons ON cluster_addons;
            DROP TRIGGER on_inserted_cluster_addons ON cluster_addons;
            DROP TRIGGER on_updated_cluster_addons ON cluster_addons;
        - sqlFile:
            path: cluster_addons_trigger_gen_bump_v1.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER gen_bump_cluster_addons_updated ON cluster_addons;
  - changeSet:
      id: 134
      author: gdsoumya
      changes:
        - addColumn:
            tableName: addons
            columns:
              - column:
                  name: status_source_update
                  type: jsonb
        - sqlFile:
            path: addons_trigger_gen_bump_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER gen_bump_addons_updated ON addons;
  - changeSet:
      id: 135
      author: jiachengxu
      changes:
        - createTable:
            tableName: organization_kubevision_usage
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_organization_kubevision_usage_organization_id
                    references: organization(id)
                    nullable: false
                    deleteCascade: true
              - column:
                  name: instance_count
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: cluster_count
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: node_count
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: pod_count
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: container_count
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: api_resource_count
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
              - column:
                  name: object_count
                  type: int
                  constraints:
                    nullable: false
                  defaultValueNumeric: 0
  - changeSet:
      id: 136
      author: yiwei
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v21.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 137
      author: hanxiaop
      changes:
        - createIndex:
            tableName: argo_cd_cluster_k8s_object
            indexName: idx_k8s_object_org_instance_namespace
            columns:
              - column:
                  name: organization_id
              - column:
                  name: instance_id
              - column:
                  name: cluster_id
              - column:
                  name: namespace
  - changeSet:
      id: 138
      author: gdsoumya
      changes:
        - sqlFile:
            path: addons_trigger_v2.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 139
      author: anubhav06
      changes:
        - createTable:
            tableName: blacklisted_tokens
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: token
                  type: text
                  constraints:
                    nullable: false
              - column:
                  name: akuity_user_id
                  type: varchar(255)
                  constraints:
                    foreignKeyName: fk_akuity_user_id
                    references: akuity_user(id)
                    nullable: false
              - column:
                  name: expiration_timestamp
                  type: timestamptz
                  constraints:
                    nullable: false
        - createIndex:
            tableName: blacklisted_tokens
            indexName: idx_blacklisted_tokens_token
            columns:
              - column:
                  name: token
        - addColumn:
            tableName: akuity_user
            columns:
              - column:
                  name: user_info_public
                  type: jsonb
                  constraints:
                    nullable: true
  - changeSet:
      id: 140
      author: pavel
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: verified_domains
                  type: jsonb
  - changeSet:
      id: 141
      author: pavel
      changes:
        sql: |
          WITH aggregated_domains AS (
            SELECT
              organization_id,
              jsonb_agg(jsonb_build_object('domain', domain, 'verified', true)) AS new_domains
            FROM organization_sso_realm
            GROUP BY organization_id
          )
          UPDATE organization
          SET verified_domains = (
            SELECT jsonb_agg(DISTINCT elem)
            FROM jsonb_array_elements(COALESCE(verified_domains, '[]'::jsonb) || a.new_domains) elem
          )
          FROM aggregated_domains a
          WHERE organization.id = a.organization_id;
  - changeSet:
      id: 142
      author: alex
      changes:
        - sqlFile:
            path: argo_cd_instance_config_triggers_v22.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 143
      author: alex
      changes:
        - addColumn:
            tableName: argo_cd_instance_config
            columns:
              - column:
                  name: basepath
                  type: varchar(253)
                  defaultValue: ''
                  constraints:
                    nullable: false
        - dropUniqueConstraint:
            tableName: argo_cd_instance_config
            uniqueColumns: fqdn
            constraintName: argo_cd_instance_config_fqdn_key
        - addUniqueConstraint:
            tableName: argo_cd_instance_config
            columnNames: fqdn, basepath
            constraintName: argo_cd_instance_config_fqdn_basepath_key
        - dropUniqueConstraint:
            tableName: argo_cd_instance_config
            uniqueColumns: subdomain
            constraintName: argo_cd_instance_config_subdomain_key
        - addUniqueConstraint:
            tableName: argo_cd_instance_config
            columnNames: subdomain, basepath
            constraintName: argo_cd_instance_config_subdomain_basepath_key
  - changeSet:
      id: 144
      author: jiachengxu
      changes:
        - createTable:
            tableName: ai_conversation
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_ai_conversation_organization_id
                    references: organization(id)
                    nullable: false
              - column:
                  name: user_id
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: last_update_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: title
                  type: varchar(255)
              - column:
                  name: messages
                  type: jsonb
  - changeSet:
      id: 145
      author: pavel
      changes:
        - addColumn:
            tableName: organization_invite
            columns:
              - column:
                  name: info
                  type: jsonb
  - changeSet:
      id: 146
      author: yiwei
      changes:
        - addColumn:
            tableName: ai_conversation
            columns:
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_ai_conversation_instance_id
                    references: argo_cd_instance(id)
  - changeSet:
      id: 147
      author: gdsoumya
      changes:
        - createTable:
            tableName: addon_marketplace_installs
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_addon_mkt_organization_id
                    references: organization(id)
                    nullable: false
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_addon_mkt_instance_id
                    references: argo_cd_instance(id)
                    nullable: false
              - column:
                  name: config
                  type: jsonb
              - column:
                  name: generation
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: status_processed_generation
                  type: int
              - column:
                  name: status_info
                  type: jsonb
        - sqlFile:
            path: addon_marketplace_install_trigger_v1.sql
            relativeToChangelogFile: true
            splitStatements: false
        - sqlFile:
            path: addon_marketplace_install_triggers_gen_bump.sql
            relativeToChangelogFile: true
            splitStatements: false
          rollback: |
            DROP TRIGGER bump_gen_addon_marketplace_install_updated ON addon_marketplace_installs;
  - changeSet:
      id: 148
      author: pavel
      changes:
        - sqlFile:
            path: addons_trigger_v3.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 149
      author: anubhav06
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: mfa_settings
                  type: jsonb
                  constraints:
                    nullable: true
  - changeSet:
      id: 150
      author: gdsoumya
      changes:
        - createTable:
            tableName: logs
            columns:
              - column:
                  name: id
                  type: varchar(50)
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: creation_timestamp
                  type: timestamptz
                  defaultValueComputed: current_timestamp
                  constraints:
                    nullable: false
              - column:
                  name: organization_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_logs_organization_id
                    references: organization(id)
                    nullable: false
                    deleteCascade: true
              - column:
                  name: type
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: instance_id
                  type: varchar(50)
                  constraints:
                    nullable: false
              - column:
                  name: metadata
                  type: jsonb
              - column:
                  name: ttl_seconds
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: log_dst
                  type: TEXT
                  constraints:
                    nullable: false
          sql: |
            CREATE INDEX log_org_instance ON logs (organization_id, instance_id);
  - changeSet:
      id: 151
      author: alexmt
      changes:
        - sql: |
            update akuity_user set email = lower(email) where email != lower(email);
            update organization_invite set invitee_email = lower(invitee_email) where invitee_email != lower(invitee_email);
            update organization_invite set invited_by_email = lower(invited_by_email) where invited_by_email != lower(invited_by_email);
            update billing set billing_email = lower(billing_email) where billing_email != lower(billing_email);
        - modifyDataType:
            tableName: akuity_user
            columnName: email
            newDataType: varchar(254)
        - modifyDataType:
            tableName: organization_invite
            columnName: invitee_email
            newDataType: varchar(254)
        - modifyDataType:
            tableName: organization_invite
            columnName: invited_by_email
            newDataType: varchar(254)
        - modifyDataType:
            tableName: billing
            columnName: billing_email
            newDataType: varchar(254)
  - changeSet:
      id: 152
      author: pavel
      changes:
        - addColumn:
            tableName: team
            columns:
              - column:
                  name: permissions
                  type: jsonb
  - changeSet:
      id: 153
      author: Marvin9
      changes:
        - addColumn:
            tableName: organization_plan
            columns:
              - column:
                  name: deprecated
                  type: boolean
  - changeSet:
      id: 154
      author: alexmt
      changes:
        - addColumn:
            tableName: ai_conversation
            columns:
              - column:
                  name: contexts
                  type: jsonb
              - column:
                  name: metadata
                  type: jsonb
        - sqlFile:
            path: ai_conversation_triggers.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 155
      author: alexmt
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: spec
                  type: jsonb
  - changeSet:
      id: 156
      author: hanxiaop
      changes:
        - addColumn:
            tableName: ai_conversation
            columns:
              - column:
                  name: kargo_instance_id
                  type: varchar(50)
                  constraints:
                    foreignKeyName: fk_ai_conversation_kargo_instance_id
                    references: kargo_instance(id)
  - changeSet:
      id: 157
      author: alexmt
      changes:
        - addColumn:
            tableName: ai_conversation
            columns:
              - column:
                  name: public
                  type: boolean
                  defaultValueBoolean: false
  - changeSet:
      id: 158
      author: mingqiu
      changes:
        - addColumn:
            tableName: ai_conversation
            columns:
              - column:
                  name: feedbacks
                  type: jsonb
  - changeSet:
      id: 159
      author: pavel
      changes:
        - sql: |
            UPDATE argo_cd_instance_config
            SET spec = jsonb_set(
              spec,
              '{app_reconciliations_rate_limiting}',
              '{
                "item_rate_limiting": {
                  "enabled": true,
                  "max_delay": 1000000000,
                  "base_delay": 1000000,
                  "backoff_factor": 1.5,
                  "failure_cooldown": 10000000000
                },
                "bucket_rate_limiting": {
                  "enabled": false,
                  "bucket_qps": 50,
                  "bucket_size": 500
                }
              }'::jsonb,
              true
            );
        - sqlFile:
            path: argo_cd_instance_config_triggers_v23.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 160
      author: yiwei
      changes:
        sql: |
          UPDATE argo_cd_instance_config 
          SET spec = jsonb_set(
              spec, 
              '{akuity_intelligence_extension}', 
              spec->'kubevision_argo_extension'
          ) - 'kubevision_argo_extension'
          WHERE spec->'kubevision_argo_extension' IS NOT NULL;
          UPDATE argo_cd_instance_config 
          SET spec = jsonb_set(
              spec, 
              '{akuity_intelligence_extension,ai_support_engineer_enabled}', 
              spec->'ai_support_engineer_extension'->'enabled'
          ) - 'ai_support_engineer_extension'
          WHERE spec->'ai_support_engineer_extension' IS NOT NULL;
          UPDATE kargo_instance_config 
          SET spec = jsonb_set(
              spec, 
              '{akuity_intelligence,ai_support_engineer_enabled}', 
              spec->'akuity_intelligence'->'ai_sre_enabled'
          ) - '{akuity_intelligence,ai_sre_enabled}'
          WHERE spec->'akuity_intelligence' IS NOT NULL;
  - changeSet:
      id: 161
      author: alex
      changes:
        - sql: |
            DROP TRIGGER IF EXISTS on_updated_argo_cd_instance_config ON argo_cd_instance_config;
        - sql: |
            UPDATE argo_cd_instance_config
            SET spec = jsonb_set(
              spec,
              '{app_reconciliations_rate_limiting}',
              '{
                "item_rate_limiting": {
                  "enabled": false
                }
              }'::jsonb,
              true
            );
        - sqlFile:
            path: argo_cd_instance_config_triggers_v23.sql
            relativeToChangelogFile: true
            splitStatements: false
  - changeSet:
      id: 162
      author: alex
      changes:
        - sql: |
            UPDATE organization
            SET
            feature_gates = feature_gates || '{
              "workspaces": null,
              "notification": null,
              "cluster_autoscaler": null,
              "pgpool": null,
              "pgbouncer": null,
              "k3s_proxy_informers": null,
              "kargo_analysis_logs": null,
              "kargo_enterprise": null
            }'::jsonb
  - changeSet:
      id: 163
      author: alex
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: ai_tokens_count
                  type: int
                  defaultValueNumeric: 0
                  constraints:
                    nullable: false
        - addColumn:
            tableName: organization_kubevision_usage
            columns:
              - column:
                  name: ai_tokens_count
                  type: int
                  defaultValueNumeric: 0
                  constraints:
                    nullable: false
  - changeSet:
      id: 164
      author: pavel
      changes:
        - addColumn:
            tableName: addons
            columns:
              - column:
                  name: clean_git_on_delete
                  type: boolean
  - changeSet:
      id: 165
      author: gdsoumya
      changes:
        - sql: |
            UPDATE organization
            SET
            feature_gates = feature_gates || '{
              "fleet_management": null
            }'::jsonb
  - changeSet:
      id: 166
      author: alex
      changes:
        - addColumn:
            tableName: organization
            columns:
              - column:
                  name: ai_usage
                  type: jsonb
        - addColumn:
            tableName: organization_kubevision_usage
            columns:
              - column:
                  name: ai_usage
                  type: jsonb
  - changeSet:
      id: 167
      author: mingqiu
      changes:
        - sql: |
            -- Migrate runbooks from incident.runbooks to metadata.runbooks
            UPDATE ai_conversation
            SET metadata = jsonb_set(
                    metadata,
                    '{runbooks}',
                    (
                        SELECT jsonb_agg(
                            jsonb_build_object(
                                'name', rb,
                                'instanceId', metadata #>> '{incident,instanceId}'
                            )
                        )
                        FROM jsonb_array_elements_text(metadata #> '{incident,runbooks}') AS t(rb)
                    ),
                    true
                )
            WHERE jsonb_typeof(metadata #> '{incident,runbooks}') = 'array'
              AND jsonb_array_length(metadata #> '{incident,runbooks}') > 0;
