package models

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"reflect"
	"strings"
	"time"

	jsonpatch "github.com/evanphx/json-patch"
	"github.com/volatiletech/null/v8"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/tools/clientcmd"
	clientcmdapi "k8s.io/client-go/tools/clientcmd/api"

	"github.com/akuityio/agent/pkg/common"
	apierror "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/kustomize"
	"github.com/akuityio/akuity-platform/models/util/dex"
	"github.com/akuityio/akuity-platform/models/util/encryption"
	"github.com/akuityio/akuity-platform/models/util/validation"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

const (
	DefaultKargoImage = "quay.io/akuity/kargo"
	DefaultTokenTTL   = "24h"
	// AkpDexGoogleSASecretKey is the key used to store the Google Service Account JSON in the secrets for dex config
	AkpDexGoogleSASecretKey = "akp.dex.google.service.account"
	AkpDexGoogleSAMountPath = "/tmp/oidc/googleAuth.json"
)

var KargoInstanceConfigAllColumns = kargoInstanceConfigAllColumns

func (o *KargoInstanceConfig) DeepCopy() KargoInstanceConfig {
	return KargoInstanceConfig{
		InstanceID:   o.InstanceID,
		Version:      o.Version,
		Spec:         o.Spec,
		PrivateSpec:  o.PrivateSpec,
		InternalSpec: o.InternalSpec,
		ControllerCM: o.ControllerCM,
		WebhookCM:    o.WebhookCM,
		OidcConfig:   o.OidcConfig,
		APISecret:    o.APISecret,
		APICM:        o.APICM,
	}
}

func (o *KargoInstanceConfig) Equals(other KargoInstanceConfig) (bool, error) {
	this := *o
	this.R = nil
	other.R = nil

	getters := []func(cfg KargoInstanceConfig) (interface{}, error){
		func(cfg KargoInstanceConfig) (interface{}, error) {
			return cfg.Version.String, nil
		},
		func(cfg KargoInstanceConfig) (interface{}, error) {
			return cfg.GetSpec()
		},
		func(cfg KargoInstanceConfig) (interface{}, error) {
			return cfg.GetPrivateSpec()
		},
	}
	for _, getter := range getters {
		thisValue, err := getter(this)
		if err != nil {
			return false, err
		}
		otherValue, err := getter(other)
		if err != nil {
			return false, err
		}
		if !reflect.DeepEqual(thisValue, otherValue) {
			return false, nil
		}
	}
	return true, nil
}

// KargoInstanceInternalSpec contains settings managed by the platform and not exposed to the user
type KargoInstanceInternalSpec struct {
	K3sImage           string                 `json:"k3s_image,omitempty"`
	Values             map[string]interface{} `json:"values,omitempty"`
	ArgoCDUrls         map[string]string      `json:"argocd_urls,omitempty"`
	CanCreateProjects  *bool                  `json:"can_create_projects,omitempty"`
	CanCreateStages    *bool                  `json:"can_create_stages,omitempty"`
	K3sSchemaMigration int                    `json:"k3s_schema_migration,omitempty"`
}

func (s *KargoInstanceInternalSpec) GetCanCreateProjects() bool {
	if s.CanCreateProjects == nil {
		return true
	}
	return *s.CanCreateProjects
}

func (s *KargoInstanceInternalSpec) GetCanCreateStages() bool {
	if s.CanCreateStages == nil {
		return true
	}
	return *s.CanCreateStages
}

// KargoInstanceConfigPrivateSpec type definition for private_spec jsonb field in kargo_instance_config
type KargoInstanceConfigPrivateSpec struct {
	K3sUsername          string `json:"k3s_username"`
	K3sPassword          string `json:"k3s_password"`
	K3sToken             string `json:"k3s_token"`
	FqdnVersion          string `json:"fqdn_version"`
	WebhookKey           string `json:"webhook_key"`
	WebhookCert          string `json:"webhook_cert"`
	KargoServerSecretKey string `json:"kargo_server_secret_key"`
	GitOpsKubeconfig     []byte `json:"git_ops_kubeconfig"`
	AkpAdminKargoToken   string `json:"akp_admin_kargo_token"`
}

func (s KargoInstanceConfigPrivateSpec) GetGitopsConfig() (*clientcmdapi.Config, error) {
	if s.GitOpsKubeconfig == nil {
		return nil, errors.New("gitops kubeconfig is empty")
	}
	return clientcmd.Load(s.GitOpsKubeconfig)
}

func (o *KargoInstanceConfig) GetPrivateSpec() (KargoInstanceConfigPrivateSpec, error) {
	privateSpec := KargoInstanceConfigPrivateSpec{}
	if o.PrivateSpec.IsZero() {
		return privateSpec, nil
	}
	decryptedString, err := encryption.Decrypt(o.PrivateSpec.String)
	if err != nil {
		return privateSpec, err
	}
	err = json.Unmarshal([]byte(decryptedString), &privateSpec)
	return privateSpec, err
}

func (o *KargoInstanceConfig) SetPrivateSpec(spec KargoInstanceConfigPrivateSpec) error {
	data, err := json.Marshal(spec)
	if err != nil {
		return err
	}
	encryptedData, err := encryption.Encrypt(string(data))
	if err != nil {
		return err
	}
	o.PrivateSpec = null.StringFrom(encryptedData)
	return nil
}

type KargoAgentCustomization struct {
	AutoUpgradeDisabled bool                    `json:"auto_upgrade_disabled"`
	Kustomization       kustomize.Kustomization `json:"kustomization"`
}

// KargoInstanceConfigSpec type definition for spec jsonb field in kargo_instance_config
type KargoInstanceConfigSpec struct {
	IpAllowlist                     []IpAllowlistEntry      `json:"ip_allowlist"`
	BackendIpAllowlistEnabled       bool                    `json:"backend_ip_allowlist"`
	KargoAgentCustomizationDefaults KargoAgentCustomization `json:"agent_customization_defaults"`

	DefaultShardAgentID    string                      `json:"default_shard_agent"`
	GlobalCredentialsNs    []string                    `json:"global_credentials_ns"`
	GlobalServiceAccountNs []string                    `json:"global_service_account_ns"`
	AkuityIntelligence     *kargov1.AkuityIntelligence `json:"akuity_intelligence,omitempty"`
}

type KargoControllerCM struct {
	// nothing to store for now
}

type KargoWebhookCM struct {
	// nothing to store for now
}

type KargoApiCM struct {
	AdminAccountEnabled       bool   `json:"ADMIN_ACCOUNT_ENABLED"`
	AdminAccountTokenIssuer   string `json:"ADMIN_ACCOUNT_TOKEN_ISSUER"`
	AdminAccountTokenAudience string `json:"ADMIN_ACCOUNT_TOKEN_AUDIENCE"`
	AdminAccountTokenTTL      string `json:"ADMIN_ACCOUNT_TOKEN_TTL"`
}

type KargoApiSecret struct {
	AdminAccountPasswordHash string `json:"ADMIN_ACCOUNT_PASSWORD_HASH"`
}

type KargoPredefinedAccountData struct {
	// Deprecated: use Claims instead
	Email []string `json:"email"`
	// Deprecated: use Claims instead
	Sub []string `json:"sub"`
	// Deprecated: use Claims instead
	Groups []string `json:"groups"`

	Claims map[string][]string `json:"claims"`
}

type KargoOidcConfig struct {
	Enabled                   bool                       `json:"enabled"`
	DexEnabled                bool                       `json:"dex_enabled"`
	DexConfig                 string                     `json:"-"`                            // not stored in db, decrypted data
	DexEncryptedConfig        string                     `json:"dex_encrypted_config"`         // encrypted config as it might contain secrets
	DexConfigSecrets          map[string]string          `json:"-"`                            // not stored in db, decrypted data
	DexEncryptedConfigSecrets string                     `json:"dex_encrypted_config_secrets"` // encrypted config as it might contain secrets
	IssuerURL                 string                     `json:"issuer_url"`
	ClientID                  string                     `json:"client_id"`
	CliClientID               string                     `json:"cli_client_id"`
	AdditionalScopes          []string                   `json:"additional_scopes"`
	AdminAccount              KargoPredefinedAccountData `json:"admin_account"`
	ViewerAccount             KargoPredefinedAccountData `json:"viewer_account"`
	UserAccount               KargoPredefinedAccountData `json:"user_account"`
}

func (o *KargoInstanceConfig) GetControllerCM() (KargoControllerCM, error) {
	cm := KargoControllerCM{}
	if o.ControllerCM.IsZero() {
		return cm, nil
	}
	err := json.Unmarshal(o.ControllerCM.JSON, &cm)
	return cm, err
}

func (o *KargoInstanceConfig) GetControllerCMValues() (map[string]interface{}, error) {
	cm := map[string]interface{}{}
	if o.ControllerCM.IsZero() {
		return cm, nil
	}
	err := json.Unmarshal(o.ControllerCM.JSON, &cm)
	return cm, err
}

func (o *KargoInstanceConfig) SetControllerCM(spec KargoControllerCM) error {
	data, err := json.Marshal(spec)
	if err != nil {
		return err
	}
	o.ControllerCM = null.JSONFrom(data)
	return nil
}

func (o *KargoInstanceConfig) GetWebhookCM() (KargoWebhookCM, error) {
	cm := KargoWebhookCM{}
	if o.WebhookCM.IsZero() {
		return cm, nil
	}
	err := json.Unmarshal(o.WebhookCM.JSON, &cm)
	return cm, err
}

func (o *KargoInstanceConfig) GetWebhookCMValues() (map[string]interface{}, error) {
	cm := map[string]interface{}{}
	if o.WebhookCM.IsZero() {
		return cm, nil
	}
	err := json.Unmarshal(o.WebhookCM.JSON, &cm)
	return cm, err
}

func (o *KargoInstanceConfig) SetWebhookCM(spec KargoWebhookCM) error {
	data, err := json.Marshal(spec)
	if err != nil {
		return err
	}
	o.WebhookCM = null.JSONFrom(data)
	return nil
}

func (o *KargoInstanceConfig) GetApiCM() (KargoApiCM, error) {
	cm := KargoApiCM{}
	if o.APICM.IsZero() {
		return cm, nil
	}
	err := json.Unmarshal(o.APICM.JSON, &cm)
	return cm, err
}

func (o *KargoInstanceConfig) GetApiCMValues() (map[string]interface{}, error) {
	cm := map[string]interface{}{}
	if o.APICM.IsZero() {
		return cm, nil
	}
	err := json.Unmarshal(o.APICM.JSON, &cm)
	// convert from bool to string
	if cm["ADMIN_ACCOUNT_ENABLED"] != nil {
		cm["ADMIN_ACCOUNT_ENABLED"] = fmt.Sprintf("%v", cm["ADMIN_ACCOUNT_ENABLED"])
	}
	return cm, err
}

func (o *KargoInstanceConfig) SetApiCM(spec KargoApiCM) error {
	if spec.AdminAccountTokenIssuer == "" {
		spec.AdminAccountTokenIssuer = o.InstanceID
	}
	if spec.AdminAccountTokenAudience == "" {
		spec.AdminAccountTokenAudience = o.InstanceID
	}
	if spec.AdminAccountTokenTTL == "" {
		spec.AdminAccountTokenTTL = DefaultTokenTTL
	} else {
		if _, err := time.ParseDuration(spec.AdminAccountTokenTTL); err != nil {
			return fmt.Errorf("invalid ttl value for ADMIN_ACCOUNT_TOKEN_TTL: %s", spec.AdminAccountTokenTTL)
		}
	}
	data, err := json.Marshal(spec)
	if err != nil {
		return err
	}
	o.APICM = null.JSONFrom(data)
	return nil
}

type KargoMiscellaneousSecrets struct {
	DatadogRolloutsSecret  *DataDogRolloutsSecret  `json:"datadog_rollouts_secret,omitempty"`
	NewRelicRolloutsSecret *NewRelicRolloutsSecret `json:"newrelic_rollouts_secret,omitempty"`
	InfluxDbRolloutsSecret *InfluxDbRolloutsSecret `json:"influxdb_rollouts_secret,omitempty"`
}

type DataDogRolloutsSecret struct {
	Address string `json:"address"`
	APIKey  string `json:"api-key"`
	AppKey  string `json:"app-key"`
}

type NewRelicRolloutsSecret struct {
	PersonalAPIKey   string `json:"personal-api-key"`
	AccountID        string `json:"account-id"`
	Region           string `json:"region"`
	BaseURLRest      string `json:"base-url-rest"`
	BaseURLNerdGraph string `json:"base-url-nerdgraph"`
}

type InfluxDbRolloutsSecret struct {
	Address   string `json:"address"`
	AuthToken string `json:"authToken"`
	Org       string `json:"org"`
}

func (o *KargoInstanceConfig) GetMiscellaneousSecrets(censored bool) (KargoMiscellaneousSecrets, error) {
	secret := KargoMiscellaneousSecrets{}
	if o.MiscellaneousSecrets.IsZero() {
		return secret, nil
	}

	decryptedString, err := encryption.Decrypt(o.MiscellaneousSecrets.String)
	if err != nil {
		return secret, err
	}
	if censored {
		// marshal into map[string]map[string]interface{} to censor the secrets
		secretMap := map[string]map[string]interface{}{}
		if err = json.Unmarshal([]byte(decryptedString), &secretMap); err != nil {
			return secret, err
		}
		for key, value := range secretMap {
			for k := range value {
				secretMap[key][k] = ""
			}
		}
		modified, err := json.Marshal(secretMap)
		if err != nil {
			return secret, err
		}
		decryptedString = string(modified)
	}
	err = json.Unmarshal([]byte(decryptedString), &secret)
	return secret, err
}

func (o *KargoInstanceConfig) GetRolloutsSecrets() ([]*interface{}, error) {
	rolloutsSecrets := []*interface{}{}
	secret := map[string]map[string]interface{}{}
	if o.MiscellaneousSecrets.IsZero() {
		return rolloutsSecrets, nil
	}

	decryptedString, err := encryption.Decrypt(o.MiscellaneousSecrets.String)
	if err != nil {
		return rolloutsSecrets, err
	}
	if err = json.Unmarshal([]byte(decryptedString), &secret); err != nil {
		return rolloutsSecrets, err
	}

	for key, value := range secret {
		if strings.HasSuffix(key, "_rollouts_secret") {
			res, err := runtime.DefaultUnstructuredConverter.ToUnstructured(&v1.Secret{
				TypeMeta: metav1.TypeMeta{
					Kind:       "Secret",
					APIVersion: "v1",
				},
				ObjectMeta: metav1.ObjectMeta{
					Name: strings.ReplaceAll(key, "_rollouts_secret", ""),
					Labels: map[string]string{
						common.KargoSecretTypeLabel: "rollouts", // necessary to pass k3s proxy filter
					},
				},
				StringData: convertMap(value),
			})
			if err != nil {
				return rolloutsSecrets, err
			}
			tempInterface := interface{}(res)
			rolloutsSecrets = append(rolloutsSecrets, &tempInterface)
		}
	}
	return rolloutsSecrets, err
}

func convertMap(data map[string]interface{}) map[string]string {
	newData := make(map[string]string)
	for key, value := range data {
		newData[key] = value.(string)
	}
	return newData
}

func (o *KargoInstanceConfig) SetMiscellaneousSecrets(spec KargoMiscellaneousSecrets) error {
	data, err := json.Marshal(spec)
	if err != nil {
		return err
	}
	encryptedData, err := encryption.Encrypt(string(data))
	if err != nil {
		return err
	}
	o.MiscellaneousSecrets = null.StringFrom(encryptedData)
	return nil
}

func (o *KargoInstanceConfig) GetAPISecret() (KargoApiSecret, error) {
	secret := KargoApiSecret{}
	if o.APISecret.IsZero() {
		return secret, nil
	}

	decryptedString, err := encryption.Decrypt(o.APISecret.String)
	if err != nil {
		return secret, err
	}
	err = json.Unmarshal([]byte(decryptedString), &secret)
	return secret, err
}

func (o *KargoInstanceConfig) GetAPISecretValues() (map[string]interface{}, error) {
	secret := map[string]interface{}{}
	if o.APISecret.IsZero() {
		return secret, nil
	}

	decryptedString, err := encryption.Decrypt(o.APISecret.String)
	if err != nil {
		return secret, err
	}
	err = json.Unmarshal([]byte(decryptedString), &secret)
	return secret, err
}

func (o *KargoInstanceConfig) SetAPISecret(spec KargoApiSecret) error {
	data, err := json.Marshal(spec)
	if err != nil {
		return err
	}
	encryptedData, err := encryption.Encrypt(string(data))
	if err != nil {
		return err
	}
	o.APISecret = null.StringFrom(encryptedData)
	return nil
}

func (o *KargoInstanceConfig) GetOidcConfig() (KargoOidcConfig, error) {
	secret := KargoOidcConfig{
		DexConfigSecrets: map[string]string{},
	}
	if o.OidcConfig.IsZero() {
		return secret, nil
	}
	if err := json.Unmarshal(o.OidcConfig.JSON, &secret); err != nil {
		return secret, err
	}
	if secret.DexEncryptedConfig != "" {
		decryptedString, err := encryption.Decrypt(secret.DexEncryptedConfig)
		if err != nil {
			return secret, err
		}
		secret.DexConfig = decryptedString
	}
	if secret.DexEncryptedConfigSecrets != "" {
		decryptedString, err := encryption.Decrypt(secret.DexEncryptedConfigSecrets)
		if err != nil {
			return secret, err
		}
		if err := json.Unmarshal([]byte(decryptedString), &secret.DexConfigSecrets); err != nil {
			return secret, err
		}
	}
	// backward compat logic for v0.9
	secret.AdminAccount.BackwardCompat()
	secret.ViewerAccount.BackwardCompat()
	secret.UserAccount.BackwardCompat()
	return secret, nil
}

func (o *KargoPredefinedAccountData) BackwardCompat() {
	// if email/sub/group are not empty then append all the values to Claims
	if o.Claims == nil {
		o.Claims = map[string][]string{}
	}
	if len(o.Email) > 0 {
		if o.Claims["email"] == nil {
			o.Claims["email"] = []string{}
		}
		o.Claims["email"] = append(o.Claims["email"], o.Email...)
		o.Email = nil
	}
	if len(o.Groups) > 0 {
		if o.Claims["groups"] == nil {
			o.Claims["groups"] = []string{}
		}
		o.Claims["groups"] = append(o.Claims["groups"], o.Groups...)
		o.Groups = nil
	}
	if len(o.Sub) > 0 {
		if o.Claims["sub"] == nil {
			o.Claims["sub"] = []string{}
		}
		o.Claims["sub"] = append(o.Claims["sub"], o.Sub...)
		o.Sub = nil
	}
}

func (o *KargoInstanceConfig) HydrateAndValidateDexConfig(config, domainSuffix string, secret map[string]string, local, skipValidation bool) ([]byte, error) {
	subdomain := o.InstanceID
	if o.Subdomain.String != "" {
		subdomain = o.Subdomain.String
	}
	instanceURl := fmt.Sprintf("%s.kargo.%s", subdomain, domainSuffix)
	if o.FQDN.String != "" {
		instanceURl = o.FQDN.String
	}
	if local {
		instanceURl = fmt.Sprintf("kargo-api.kargo-%s.svc.cluster.local", o.InstanceID)
	}

	googleSaPath := ""
	if secret != nil && secret[AkpDexGoogleSASecretKey] != "" {
		googleSaPath = AkpDexGoogleSAMountPath
	}

	if !skipValidation {
		cfg, err := dex.GenerateDexConfigYAML(config, o.InstanceID, instanceURl, googleSaPath, true, true)
		if err != nil {
			return nil, fmt.Errorf("failed to generate dex config: %w", err)
		}

		// run validation after hydration to ensure that the final generated config is valid
		if err := validation.ValidateDexConfig(string(cfg)); err != nil {
			return nil, err
		}
	}

	return dex.GenerateDexConfigYAML(config, o.InstanceID, instanceURl, googleSaPath, true, false)
}

func (o *KargoInstanceConfig) SetOidcConfig(spec KargoOidcConfig, domainSuffix string) error {
	// backward compat logic for v0.9
	spec.AdminAccount.BackwardCompat()
	spec.ViewerAccount.BackwardCompat()
	spec.UserAccount.BackwardCompat()

	if spec.DexConfig != "" {
		// validate that hydration works
		if _, err := o.HydrateAndValidateDexConfig(spec.DexConfig, domainSuffix, spec.DexConfigSecrets, false, false); err != nil {
			return apierror.NewAPIStatus(http.StatusBadRequest, err.Error())
		}
		encryptedData, err := encryption.Encrypt(spec.DexConfig)
		if err != nil {
			return err
		}
		spec.DexEncryptedConfig = encryptedData
	}
	if len(spec.DexConfigSecrets) == 0 {
		spec.DexConfigSecrets = map[string]string{}
	}
	secretData, err := json.Marshal(spec.DexConfigSecrets)
	if err != nil {
		return err
	}
	encryptedData, err := encryption.Encrypt(string(secretData))
	if err != nil {
		return err
	}
	spec.DexEncryptedConfigSecrets = encryptedData

	data, err := json.Marshal(spec)
	if err != nil {
		return err
	}
	o.OidcConfig = null.JSONFrom(data)
	return nil
}

func (o *KargoInstanceConfig) GetSpec() (KargoInstanceConfigSpec, error) {
	spec := KargoInstanceConfigSpec{}
	if o.Spec.IsZero() {
		return spec, nil
	}
	if err := json.Unmarshal(o.Spec.JSON, &spec); err != nil {
		return spec, err
	}

	kustomization := spec.KargoAgentCustomizationDefaults.Kustomization
	if kustomization == nil {
		kustomization = kustomize.Kustomization{}
	}
	spec.KargoAgentCustomizationDefaults.Kustomization = kustomization

	return spec, nil
}

func (o *KargoInstanceConfig) SetSpec(spec KargoInstanceConfigSpec) error {
	data, err := json.Marshal(spec)
	if err != nil {
		return err
	}
	o.Spec = null.JSONFrom(data)
	return nil
}

func (o *KargoInstanceConfig) GetInternalSpec() (*KargoInstanceInternalSpec, error) {
	internalSpec := &KargoInstanceInternalSpec{}
	if o.InternalSpec.IsZero() {
		return internalSpec, nil
	}

	if err := json.Unmarshal(o.InternalSpec.JSON, internalSpec); err != nil {
		return nil, err
	}
	return internalSpec, nil
}

func (o *KargoInstanceConfig) SetInternalSpec(internalSpec *KargoInstanceInternalSpec) error {
	data, err := json.Marshal(internalSpec)
	if err != nil {
		return err
	}
	o.InternalSpec = null.JSONFrom(data)
	return nil
}

func (o *KargoInstanceConfig) GetPatch(updated *KargoInstanceConfig) (map[string]interface{}, error) {
	originJSON, err := json.Marshal(o)
	if err != nil {
		return nil, err
	}
	updatedJSON, err := json.Marshal(updated)
	if err != nil {
		return nil, err
	}
	patch, err := jsonpatch.CreateMergePatch(originJSON, updatedJSON)
	if err != nil {
		return nil, err
	}
	rv := map[string]interface{}{}
	if err := json.Unmarshal(patch, &rv); err != nil {
		return nil, err
	}

	// Oidc Secret
	originOidc, err := o.GetOidcConfig()
	if err != nil {
		return nil, err
	}
	updatedOidc, err := updated.GetOidcConfig()
	if err != nil {
		return nil, err
	}
	oidcPatch, err := getSecretPatch(originOidc, updatedOidc, "dex_encrypted_config_secrets", "dex_encrypted_config")
	if err != nil {
		return nil, err
	}
	if len(oidcPatch) > 0 {
		if _, ok := oidcPatch["dex_encrypted_config"]; ok {
			oidcPatch["dex_encrypted_config"] = "*****"
		}
		if _, ok := oidcPatch["dex_encrypted_config_secrets"]; ok {
			oidcPatch["dex_encrypted_config_secrets"] = "*****"
		}
		rv["oidc_config"] = oidcPatch
	}

	// APISecret
	originAPISecret, err := o.GetAPISecret()
	if err != nil {
		return nil, err
	}
	updatedAPISecret, err := updated.GetAPISecret()
	if err != nil {
		return nil, err
	}
	apiSecretPatch, err := getSecretPatch(originAPISecret, updatedAPISecret)
	if err != nil {
		return nil, err
	}
	if len(apiSecretPatch) > 0 {
		rv["api_secret"] = apiSecretPatch
	}

	// MiscellaneousSecrets
	originMiscellaneousSecrets, err := o.GetMiscellaneousSecrets(false)
	if err != nil {
		return nil, err
	}
	updatedMiscellaneousSecrets, err := updated.GetMiscellaneousSecrets(false)
	if err != nil {
		return nil, err
	}
	miscSecretPatch, err := getSecretPatch(originMiscellaneousSecrets, updatedMiscellaneousSecrets)
	if err != nil {
		return nil, err
	}
	if len(miscSecretPatch) > 0 {
		rv["miscellaneous_secrets"] = miscSecretPatch
	}

	// ControllerCM
	originControllerCM, err := o.GetControllerCM()
	if err != nil {
		return nil, err
	}
	updatedControllerCM, err := updated.GetControllerCM()
	if err != nil {
		return nil, err
	}
	controllerCMPatch, err := getPatch(originControllerCM, updatedControllerCM)
	if err != nil {
		return nil, err
	}
	if len(controllerCMPatch) > 0 {
		rv["controller_cm"] = controllerCMPatch
	}

	// WebhookCM
	originWebhookCM, err := o.GetWebhookCM()
	if err != nil {
		return nil, err
	}
	updatedWebhookCM, err := updated.GetWebhookCM()
	if err != nil {
		return nil, err
	}
	webhookCMPatch, err := getPatch(originWebhookCM, updatedWebhookCM)
	if err != nil {
		return nil, err
	}
	if len(webhookCMPatch) > 0 {
		rv["webhook_cm"] = webhookCMPatch
	}

	// APICM
	originAPICM, err := o.GetApiCM()
	if err != nil {
		return nil, err
	}
	updatedAPICM, err := updated.GetApiCM()
	if err != nil {
		return nil, err
	}
	apiCMPatch, err := getPatch(originAPICM, updatedAPICM)
	if err != nil {
		return nil, err
	}
	if len(apiCMPatch) > 0 {
		rv["api_cm"] = apiCMPatch
	}

	// Spec
	originSpec, err := o.GetSpec()
	if err != nil {
		return nil, err
	}
	updatedSpec, err := updated.GetSpec()
	if err != nil {
		return nil, err
	}
	specPatch, err := getPatch(originSpec, updatedSpec)
	if err != nil {
		return nil, err
	}
	if len(specPatch) > 0 {
		rv["spec"] = specPatch
	}
	return rv, nil
}
