module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  extends: ['eslint:recommended', 'plugin:@typescript-eslint/recommended', 'prettier'],
  plugins: ['@typescript-eslint', 'prettier', 'import', 'react'],
  ignorePatterns: ['*.cjs'],
  parserOptions: {
    sourceType: 'module',
    ecmaVersion: 2019
  },
  env: {
    browser: true,
    es2017: true,
    node: true
  },
  rules: {
    'prettier/prettier': 'error',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    'import/order': [
      'error',
      {
        groups: ['builtin', 'external', ['internal', 'unknown'], 'parent', 'sibling', 'index'],
        pathGroups: [
          {
            pattern: '@ui/**',
            group: 'internal'
          }
        ],
        pathGroupsExcludedImportTypes: [],
        'newlines-between': 'always',
        alphabetize: {
          order: 'asc'
        }
      }
    ],
    '@typescript-eslint/ban-ts-comment': 'warn',
    'no-console': 'error',
    'no-restricted-imports': [
      'error',
      {
        paths: [
          // Message shouldn't be used at all, there's no need for a slightly lighter notification format.
          // Static notification functions are unable to get necessary context data.
          {
            name: 'antd',
            importNames: ['message', 'notification'],
            message: 'Use notification from NotificationContextProvider instead.'
          },
          {
            name: 'antd/lib',
            importNames: ['message', 'notification'],
            message: 'Use notification from NotificationContextProvider instead.'
          }
        ]
      }
    ],
    'react/jsx-key': 'error',
    '@typescript-eslint/no-unused-vars': ['error', { caughtErrors: 'none' }],
    '@typescript-eslint/no-unused-expressions': ['error', { allowShortCircuit: true }]
  },
  overrides: [
    {
      files: ['*.test.ts'],
      rules: {
        '@typescript-eslint/ban-ts-comment': 'off'
      }
    },
    {
      files: ['config/**', 'scripts/**'],
      rules: {
        'no-console': 'off'
      }
    }
  ]
};
