import { AIAssistant } from '@ui/feature/kubevision/components/shared/ai-assistant';
import { PlatformContextProvider } from '@ui/feature/shared/context/platform-context-provider';
import {
  AkuityIntelligenceContextProvider,
  useAkuityIntelligenceContext
} from '@ui/lib/context/akuity-intelligence-context';

import { Application, Resource } from '../types';

type ContextedAIAssistantProps = {
  application: Application;
  resource: Resource;
  settings: { organizationId: string; instanceId: string };
  resourceGroup: string;
  resourceVersion: string;
};

const ContextedAIAssistant = ({
  application,
  resource,
  settings,
  resourceGroup,
  resourceVersion
}: ContextedAIAssistantProps) => {
  const { enabledClustersInfo } = useAkuityIntelligenceContext();
  let clusterId = application.metadata.labels?.cluster;
  if (clusterId !== 'in-cluster') {
    clusterId = enabledClustersInfo.clusterId({
      instanceId: settings?.instanceId || '',
      clusterName: application.metadata.labels?.cluster || ''
    });
  }

  return (
    <AIAssistant
      instanceId={settings?.instanceId || ''}
      clusterId={clusterId}
      applicationName={application.metadata.name}
      resourceId={resource.metadata.uid}
      resourceGroup={resourceGroup}
      resourceVersion={resourceVersion}
      resourceKind={resource.kind}
      resourceNamespace={resource.metadata.namespace}
      resourceName={resource.metadata.name}
    />
  );
};

export const AssistantExtension = ({
  application,
  resource,
  settings
}: {
  application: Application;
  resource: Resource;
  settings: { organizationId: string; instanceId: string };
}) => {
  const chunks = resource.apiVersion.split('/');
  const resourceGroup = chunks.length == 2 ? chunks[0] : '';
  const resourceVersion = chunks.length == 2 ? chunks[1] : chunks[0];
  return (
    <PlatformContextProvider platform='argocd'>
      <AkuityIntelligenceContextProvider
        organizationMode={false}
        organizationId={settings?.organizationId || ''}
        instanceId={settings?.instanceId || ''}
        includeDisabled={true}
      >
        <ContextedAIAssistant
          application={application}
          resource={resource}
          settings={settings}
          resourceGroup={resourceGroup}
          resourceVersion={resourceVersion}
        />
      </AkuityIntelligenceContextProvider>
    </PlatformContextProvider>
  );
};
