import { faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { useQuery } from '@tanstack/react-query';
import { Flex } from 'antd';

import { akpTextPrimaryColor, featureExtensionNotAllowed } from '@ui/feature/kubevision/const';
import { KubeVision } from '@ui/feature/kubevision/kubevision';
import { PlatformContextProvider } from '@ui/feature/shared/context/platform-context-provider';
import { KubeVisionConfig } from '@ui/lib/apiclient/generated';
import { IconLabel, Loading } from '@ui/lib/components';
import { AkuityIntelligenceContextProvider } from '@ui/lib/context/akuity-intelligence-context';

import { checkAkuityIntelligencePermission } from '../utils';

type Props = {
  organizationId: string;
  instanceId: string;
  allowedUsernames: Array<string>;
  allowedGroups: Array<string>;
  isk8sFeatureOn: boolean;
  isAISREFeatureOn: boolean;
  kubeVisionConfig: KubeVisionConfig;
};

export const KubeVisionExtension = ({
  organizationId,
  instanceId,
  allowedUsernames,
  allowedGroups,
  isk8sFeatureOn,
  isAISREFeatureOn,
  kubeVisionConfig
}: Props) => {
  const { data, isFetching } = useQuery({
    queryKey: ['argo', 'session', 'userinfo'],
    queryFn: async () => {
      const resp = await fetch('/api/v1/session/userinfo');
      return resp.json();
    }
  });

  if (!isFetching && !data?.loggedIn) {
    if (!window.location.pathname.startsWith('/login')) {
      window.location.href = `/login?return_url=${encodeURIComponent(location.href)}`;
    }
    return <></>;
  }

  const isAllowed = checkAkuityIntelligencePermission(
    data?.username,
    data?.groups,
    allowedUsernames,
    allowedGroups
  );

  return (
    <div id='kubevision-extension'>
      {isFetching ? (
        <Flex justify={'center'} className='w-full mt-20' style={{ color: akpTextPrimaryColor }}>
          <Loading />
        </Flex>
      ) : isAllowed ? (
        <PlatformContextProvider platform='argocd'>
          <AkuityIntelligenceContextProvider
            organizationMode={false}
            organizationId={organizationId}
            instanceId={instanceId}
            kubeVisionConfig={kubeVisionConfig}
          >
            <div className={'p-4'}>
              <KubeVision isk8sFeatureOn={isk8sFeatureOn} isAISREFeatureOn={isAISREFeatureOn} />
            </div>
          </AkuityIntelligenceContextProvider>
        </PlatformContextProvider>
      ) : (
        <div className='mx-auto my-40 px-20 py-10 w-[65%] rounded-2xl bg-[#fafafa]'>
          <IconLabel
            icon={faInfoCircle}
            iconSize={'xl'}
            iconClass='mr-8'
            className='text-lg leading-loose'
            label={featureExtensionNotAllowed}
          />
        </div>
      )}
    </div>
  );
};
