import { faEye } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useQuery } from '@tanstack/react-query';

import { IncidentLink } from '@ui/feature/kubevision/components/shared/incident-link';
import { PlatformContextProvider } from '@ui/feature/shared/context/platform-context-provider';
import { AkuityIntelligenceExtension } from '@ui/lib/apiclient/argocd/v1/argocd_pb';
import { FeatureStatus, FeatureStatuses } from '@ui/lib/apiclient/types/features/v1/features_pb';
import { AkuityIntelligenceContextProvider } from '@ui/lib/context/akuity-intelligence-context';
import { NotificationContextProvider } from '@ui/lib/context/notification-context';

import { Application } from '../types';
import { checkAkuityIntelligencePermission } from '../utils';

type KubeVisionStatusPanelButtonsProps = {
  organizationId: string;
  instanceId: string;
  openFlyout: () => void;
  application: Application;
  featureStatuses: FeatureStatuses;
  akuityIntelligenceExtension: AkuityIntelligenceExtension;
};

export const KubeVisionStatusPanelButtons = ({
  organizationId,
  instanceId,
  openFlyout,
  application,
  featureStatuses,
  akuityIntelligenceExtension
}: KubeVisionStatusPanelButtonsProps) => {
  const { data } = useQuery({
    queryKey: ['argo', 'session', 'userinfo'],
    queryFn: async () => {
      const resp = await fetch('/api/v1/session/userinfo');
      return resp.json();
    }
  });

  const isGroupAllowed = checkAkuityIntelligencePermission(
    data?.username,
    data?.groups,
    akuityIntelligenceExtension.allowedUsernames,
    akuityIntelligenceExtension.allowedGroups
  );

  const isKubevisionEnabled =
    featureStatuses?.multiClusterK8sDashboard === FeatureStatus.ENABLED && isGroupAllowed;
  if (!isKubevisionEnabled) {
    return <></>;
  }

  const isAIAllowed =
    featureStatuses.aiSupportEngineer === FeatureStatus.ENABLED &&
    akuityIntelligenceExtension.aiSupportEngineerEnabled &&
    isGroupAllowed;

  return (
    <div className={'application-status-panel__item'}>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5em' }}>
        <label style={{ fontSize: '12px', fontWeight: 600, color: 'rgb(109, 127, 139)' }}>
          AKUITY INTELLIGENCE (BETA)
        </label>
      </div>
      <button className={'argo-button argo-button--base'} onClick={() => openFlyout()}>
        <FontAwesomeIcon icon={faEye} style={{ marginRight: '5px' }} />
        Event Timeline
      </button>
      {isAIAllowed && (
        <PlatformContextProvider platform='argocd'>
          <NotificationContextProvider>
            <AkuityIntelligenceContextProvider
              organizationId={organizationId}
              organizationMode={false}
              instanceId={instanceId}
              disableLoadingAnimation={true}
            >
              <div style={{ marginTop: '10px' }}>
                <IncidentLink
                  instanceId={instanceId}
                  isArgoApplication={true}
                  applicationName={application.metadata.name}
                />
              </div>
            </AkuityIntelligenceContextProvider>
          </NotificationContextProvider>
        </PlatformContextProvider>
      )}
    </div>
  );
};
