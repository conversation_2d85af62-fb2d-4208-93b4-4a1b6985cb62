import { faHatWizard } from '@fortawesome/free-solid-svg-icons/faHatWizard';
import { faPencilAlt } from '@fortawesome/free-solid-svg-icons/faPencilAlt';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

import { Instance } from '@ui/lib/apiclient';
import { useGetArgoCDCSS, useUpdateArgoCDCSS } from '@ui/lib/apiclient/argocd/argocd-queries';
import { Error as ErrorComponent, Loading } from '@ui/lib/components';
import { CodeEditor } from '@ui/lib/components/code-editor';

import { SectionHeader } from '../../section-header';
import { useBlockSettingsNavigation } from '../../utils/use-block-settings-navigation';

import { rawCSSToWizardState, wizardStateToRawCSS } from './parser';
import { CustomStylesContext, WizardState } from './store';
import { maskLongBase64, unmaskLongBase64 } from './utils';
import {
  CustomStylesWizard,
  parserWorker,
  useCustomStylesWizard
} from './wizard/custom-styles-wizard';

export interface CustomStylesSettingsProps {
  instance: Instance;
}

export const CustomStylesSettings = ({ instance }: CustomStylesSettingsProps) => {
  const {
    data: css,
    isLoading: instanceCssFetchLoading,
    error: instanceCssFetchError
  } = useGetArgoCDCSS({ id: instance.id, workspaceId: '' });

  const { mutate, isPending, isSuccess } = useUpdateArgoCDCSS();

  const { handleSubmit, reset, setValue, watch } = useForm<
    { css?: string },
    unknown,
    { css?: string }
  >({
    values: { css }
  });
  const values = watch();

  const [isWizardMode, setIsWizardMode] = useState(true);

  const [wizardState, setWizardState] = useState<WizardState>({});

  useEffect(() => {
    if (!css) {
      return;
    }
    // the wizard state isn't reacting by - useState(rawCSSToWizardState(css, instance.version))
    setWizardState(rawCSSToWizardState(css, instance.version));
  }, [css, instance.version]);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { wizardState: _, ...wizardProps } = useCustomStylesWizard([wizardState, setWizardState]);

  useEffect(() => {
    const onMessage = (e: MessageEvent<{ event: string; result: unknown }>) => {
      const event = e.data.event;
      switch (event) {
        case 'WIZARD_STATE_TO_RAW_CSS':
          setValue('css', e.data.result as string);
          break;
        case 'RAW_CSS_TO_WIZARD_STATE':
          setWizardState(e.data.result as WizardState);
          break;
      }
    };
    parserWorker.addEventListener('message', onMessage);
    return () => parserWorker.removeEventListener('message', onMessage);
  }, []);

  const toggleWizard = () => setIsWizardMode(!isWizardMode);

  const blockSettingsNavigation = useMemo(
    () => values?.css?.replaceAll(' ', '') !== css?.replaceAll(' ', ''),
    [values?.css, css]
  );

  useBlockSettingsNavigation(blockSettingsNavigation, reset);

  const Body = instanceCssFetchLoading ? (
    <Loading />
  ) : instanceCssFetchError ? (
    <ErrorComponent err={instanceCssFetchError} />
  ) : (
    <CustomStylesContext.Provider value={{ wizardState, controllers: wizardProps }}>
      <div className='mb-5'>
        <a onClick={() => toggleWizard()}>
          {isWizardMode && (
            <>
              <FontAwesomeIcon icon={faPencilAlt} /> Edit as CSS
            </>
          )}

          {!isWizardMode && (
            <>
              <FontAwesomeIcon icon={faHatWizard} /> Switch to wizard
            </>
          )}
        </a>
      </div>

      <div
        className={classNames({
          hidden: !isWizardMode
        })}
      >
        <CustomStylesWizard />
      </div>

      <div
        className={classNames({
          hidden: isWizardMode
        })}
      >
        {!isWizardMode && (
          <CodeEditor
            style={{ height: 'calc(100vh - 300px)', minHeight: '500px' }}
            mode={{
              name: 'css',
              options: {}
            }}
            value={maskLongBase64(values.css, wizardState.logo || '') || ''}
            onChange={(value) => setValue('css', unmaskLongBase64(value, wizardState.logo || ''))}
          />
        )}
      </div>
    </CustomStylesContext.Provider>
  );

  const onSubmit = handleSubmit((values) => {
    mutate({
      css: isWizardMode
        ? wizardStateToRawCSS(values.css, wizardState, instance.version)
        : values.css,
      id: instance.id,
      workspaceId: ''
    });
  });

  return (
    <form onSubmit={onSubmit}>
      <SectionHeader
        saveSettings={{
          run: onSubmit,
          loading: isPending,
          done: isSuccess,
          reset: () => {
            reset();
            parserWorker.postMessage({
              event: 'RAW_CSS_TO_WIZARD_STATE',
              payload: {
                css,
                argocdVersion: instance.version
              }
            });
          }
        }}
      >
        Custom Styles
      </SectionHeader>
      {Body}
    </form>
  );
};
