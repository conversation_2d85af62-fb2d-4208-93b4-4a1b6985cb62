import { Tabs } from 'antd';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import yaml from 'yaml';

import { SecretsEditor } from '@ui/instances/components/secrets-editor';
import { Instance } from '@ui/lib/apiclient';
import {
  useGetNotificationSettings,
  usePatchNoficationSecrets,
  useUpdateNotificationConfig
} from '@ui/lib/apiclient/argocd/argocd-queries';
import { Error, Loading } from '@ui/lib/components';
import { CodeEditor } from '@ui/lib/components/code-editor';

import { SectionHeader } from '../section-header';
import { useBlockSettingsNavigation } from '../utils/use-block-settings-navigation';

import { NotificationsWizard } from './notifications-wizard';

type FormValues = { config: string; secretPatch: { [key: string]: string } };

type Props = {
  instance: Instance;
};

export const NotificationsSettings = ({ instance }: Props) => {
  const {
    data: notifications,
    error,
    refetch
  } = useGetNotificationSettings({ id: instance.id, workspaceId: '' });
  const {
    mutateAsync: updateNotificationConfig,
    isPending: isUpdatePending,
    isSuccess: isUpdateSuccess
  } = useUpdateNotificationConfig();
  const {
    mutateAsync: patchNotificationSecrets,
    isPending: isPatchSecretsPending,
    isSuccess: isPatchSecretsSuccess
  } = usePatchNoficationSecrets();

  const notificationsConfig = useMemo(() => {
    if (!notifications) {
      return null;
    }

    if (notifications?.config) {
      const config = { ...(notifications?.config || {}) };
      return yaml.stringify(config);
    }

    return '';
  }, [notifications]);

  const {
    formState: { isDirty },
    handleSubmit,
    reset,
    setValue,
    watch,
    getValues
  } = useForm({
    values: {
      config: notificationsConfig,
      secretPatch: {}
    }
  });

  const values = watch();

  const onSave = async (vals: FormValues) => {
    try {
      const config = yaml.parse(vals.config);
      await updateNotificationConfig({ id: instance.id, workspaceId: '', config });
      await patchNotificationSecrets({ id: instance.id, secret: vals.secretPatch });
    } catch (err) {
      return;
    }

    refetch();
    setValue('secretPatch', {});
  };

  const handleSecretsChange = (secrets: Record<string, string>) => {
    setValue('secretPatch', { ...getValues('secretPatch'), ...secrets });
  };

  useBlockSettingsNavigation(
    isDirty ||
      values.config?.trim() !== notificationsConfig?.trim() ||
      !!Object.keys(values?.secretPatch || {})?.length,
    reset,
    [],
    'Notifications'
  );

  if (notificationsConfig == null || notificationsConfig === undefined) {
    return <Loading />;
  } else if (error) {
    return <Error err={error} />;
  }

  return (
    <>
      <SectionHeader
        saveSettings={{
          loading: isUpdatePending || isPatchSecretsPending,
          done: isUpdateSuccess && isPatchSecretsSuccess,
          run: handleSubmit(onSave),
          reset
        }}
      >
        Notifications
      </SectionHeader>
      <Tabs
        className='-mt-2'
        items={[
          {
            key: 'configuration',
            label: 'Configuration',
            children: (
              <NotificationsWizard
                configYAML={values.config}
                onChange={(val) => setValue('config', val)}
                onSecretsChange={handleSecretsChange}
                instanceID={instance.id}
              />
            )
          },
          {
            key: 'secrets',
            className: 'px-1',
            label: 'Secrets',
            children: (
              <SecretsEditor
                suggestBlackList={['index', 'c', 'c.type', 'c.message']}
                config={values.config}
                secretsPatch={values.secretPatch}
                secrets={notifications?.secret || {}}
                onPatchChanged={(secrets) => setValue('secretPatch', secrets)}
              />
            )
          },
          {
            key: 'yaml',
            label: 'YAML Editor',
            children: (
              <CodeEditor
                style={{ height: 'calc(100vh - 300px)', minHeight: '500px' }}
                name='notificationsConfig'
                mode={{
                  name: 'yaml',
                  options: {}
                }}
                value={values.config || ''}
                onChange={(val) => setValue('config', val)}
              />
            )
          }
        ]}
      />
    </>
  );
};
