import { useEffect } from 'react';
import { Navigate, Outlet } from 'react-router-dom';

import { paths } from '@ui/config/paths';
import { FeatureStatus } from '@ui/lib/apiclient/types/features/v1/features_pb';
import { AkuityIntelligenceContextProvider } from '@ui/lib/context/akuity-intelligence-context';
import { useMainContext } from '@ui/lib/context/main-context';
import { useRequiredParams } from '@ui/lib/hooks/use-required-params';

export const KubeVisionRoutesWrapper = () => {
  const { org } = useRequiredParams<'org'>(['org']);
  const ctx = useMainContext();
  useEffect(() => {
    const organization = ctx?.user?.organizations?.find((o) => o.name === org);
    if (organization?.id !== ctx.currentOrg?.id) {
      ctx.changeCurrentOrg(organization);
    }
  }, [org]);

  if (ctx.currentOrgFeatureStatuses.multiClusterK8sDashboard === FeatureStatus.ENABLED) {
    return (
      <AkuityIntelligenceContextProvider
        currentRole={ctx.currentRole}
        organizationMode={true}
        kubeVisionConfig={null}
        instanceId={''}
        organizationId={ctx.currentOrg.id}
      >
        <Outlet />
      </AkuityIntelligenceContextProvider>
    );
  }

  return <Navigate to={paths.home} />;
};
