import { faCircleInfo } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Flex, Row, TableColumnsType, Tooltip } from 'antd';
import { MouseEvent } from 'react';

import { ExplorerFilter } from '@ui/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter';
import { KubernetesButton } from '@ui/feature/kubevision/components/shared/kubevision-button';
import { TruncateText } from '@ui/feature/kubevision/components/shared/truncate-text';
import { parseSortOrder } from '@ui/feature/kubevision/filter';
import { useGetKubernetesResourceDetailQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { ClusterResource, EnabledCluster } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { ResourceType } from '@ui/lib/apiclient/types/k8s/v1/k8s_pb';
import { EnabledClustersInfo } from '@ui/lib/apiclient/utils';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { akpTextPrimaryColor, DashboardType } from '../../const';
import useCustomSearchParams from '../../hooks/use-custom-search-params';
import { formatTime } from '../../utils';
import { DeprecatedTag } from '../shared/deprecated-tag';
import { createDetailsColumn } from '../shared/kubevision-table-details-column';

import { ArgoCDLink } from './kubevision-explorer-table-argocd';

type RecordType = ClusterResource & Pick<EnabledCluster, 'lastRefreshTime'>;

type NameButtonProps = {
  instanceId: string;
  record: RecordType;
  onFilterChange: (filter: ExplorerFilter) => void;
};

const NameButton = ({ instanceId, record, onFilterChange }: NameButtonProps) => {
  const { enabledClustersInfo } = useAkuityIntelligenceContext();
  const { setSearchParams } = useCustomSearchParams();

  const clusterId = enabledClustersInfo.clusterId({
    instanceId,
    clusterName: record.columns['Cluster']
  });

  const { refetch, isLoading } = useGetKubernetesResourceDetailQuery(
    {
      instanceId,
      resourceId: record.uid,
      clusterId: clusterId
    },
    { enabled: false, staleTime: 1000 }
  );

  const handleClick = async (e: MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    if (record.kind === 'Pod') {
      setSearchParams(
        {
          tab: 'kubevision',
          dashboard: DashboardType.Containers,
          instance: enabledClustersInfo.instanceName(record.instanceId),
          cluster: record.columns['Cluster'],
          podId: record.uid
        },
        { overwrite: true }
      );
    } else {
      const { data } = await refetch();
      const childType = data?.childTypes?.length > 0 ? data.childTypes[0] : null;
      if (!childType) return;
      onFilterChange({
        group: childType.groupVersionKind.group,
        version: childType.groupVersionKind.version,
        kind: childType.groupVersionKind.kind,
        instance: enabledClustersInfo.instanceName(record.instanceId),
        cluster: record.columns['Cluster'],
        namespace: record.columns['Namespace'],
        ownerId: record.uid,
        offset: 0
      });
    }
  };

  const isClickable = record.hasChildObjects ?? false;
  return (
    <Flex align={'center'}>
      <KubernetesButton
        tooltip={
          isClickable ? (
            <>
              <b className={'mr-1'}>{record.kind === 'Pod' ? `Show containers:` : `Filter by:`}</b>
              {record.name}
            </>
          ) : null
        }
        text={record.name}
        onClick={isClickable ? handleClick : undefined}
      />
      {record.deprecatedInfo?.deprecated && (
        <Tooltip
          styles={{ body: { width: 'fit-content' } }}
          title={
            <div className='whitespace-nowrap'>
              {
                <Flex className='px-2 py-3 w-[380px] whitespace-normal break-words' vertical>
                  <strong className='block'>Deprecated: </strong>
                  <span className='mt-2'>{record.deprecatedInfo?.message}</span>
                </Flex>
              }
            </div>
          }
          placement='top'
        >
          <div>
            <DeprecatedTag />
          </div>
        </Tooltip>
      )}
      <div style={{ width: '40px', color: akpTextPrimaryColor }}>
        {isLoading && <Loading text={' '} />}
      </div>
    </Flex>
  );
};

type ParseTableColumnsProps = {
  filter: ExplorerFilter;
  hasNamespace: boolean;
  onFilterChange: (filter: Partial<ExplorerFilter>) => void;
  resourceTypes?: Array<ResourceType>;
  isDeprecatedApiDashboard?: boolean;
  enabledClustersInfo: EnabledClustersInfo;
  onRowClick?: (resource: ClusterResource) => void;
};

export const parseTableColumns = ({
  filter,
  hasNamespace,
  onFilterChange,
  resourceTypes,
  isDeprecatedApiDashboard,
  enabledClustersInfo,
  onRowClick
}: ParseTableColumnsProps): TableColumnsType<RecordType> => {
  const columns: Array<{ name: string; title: string }> = [];
  const resourceType = resourceTypes?.find(
    (t) => t?.groupVersionKind?.group === filter.group && t?.groupVersionKind?.kind === filter.kind
  );
  columns.push(...(resourceType?.columns ?? []));
  if (columns.length === 0) {
    columns.push(
      { name: 'Cluster', title: 'Cluster' },
      { name: 'Namespace', title: 'Namespace' },
      { name: 'Name', title: 'Name' },
      { name: 'Age', title: 'Age' }
    );
  }

  const tableColumns: TableColumnsType<RecordType> = [
    createDetailsColumn({ onRowClick: onRowClick }),
    ...columns.map((column) => {
      return {
        key: column.name,
        title: column.title,
        hidden: (() => {
          if (column.name === 'Instance') {
            return !!filter.instance || !!filter.nodeId;
          }
          if (column.name === 'Cluster') {
            return !!filter.cluster || !!filter.nodeId;
          }
          if (column.name === 'Namespace') {
            return !hasNamespace || !!filter.namespace;
          }
          return false;
        })(),
        width: (() => {
          if (column.name === 'Instance') {
            return 150;
          }
          if (column.name === 'Cluster') {
            return 150;
          }
          if (column.name === 'Namespace') {
            return 150;
          }
          return null;
        })(),
        render: (_: unknown, record: RecordType) => {
          if (column.name === 'Instance') {
            return (
              <>
                {isDeprecatedApiDashboard ? (
                  <TruncateText disableTooltip={true}>{record.columns[column.name]}</TruncateText>
                ) : (
                  <KubernetesButton
                    tooltip={
                      <>
                        <b className={'mr-1'}>Filter by:</b>
                        {record.columns[column.name]}
                      </>
                    }
                    text={record.columns[column.name]}
                    onClick={(e) => {
                      e.stopPropagation();
                      onFilterChange({
                        instance: enabledClustersInfo.instanceName(record.instanceId),
                        cluster: undefined,
                        offset: 0
                      });
                    }}
                  />
                )}
              </>
            );
          }
          if (column.name === 'Cluster') {
            return (
              <>
                {isDeprecatedApiDashboard ? (
                  <TruncateText disableTooltip={true}>{record.columns[column.name]}</TruncateText>
                ) : (
                  <KubernetesButton
                    tooltip={
                      <>
                        <b className={'mr-1'}>Filter by:</b>
                        {record.columns[column.name]}
                      </>
                    }
                    text={record.columns[column.name]}
                    onClick={(e) => {
                      e.stopPropagation();
                      onFilterChange({
                        instance: enabledClustersInfo.instanceName(record.instanceId),
                        cluster: record.columns['Cluster'],
                        offset: 0
                      });
                    }}
                  />
                )}
              </>
            );
          }
          if (column.name === 'Namespace') {
            return (
              <>
                {isDeprecatedApiDashboard ? (
                  <TruncateText disableTooltip={true}>{record.columns[column.name]}</TruncateText>
                ) : (
                  <KubernetesButton
                    tooltip={
                      <>
                        <b className={'mr-1'}>Filter by:</b>
                        {record.columns[column.name]}
                      </>
                    }
                    text={record.columns[column.name]}
                    onClick={(e) => {
                      e.stopPropagation();
                      onFilterChange({
                        instance: enabledClustersInfo.instanceName(record.instanceId),
                        cluster: record.columns['Cluster'],
                        namespace: record.columns[column.name],
                        offset: 0
                      });
                    }}
                  />
                )}
              </>
            );
          }
          if (column.name === 'Name') {
            return (
              <>
                {isDeprecatedApiDashboard ? (
                  <TruncateText disableTooltip={true}>{record.columns[column.name]}</TruncateText>
                ) : (
                  <Flex align={'center'} justify={'space-between'} style={{ height: '32px' }}>
                    <NameButton
                      instanceId={record.instanceId}
                      record={record}
                      onFilterChange={onFilterChange}
                    />
                  </Flex>
                )}
              </>
            );
          }
          if (column.name === 'Age') {
            return (
              <TruncateText className={'block min-w-20'} maxWidth={360}>
                {formatTime(record.columns[column.name], { fromNow: true })}
              </TruncateText>
            );
          }
          return (
            <TruncateText className={'block min-w-20'} maxWidth={360}>
              {record.columns[column.name] ?? '-'}
            </TruncateText>
          );
        }
      };
    })
  ];

  tableColumns.push({
    key: 'argocdApplicationInfo',
    title: 'Argo CD',
    width: 180,
    sorter: true,
    sortOrder: parseSortOrder(filter.orderBy, 'argocdApplicationInfo'),
    render: (_, record) => {
      return (
        <Row>
          <ArgoCDLink
            name={record?.argocdApplicationInfo?.name}
            link={record?.argocdApplicationInfo?.link}
            syncStatus={record?.argocdApplicationInfo?.syncStatus}
            healthStatus={record?.argocdApplicationInfo?.healthStatus}
          />
        </Row>
      );
    }
  });

  tableColumns.push({
    key: 'lastRefreshTime',
    title: (
      <Flex align={'center'}>
        Last Refreshed
        <Tooltip
          title='Shows the last time Akuity Intelligence synced its data for this specific resource. Note that the live data in the Kubernetes cluster might be more recent.'
          placement='top'
        >
          <span className='ml-3 text-[#b1b1b1]'>
            <FontAwesomeIcon icon={faCircleInfo} />
          </span>
        </Tooltip>
      </Flex>
    ),
    render: (_, record) => {
      return (
        <TruncateText className={'block min-w-20'} maxWidth={360}>
          {record.lastRefreshTime}
        </TruncateText>
      );
    }
  });
  return tableColumns;
};
