import { Struct } from '@bufbuild/protobuf';
import { faCircleInfo } from '@fortawesome/free-solid-svg-icons';
import {
  faCalendarAlt,
  faEye,
  faFileCode,
  faHatWizard,
  faListAlt,
  faShoePrints
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Checkbox, Descriptions, DescriptionsProps, Flex, Tabs, TabsProps, Tooltip } from 'antd';
import { useMemo, useState } from 'react';
import type { IAnnotation, IMarker } from 'react-ace';
import yaml from 'yaml';

import { AIAssistant } from '@ui/feature/kubevision/components/shared/ai-assistant';
import { KubeVisionAuditLogs } from '@ui/feature/kubevision/components/shared/kubevision-audit-logs';
import { KubeVisionEventTimeline } from '@ui/feature/kubevision/components/shared/kubevision-event-timeline';
import { KubeVisionEvents } from '@ui/feature/kubevision/components/shared/kubevision-events';
import { ClusterResource } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { CodeEditor } from '@ui/lib/components/code-editor';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { KubernetesLogType, TabType } from '../../const';
import { formatTime, getAPIVersion } from '../../utils';
import { IncidentLink } from '../shared/incident-link';
import { isEventTimelineEnabled } from '../shared/kubevision-event-timeline/helper';
import { KubeVisionLogs } from '../shared/kubevision-log';
import { UTCTimeContextProvider } from '../shared/utctime-context';

import { ExplorerFilter } from './kubevision-explorer-filter';

type Props = {
  resourceId: string;
  instanceId: string;
  clusterId: string;
  resource: ClusterResource;
  object: Struct;
  isWorkload: boolean;
  isUnderDeletion?: boolean;
  detailsTab?: string;
  onFilterChange: (filter: Partial<ExplorerFilter>, overwrite?: boolean) => void;
};

export const ResourceDetailsPanel = ({
  resourceId,
  instanceId,
  clusterId,
  resource,
  object,
  isWorkload,
  isUnderDeletion,
  detailsTab,
  onFilterChange
}: Props) => {
  const { isDarkTheme, organizationMode, enabledClustersInfo } = useAkuityIntelligenceContext();

  const [hideManagedFields, setHideManagedFields] = useState(true);
  const [annotations, setAnnotations] = useState<IAnnotation[]>([]);
  const [markers, setMarkers] = useState<IMarker[]>([]);

  const resourceManifest = useMemo(() => {
    const obj = object.toJson() as { metadata: { managedFields: unknown; finalizers?: string[] } };
    if (obj && hideManagedFields) {
      delete obj?.metadata?.managedFields;
    }
    const yamlString = yaml.stringify(obj);

    // Find the range of the finalizers field for Deletion Dashboard
    if (isUnderDeletion) {
      const lines = yamlString.split('\n');
      const finalizersIndex = lines.findIndex((line) => line.trim().startsWith('finalizers:'));
      if (finalizersIndex !== -1) {
        const startLine = finalizersIndex + 1;
        let endLine = startLine;
        while (endLine < lines.length && lines[endLine].startsWith('    - ')) {
          endLine++;
        }
        setAnnotations([
          {
            row: startLine,
            column: 0,
            type: 'warning',
            text: 'Finalizers may be blocking the resource deletion'
          }
        ]);
        setMarkers([
          {
            startRow: startLine,
            startCol: 0,
            endRow: endLine,
            endCol: lines[endLine - 1].length + 1,
            type: 'fullLine',
            className: 'marker-highlight'
          }
        ]);
      } else {
        setAnnotations([]);
        setMarkers([]);
      }
    } else {
      setAnnotations([]);
      setMarkers([]);
    }

    return yamlString;
  }, [object, hideManagedFields, isUnderDeletion]);

  const tabs: TabsProps['items'] = [];
  if (isEventTimelineEnabled(resource.group, resource.kind)) {
    tabs.push({
      key: TabType.EventTimeline,
      label: (
        <div className={'px-1 flex items-center'}>
          <FontAwesomeIcon icon={faEye} className={'mr-2'} />
          Event Timeline
        </div>
      ),
      children: (
        <UTCTimeContextProvider>
          <KubeVisionEventTimeline
            scope={resource.kind === 'Namespace' ? 'namespace' : 'resource'}
            instanceId={instanceId}
            clusterId={resource.clusterId}
            applicationNames={[]}
            namespace={resource.kind === 'Namespace' ? resource.name : resource.namespace}
            name={resource.name}
            group={resource.group}
            kind={resource.kind}
          />
        </UTCTimeContextProvider>
      )
    });
  }

  tabs.push({
    key: TabType.Manifest,
    label: (
      <div className={'px-1 flex items-center'}>
        <FontAwesomeIcon icon={faFileCode} className={'mr-2'} />
        Manifest
      </div>
    ),
    children: (
      <div>
        <Flex>
          <div className={'grow'}></div>
          <Checkbox
            className='mb-2'
            checked={hideManagedFields}
            onChange={(e) => setHideManagedFields(e.target.checked)}
          >
            Hide Managed Fields
          </Checkbox>
        </Flex>
        <CodeEditor
          style={{ height: 'calc(100vh - 330px)', minHeight: '500px' }}
          theme={isDarkTheme ? 'github_dark' : 'github_light_default'}
          mode={{
            name: 'yaml',
            options: {}
          }}
          value={resourceManifest}
          annotations={annotations}
          markers={markers}
          readOnly
        />
      </div>
    )
  });
  if (isWorkload) {
    tabs.push({
      key: TabType.Logs,
      label: (
        <div className={'px-1 flex items-center'}>
          <FontAwesomeIcon icon={faListAlt} className={'mr-2'} />
          Logs
        </div>
      ),
      children: (
        <KubeVisionLogs
          instanceId={instanceId}
          clusterId={clusterId}
          resourceId={resource.uid}
          logType={KubernetesLogType.Pod}
          toTop={450}
        />
      )
    });
  }
  tabs.push({
    key: TabType.Event,
    label: (
      <div className={'px-1 flex items-center'}>
        <FontAwesomeIcon icon={faCalendarAlt} className={'mr-2'} />
        Events
      </div>
    ),
    children: (
      <KubeVisionEvents instanceId={instanceId} clusterId={clusterId} resourceId={resourceId} />
    )
  });
  tabs.push({
    key: TabType.AIAssistant,
    label: (
      <div className={'px-1 flex items-center'}>
        <FontAwesomeIcon icon={faHatWizard} className={'mr-2'} />
        Assistant
      </div>
    ),
    children: (
      <AIAssistant
        instanceId={instanceId}
        clusterId={clusterId}
        applicationName={resource?.argocdApplicationInfo?.name}
        resourceId={resourceId}
        resourceGroup={resource?.group}
        resourceVersion={resource?.version}
        resourceKind={resource.kind}
        resourceNamespace={resource?.namespace}
        resourceName={resource?.name}
      />
    )
  });
  if (resource.argocdApplicationInfo) {
    tabs.push({
      key: TabType.AuditLogs,
      label: (
        <div className={'px-1 flex items-center'}>
          <FontAwesomeIcon icon={faShoePrints} className={'mr-2'} />
          Audit Logs
        </div>
      ),
      children: (
        <KubeVisionAuditLogs
          instanceId={instanceId}
          clusterId={resource.clusterId}
          resourceId={resource.uid}
        />
      )
    });
  }

  const descriptionsItems: DescriptionsProps['items'] = [
    ...(organizationMode
      ? [
          {
            label: 'Instance',
            children: <div>{enabledClustersInfo.instanceName(instanceId)}</div>
          }
        ]
      : []),
    ...(resource.kind !== 'Namespace'
      ? [
          {
            label: 'Namespace',
            children: <div>{resource.namespace}</div>
          }
        ]
      : []),
    {
      label: 'Name',
      children: <div>{resource.name}</div>
    },
    {
      label: 'API Version',
      children: <div>{getAPIVersion({ group: resource.group, version: resource.version })}</div>
    },
    {
      label: 'Kind',
      children: <div>{resource.kind}</div>
    },
    {
      label: (
        <Flex align={'center'}>
          Last Refreshed
          <Tooltip
            title='Shows the last time Akuity Intelligence synced its data for this specific resource. Note that the live data in the Kubernetes cluster might be more recent.'
            placement='top'
          >
            <span className='ml-3 text-[#b1b1b1]'>
              <FontAwesomeIcon icon={faCircleInfo} />
            </span>
          </Tooltip>
        </Flex>
      ),
      children: (
        <div>
          {formatTime(
            enabledClustersInfo.clusterLastRefreshTime({
              clusterId
            }),
            { fromNow: true }
          )}
        </div>
      )
    }
  ];

  if (isUnderDeletion && resource?.deleteTime) {
    descriptionsItems.push(
      {
        label: 'Deletion Timestamp',
        children: <div>{formatTime(resource.deleteTime)}</div>
      },
      {
        label: 'Deletion Duration',
        children: <div>{formatTime(resource.deleteTime, { fromNow: true }).replace('ago', '')}</div>
      }
    );
  }

  if (resource.kind === 'Namespace') {
    descriptionsItems.push({
      label: 'Incidents',
      children: (
        <IncidentLink
          instanceId={resource.instanceId}
          namespace={resource.name}
          clusterId={resource.clusterId}
          clusterName={enabledClustersInfo.clusterName(resource.clusterId)}
        />
      )
    });
  }

  return (
    <div>
      <Descriptions
        styles={{ label: { fontWeight: 'bold', padding: '12px' } }}
        className='mb-8'
        size='small'
        bordered
        column={2}
        items={descriptionsItems}
      />

      <Tabs
        defaultActiveKey={TabType.Manifest}
        destroyOnHidden
        activeKey={detailsTab}
        items={tabs}
        onChange={(key) => {
          onFilterChange({ detailsTab: key });
        }}
      />
    </div>
  );
};
