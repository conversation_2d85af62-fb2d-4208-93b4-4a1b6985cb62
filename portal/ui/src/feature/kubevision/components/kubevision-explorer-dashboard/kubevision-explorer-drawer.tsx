import { Struct } from '@bufbuild/protobuf';
import { faBox } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Row, Drawer } from 'antd';
import { useLocation } from 'react-router-dom';

import { ExplorerFilter } from '@ui/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter';
import { ResourceDetailsPanel } from '@ui/feature/kubevision/components/kubevision-explorer-dashboard/resource-details';
import { RecentViews } from '@ui/feature/kubevision/components/shared/recent-views';
import { TabType } from '@ui/feature/kubevision/const';
import {
  useGetKubernetesManifestQuery,
  useGetKubernetesResourceDetailQuery
} from '@ui/lib/apiclient/organization/kubevision-queries';
import { ResourceCategory } from '@ui/lib/apiclient/types/k8s/v1/k8s_pb';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { clearQueryParams } from '../../filter';
import { ErrorNotFound } from '../shared/error-not-found';

import { KubeVisionExplorerOwnerPath } from './kubevision-explorer-owner-path';

type Props = {
  instanceId: string;
  filter: ExplorerFilter;
  onFilterChange: (filter: Partial<ExplorerFilter>, overwrite?: boolean) => void;
  isUnderDeletion?: boolean;
};

export const KubeVisionExplorerDrawer = ({
  instanceId,
  filter,
  onFilterChange,
  isUnderDeletion = false
}: Props) => {
  const { enabledClustersInfo } = useAkuityIntelligenceContext();
  const { pathname, search } = useLocation();

  const drawerEnabled = !!filter.resourceId && !!filter.resourceCluster;
  const shouldFetchManifest = drawerEnabled && filter.detailsTab === TabType.Manifest;

  const shard = enabledClustersInfo.instanceShard({ instanceId });
  const clusterId = enabledClustersInfo.clusterId({
    instanceId,
    clusterName: filter.resourceCluster
  });

  const { data: resourceDetails, isLoading: isLoadingDetails } =
    useGetKubernetesResourceDetailQuery(
      {
        instanceId: instanceId,
        resourceId: filter.resourceId,
        clusterId: clusterId
      },
      { enabled: drawerEnabled }
    );

  const { data: manifestData, isLoading: isLoadingManifest } = useGetKubernetesManifestQuery(
    shard,
    {
      instanceId: instanceId,
      resourceId: filter.resourceId,
      clusterId: clusterId
    },
    { enabled: shouldFetchManifest }
  );

  const isLoading =
    filter.detailsTab === TabType.Manifest
      ? isLoadingDetails || isLoadingManifest
      : isLoadingDetails;

  const getResourceData = () => {
    if (!resourceDetails) return null;

    if (filter.detailsTab === TabType.Manifest) {
      return manifestData
        ? {
            resource: manifestData.resource,
            object: manifestData.object
          }
        : null;
    }

    return {
      resource: resourceDetails.resource,
      object: manifestData?.object || new Struct()
    };
  };

  const resourceData = getResourceData();
  const resourceName = resourceData?.resource?.name || 'N/A';

  return (
    <Drawer
      open={drawerEnabled}
      onClose={() => {
        if (filter.treeView) {
          onFilterChange({
            ...clearQueryParams(filter, 'detailsTab'),
            namespace: filter.namespace,
            resourceId: undefined,
            resourceInstance: undefined,
            resourceCluster: undefined
          });
        } else {
          onFilterChange({
            ...clearQueryParams(filter, 'detailsTab'),
            resourceId: undefined,
            resourceInstance: undefined,
            resourceCluster: undefined
          });
        }
      }}
      title={
        <Row justify='start' align='middle'>
          <span>
            <FontAwesomeIcon icon={faBox} className='mr-2' />{' '}
            {`${isLoading ? 'Loading...' : resourceName}`}
          </span>

          <div className='grow' />
          <KubeVisionExplorerOwnerPath
            className='mr-2'
            instanceId={instanceId}
            clusterId={clusterId}
            resourceId={filter.resourceId}
            onFilterChange={onFilterChange}
          />
          <RecentViews
            key={pathname + search}
            resourceName={resourceData?.resource?.name}
            resourceNamespace={resourceData?.resource?.namespace}
            kind={resourceData?.resource?.kind}
          />
        </Row>
      }
      width='95%'
    >
      {isLoading ? (
        <Loading />
      ) : resourceData ? (
        <ResourceDetailsPanel
          resourceId={filter.resourceId}
          instanceId={instanceId}
          clusterId={clusterId}
          resource={resourceData.resource}
          object={resourceData.object}
          isWorkload={
            (resourceData.resource.category ?? ResourceCategory.UNSPECIFIED) ===
            ResourceCategory.WORKLOADS
          }
          isUnderDeletion={isUnderDeletion}
          detailsTab={filter.detailsTab}
          onFilterChange={onFilterChange}
        />
      ) : (
        <ErrorNotFound />
      )}
    </Drawer>
  );
};
