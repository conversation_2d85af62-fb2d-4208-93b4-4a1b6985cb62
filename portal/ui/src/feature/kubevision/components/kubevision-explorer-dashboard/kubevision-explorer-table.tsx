import { ConfigProvider, Flex, Table } from 'antd';
import { forwardRef, useImperativeHandle, useMemo } from 'react';

import { handleTableChange } from '@ui/feature/kubevision/filter';
import {
  useGetKubernetesResourceListQuery,
  useGetKubernetesResourceTypesQuery
} from '@ui/lib/apiclient/organization/kubevision-queries';
import { ClusterResource, EnabledCluster } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { getResourceKey } from '@ui/lib/utils';

import { akpTextDarkThemeColor, akpTextPrimaryColor } from '../../const';
import { formatTime } from '../../utils';

import {
  ExplorerFilter,
  filterToListKubernetesResourcesRequest
} from './kubevision-explorer-filter';
import { parseTableColumns } from './kubevision-explorer-table-columns';
import { KubeVisionExplorerTreeView } from './kubevision-explorer-tree-view';

type RecordType = ClusterResource & Pick<EnabledCluster, 'lastRefreshTime'>;

type Props = {
  instanceId: string;
  filter: ExplorerFilter;
  onFilterChange: (filter: Partial<ExplorerFilter>, overwrite?: boolean) => void;
  onClickResource: (resource: RecordType) => void;
  isDeprecatedApiDashboard?: boolean;
};

export type KubeVisionExplorerTableRef = {
  getFirstResource: () => RecordType | undefined;
  getDataSource: () => RecordType[];
};

export const KubeVisionExplorerTable = forwardRef<KubeVisionExplorerTableRef, Props>(
  ({ instanceId, filter, onFilterChange, onClickResource, isDeprecatedApiDashboard }, ref) => {
    const { isDarkTheme, enabledClustersInfo } = useAkuityIntelligenceContext();

    const { data: resourceTypes, isLoading: isLoadingResourceTypes } =
      useGetKubernetesResourceTypesQuery(instanceId);

    const { data: resources, isLoading: isLoadingResources } = useGetKubernetesResourceListQuery(
      filterToListKubernetesResourcesRequest(enabledClustersInfo, filter, instanceId),
      {
        enabled: filter.treeView ? !!filter.namespace : true
      }
    );

    // Get all resources (unfiltered) for tree view filter comparison
    const hasActiveTreeViewFilters =
      filter.treeView &&
      ((filter.treeViewResourceKinds && filter.treeViewResourceKinds.length > 0) ||
        (filter.treeViewHealthStatuses && filter.treeViewHealthStatuses.length > 0));

    const { data: allResources } = useGetKubernetesResourceListQuery(
      filterToListKubernetesResourcesRequest(
        enabledClustersInfo,
        {
          ...filter,
          treeViewResourceKinds: [],
          treeViewHealthStatuses: [],
          treeViewNameContains: ''
        },
        instanceId
      ),
      {
        enabled: filter.treeView && !!filter.namespace && hasActiveTreeViewFilters
      }
    );

    const dataSource = useMemo(
      () =>
        (resources?.resources ?? []).map(
          (r) =>
            ({
              ...r,
              lastRefreshTime: formatTime(
                enabledClustersInfo.clusterLastRefreshTime({
                  clusterId: r.clusterId
                }),
                { fromNow: true }
              )
            }) as RecordType
        ),
      [resources, enabledClustersInfo]
    );

    useImperativeHandle(ref, () => {
      return {
        getFirstResource: () => dataSource[0],
        getDataSource: () => dataSource
      };
    }, [dataSource]);

    return isLoadingResourceTypes || isLoadingResources ? (
      <Flex justify={'center'} className='w-full mt-20' style={{ color: akpTextPrimaryColor }}>
        <Loading />
      </Flex>
    ) : (
      <>
        {!filter.treeView && (
          <>
            <ConfigProvider
              theme={{
                components: {
                  Table: {
                    colorText: isDarkTheme ? akpTextDarkThemeColor : akpTextPrimaryColor
                  }
                }
              }}
            >
              <Table<RecordType>
                columns={parseTableColumns({
                  filter,
                  hasNamespace: !!resources?.resources[0]?.namespace,
                  onFilterChange,
                  resourceTypes: resourceTypes?.resourceTypes,
                  isDeprecatedApiDashboard,
                  enabledClustersInfo,
                  onRowClick: onClickResource
                })}
                dataSource={dataSource}
                rowKey={(record) => getResourceKey(record)}
                onChange={handleTableChange(onFilterChange)}
                pagination={{
                  pageSizeOptions: [20, 50, 100],
                  pageSize: filter.limit,
                  total: resources?.count ?? 0,
                  current: filter.offset / filter.limit + 1,
                  showSizeChanger: true
                }}
                scroll={{ x: 'max-content' }}
                rowClassName={'cursor-pointer'}
                onRow={(record) => ({
                  onClick: () => onClickResource?.(record)
                })}
              />
            </ConfigProvider>
          </>
        )}

        {filter.treeView && (
          <KubeVisionExplorerTreeView
            isLoadingResources={isLoadingResources}
            resources={resources?.resources}
            onNodeClick={(node) => onClickResource?.(node)}
            allResources={allResources?.resources}
            hasActiveFilters={hasActiveTreeViewFilters}
            currentNamespace={filter.namespace}
            treeViewHealthStatuses={filter.treeViewHealthStatuses}
            onClearFilters={() =>
              onFilterChange?.({
                treeViewResourceKinds: [],
                treeViewHealthStatuses: [],
                treeViewNameContains: ''
              })
            }
          />
        )}
      </>
    );
  }
);
