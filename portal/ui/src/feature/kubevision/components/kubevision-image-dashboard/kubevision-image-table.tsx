import { faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ConfigProvider, Flex, Table, TableColumnsType, Tag, Tooltip } from 'antd';

import {
  filterToListKubernetesImagesRequest,
  ImageFilter
} from '@ui/feature/kubevision/components/kubevision-image-dashboard/kubevision-image-filter';
import { KubernetesButton } from '@ui/feature/kubevision/components/shared/kubevision-button';
import { TruncateText } from '@ui/feature/kubevision/components/shared/truncate-text';
import { handleTableChange, parseSortOrder } from '@ui/feature/kubevision/filter';
import { useGetKubernetesImagesQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { Image, ImageCVEScanResult } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { akpTextDarkThemeColor, akpTextPrimaryColor, TabType } from '../../const';
import { getSeverityColor } from '../../utils';
import { createDetailsColumn } from '../shared/kubevision-table-details-column';
import { KubernetesTableStringFilter } from '../shared/kubevision-table-string-filter';

type Props = {
  instanceId: string;
  filter: ImageFilter;
  onFilterChange: (filter: Partial<ImageFilter>, overwrite?: boolean) => void;
  onClick?: (image: Image) => void;
};

export const KubeVisionImageTable = ({ instanceId, filter, onFilterChange, onClick }: Props) => {
  const { isDarkTheme, kubeVisionConfig, enabledClustersInfo } = useAkuityIntelligenceContext();

  const { data: imagesList, isLoading: isLoadingImageList } = useGetKubernetesImagesQuery(
    filterToListKubernetesImagesRequest(enabledClustersInfo, filter, instanceId)
  );

  const columns: TableColumnsType<Image> = [
    createDetailsColumn({ onRowClick: onClick }),
    {
      key: 'name',
      title: 'Image Name',
      dataIndex: 'name',
      sorter: true,
      sortOrder: parseSortOrder(filter.orderBy, 'name'),
      filtered: !!filter.nameContains,
      filterDropdown: ({ close }: { close: () => void }) => (
        <KubernetesTableStringFilter
          label='Image Name'
          defaultValue={filter.nameContains?.toString() ?? ''}
          onClearFilter={() => {
            onFilterChange({ nameContains: undefined, offset: 0 });
            close();
          }}
          onApplyFilter={(nameLike: string) => {
            onFilterChange({ nameContains: nameLike, offset: 0 });
            close();
          }}
        />
      ),
      render: (text: string) => (
        <KubernetesButton
          tooltip={
            <>
              <b className={'mr-1'}>Filter by:</b>
              {text}
            </>
          }
          text={text}
          maxWidth={360}
          onClick={(e) => {
            e.stopPropagation();
            onFilterChange({ nameContains: text, offset: 0 });
          }}
        />
      )
    },
    {
      key: 'containerCount',
      title: 'Containers',
      dataIndex: 'containerCount',
      sorter: true,
      sortOrder: parseSortOrder(filter.orderBy, 'containerCount'),
      render: (containers: string) => <div>{containers}</div>
    },
    {
      key: 'tag',
      title: 'Image Tag',
      dataIndex: 'tag',
      sorter: true,
      sortOrder: parseSortOrder(filter.orderBy, 'tag'),
      render: (imageTag: string) => (
        <Tag
          bordered={false}
          color={isDarkTheme ? '#262626' : '#e5e5e5'}
          className={'py-1 px-2 text-xs'}
          style={{ color: isDarkTheme ? '#e5e5e5' : '#262626' }}
        >
          {imageTag}
        </Tag>
      )
    },
    {
      key: 'digest',
      title: 'Digest',
      dataIndex: 'digest',
      render: (digest: string) => {
        return (
          <Tag
            bordered={false}
            color={isDarkTheme ? '#262626' : '#e5e5e5'}
            className={'py-1 px-2 text-xs'}
            style={{ color: isDarkTheme ? '#e5e5e5' : '#262626' }}
            onClick={(e) => {
              e.stopPropagation();
              onFilterChange({
                digest: digest ? digest : '_empty',
                offset: 0
              });
            }}
          >
            <TruncateText
              maxWidth={160}
              customTooltip={
                <>
                  <b className={'mr-1'}>Filter by:</b>
                  {digest ? digest : 'empty digest'}
                </>
              }
              alwaysShowTooltip={true}
            >
              {digest ? digest : 'N/A'}
            </TruncateText>
          </Tag>
        );
      }
    },
    {
      key: 'cveCount',
      title: 'CVEs',
      dataIndex: 'cveScanResult',
      sorter: true,
      sortOrder: parseSortOrder(filter.orderBy, 'cveCount'),
      render: (cveScanResult?: ImageCVEScanResult, record?: Image) => {
        if (!!filter.instance && !kubeVisionConfig?.cveScanConfig?.scanEnabled) {
          return (
            <div>
              Disabled
              <Tooltip title='Please enable CVE Scan in the Akuity Intelligence settings.'>
                <FontAwesomeIcon className='ml-2' icon={faInfoCircle} />
              </Tooltip>
            </div>
          );
        }

        if (!cveScanResult) {
          return (
            <div>
              <Tag
                bordered={false}
                color={isDarkTheme ? '#262626' : '#e5e5e5'}
                className={'py-1 px-2 text-xs'}
                style={{ color: isDarkTheme ? '#e5e5e5' : '#262626' }}
              >
                N/A
              </Tag>
              {!filter.instance && (
                <Tooltip
                  classNames={{ root: 'w-[400px]' }}
                  title={
                    'CVEs are unavailable for this image. Ensure CVE scanning is enabled in Akuity Intelligence and only public images are scanned.'
                  }
                >
                  <FontAwesomeIcon className='ml-2' icon={faInfoCircle} />
                </Tooltip>
              )}
            </div>
          );
        }
        if (cveScanResult.cveCount === 0) {
          return (
            <Tag
              color={'green'}
              className={'py-1 px-2 text-xs text-center'}
              style={{ width: '200px' }}
            >
              No Vulnerability Found
            </Tag>
          );
        }
        const text = cveScanResult.cveCount === 1 ? 'Vulnerability Found' : 'Vulnerabilities Found';
        const cveIdText = cveScanResult.cves
          .map((c) => c.vulnerabilityId)
          .filter((v) => !!v)
          .join(', ');
        return (
          <Tag
            color={getSeverityColor(cveScanResult.cves[0].severity)}
            className={'py-1 px-2 text-xs text-center'}
            style={{ width: '200px' }}
            onClick={(e) => {
              e.stopPropagation();
              onFilterChange({
                image: record.name,
                imageTag: record.tag,
                imageDigest: record.digest,
                detailsTab: TabType.Vulnerabilities
              });
            }}
          >
            <TruncateText
              maxWidth={188}
              customTooltip={
                <>{cveIdText.length > 512 ? `${cveIdText.slice(0, 512)}...` : cveIdText}</>
              }
              alwaysShowTooltip={true}
            >
              <FontAwesomeIcon icon={faInfoCircle} className={'mr-1'} /> {cveScanResult.cveCount}{' '}
              {text}
            </TruncateText>
          </Tag>
        );
      }
    }
  ];

  return isLoadingImageList ? (
    <Flex justify={'center'} className='w-full mt-20' style={{ color: akpTextPrimaryColor }}>
      <Loading />
    </Flex>
  ) : (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            colorText: isDarkTheme ? akpTextDarkThemeColor : akpTextPrimaryColor
          }
        }
      }}
    >
      <Table
        columns={columns}
        rowKey={(record) => `${record.name}/${record.tag}/${record.digest}`}
        dataSource={imagesList?.images}
        onRow={(record) => ({
          onClick: () => onClick?.(record)
        })}
        rowClassName={'cursor-pointer'}
        onChange={handleTableChange(onFilterChange)}
        pagination={{
          pageSizeOptions: [20, 50, 100],
          pageSize: filter.limit,
          total: imagesList?.count ?? 0,
          current: filter.offset / filter.limit + 1,
          showSizeChanger: true
        }}
        scroll={{ x: 'max-content' }}
      />
    </ConfigProvider>
  );
};
