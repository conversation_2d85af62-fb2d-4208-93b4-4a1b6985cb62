import { useMemo } from 'react';

import { KubeVisionImageDrawer } from '@ui/feature/kubevision/components/kubevision-image-dashboard/kubevision-image-drawer';
import {
  clearQueryParams,
  generatePrefixedParams,
  mergeFilter
} from '@ui/feature/kubevision/filter';
import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import { Image } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { PageTitle } from '@ui/lib/components/page-title';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { TabType } from '../../const';
import { KubeVisionCheckEnabledClusters } from '../shared/kubevision-check-enabled-clusters';

import { defaultImageFilter, ImageFilter } from './kubevision-image-filter';
import { KubeVisionImageFilterBar } from './kubevision-image-filter-bar';
import { KubeVisionImageTable } from './kubevision-image-table';

type Props = {
  instanceId: string;
};

const KubeVisionImageDashboard = ({ instanceId }: Props) => {
  const { organizationMode } = useAkuityIntelligenceContext();
  const { getSearchParam, getSearchParamPagination, setSearchParams } = useCustomSearchParams();

  const initFilter: Record<string, string | number> = {};
  const baseParams = [
    'instance',
    'cluster',
    'image',
    'imageTag',
    'imageDigest',
    'imageCve',
    'nameContains',
    'digest',
    'orderBy',
    'detailsTab'
  ];

  const containerParamsWithoutPrefix = [
    'nameContains',
    'status',
    'type',
    'orderBy',
    'limit',
    'offset'
  ];

  const containerParams = generatePrefixedParams(TabType.Containers, containerParamsWithoutPrefix);

  const vulParamsWithoutPrefix = ['severity', 'pageSize', 'page'];

  const vulnerabilitiesParams = generatePrefixedParams(
    TabType.Vulnerabilities,
    vulParamsWithoutPrefix
  );

  const params = [...baseParams, ...containerParams, ...vulnerabilitiesParams];

  for (const param of params) {
    if (getSearchParam(param)) initFilter[param] = getSearchParam(param);
  }

  // handle container tab params
  if (getSearchParam('detailsTab') === TabType.Containers) {
    for (const param of containerParamsWithoutPrefix) {
      const prefixedParam = `${TabType.Containers}_${param}`;
      if (getSearchParam(prefixedParam)) {
        initFilter[prefixedParam] = getSearchParam(prefixedParam);
      }
    }
  }

  // handle vulnerabilities ta params
  if (getSearchParam('detailsTab') === TabType.Vulnerabilities) {
    for (const param of vulParamsWithoutPrefix) {
      const prefixedParam = `${TabType.Vulnerabilities}_${param}`;
      if (getSearchParam(prefixedParam)) {
        initFilter[prefixedParam] = getSearchParam(prefixedParam);
      }
    }
  }

  const { limit, offset, orderBy } = getSearchParamPagination();
  initFilter.limit = limit;
  initFilter.offset = offset;
  initFilter.orderBy = orderBy;

  if (getSearchParam('detailsTab') === TabType.Containers) {
    const containerPagination = getSearchParamPagination(`${TabType.Containers}`);
    initFilter[`${TabType.Containers}_limit`] = containerPagination.limit;
    initFilter[`${TabType.Containers}_offset`] = containerPagination.offset;
    initFilter[`${TabType.Containers}_orderBy`] = containerPagination.orderBy;
  }

  const imageFilter = useMemo(() => mergeFilter(defaultImageFilter(), initFilter), [initFilter]);

  const handleFilterChange = (filter: Partial<ImageFilter>, overwrite?: boolean) => {
    const newFilter = overwrite
      ? mergeFilter(defaultImageFilter(), filter)
      : mergeFilter(imageFilter, filter);

    let filterToUse = newFilter;

    // clear params based on detailsTab
    if (newFilter.detailsTab !== TabType.Containers) {
      filterToUse = clearQueryParams(filterToUse, TabType.Containers);
    }
    if (newFilter.detailsTab !== TabType.Vulnerabilities) {
      filterToUse = clearQueryParams(filterToUse, TabType.Vulnerabilities);
    }

    const searchParams: Record<string, string | undefined> = {
      ...Object.fromEntries(
        Object.entries(filterToUse).map(([key, value]) => [key, value?.toString()])
      ),
      instance: organizationMode ? filterToUse?.instance : undefined
    };

    setSearchParams(searchParams);
  };

  const handleRowClick = (image: Image) => {
    handleFilterChange({
      image: image.name,
      imageTag: image.tag,
      imageDigest: image.digest,
      detailsTab: TabType.Containers
    });
  };

  return (
    <>
      <PageTitle>Akuity Intelligence Images</PageTitle>

      <KubeVisionImageDrawer
        instanceId={instanceId}
        filter={imageFilter}
        onFilterChange={handleFilterChange}
      />
      <KubeVisionImageFilterBar
        instanceId={instanceId}
        filter={imageFilter}
        onFilterChange={handleFilterChange}
      />
      <KubeVisionCheckEnabledClusters>
        <KubeVisionImageTable
          instanceId={instanceId}
          filter={imageFilter}
          onFilterChange={handleFilterChange}
          onClick={handleRowClick}
        />
      </KubeVisionCheckEnabledClusters>
    </>
  );
};

export default KubeVisionImageDashboard;
