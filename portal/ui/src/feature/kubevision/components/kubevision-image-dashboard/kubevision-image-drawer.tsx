import { faCopy } from '@fortawesome/free-regular-svg-icons';
import {
  faBox,
  faCircleExclamation,
  faExternalLinkAlt,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Descriptions,
  DescriptionsProps,
  Drawer,
  Flex,
  Row,
  Table,
  Tabs,
  Tag,
  Tooltip
} from 'antd';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { ContainerFilter } from '@ui/feature/kubevision/components/kubevision-container-dashboard/kubevision-container-filter';
import { KubeVisionContainerTable } from '@ui/feature/kubevision/components/kubevision-container-dashboard/kubevision-container-table';
import { ImageFilter } from '@ui/feature/kubevision/components/kubevision-image-dashboard/kubevision-image-filter';
import { TruncateText } from '@ui/feature/kubevision/components/shared/truncate-text';
import { DashboardType, TabType } from '@ui/feature/kubevision/const';
import { clearQueryParams, convertToTabPrefix, mergeFilter } from '@ui/feature/kubevision/filter';
import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import { useGetKubernetesImageDetailQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { IconLabel, Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { useNotificationContext } from '@ui/lib/context/notification-context';

import { getSeverityColor } from '../../utils';
import { ErrorNotFound } from '../shared/error-not-found';
import KubernetesTableRadioFilter from '../shared/kubevision-table-radio-filter';
import { RecentViews } from '../shared/recent-views';

type Props = {
  instanceId: string;
  filter: ImageFilter;
  onFilterChange: (filter: Partial<ImageFilter>, overwrite?: boolean) => void;
};

const KubeVisionImageDrawer = ({ instanceId, filter, onFilterChange }: Props) => {
  const { kubeVisionConfig, isDarkTheme, enabledClustersInfo } = useAkuityIntelligenceContext();
  const { pathname, search } = useLocation();

  const notification = useNotificationContext();
  const { setSearchParams } = useCustomSearchParams();
  const [containerFilter, setContainerFilter] = useState<ContainerFilter>({
    image: filter.image,
    imageTag: filter.imageTag,
    imageDigest: filter.imageDigest ? filter.imageDigest : '_empty',
    limit: 20,
    offset: 0,
    detailsTab: filter.detailsTab,
    detailsTab_containers_status: filter.detailsTab_containers_status,
    detailsTab_containers_type: filter.detailsTab_containers_type,
    detailsTab_containers_nameContains: filter.detailsTab_containers_nameContains,
    detailsTab_containers_orderBy: filter.detailsTab_containers_orderBy,
    detailsTab_containers_limit: filter.detailsTab_containers_limit,
    detailsTab_containers_offset: filter.detailsTab_containers_offset
  });
  const [vulnerabilitySeverityFilter, setVulnerabilitySeverityFilter] = useState<string | null>(
    filter.detailsTab_vulnerabilities_severity || null
  );

  const { data: image, isLoading: isLoadingImage } = useGetKubernetesImageDetailQuery(
    {
      instanceId,
      clusterId: undefined,
      imageId: `${filter.image}|${filter.imageTag}|${filter.imageDigest}`
    },
    { enabled: !!filter.image && !!filter.imageTag && !!filter.imageDigest }
  );

  useEffect(() => {
    setContainerFilter((prevFilter) => ({
      ...prevFilter,
      image: filter.image,
      imageTag: filter.imageTag,
      imageDigest: filter.imageDigest ? filter.imageDigest : '_empty',
      limit: 20,
      offset: 0,
      detailsTab: filter.detailsTab,
      detailsTab_containers_status: filter.detailsTab_containers_status,
      detailsTab_containers_type: filter.detailsTab_containers_type,
      detailsTab_containers_nameContains: filter.detailsTab_containers_nameContains,
      detailsTab_containers_orderBy: filter.detailsTab_containers_orderBy,
      detailsTab_containers_limit: filter.detailsTab_containers_limit,
      detailsTab_containers_offset: filter.detailsTab_containers_offset
    }));
  }, [filter]);

  useEffect(() => {
    if (filter.detailsTab === TabType.Containers) {
      onFilterChange(containerFilter);
    }
  }, [
    containerFilter.detailsTab_containers_nameContains,
    containerFilter.detailsTab_containers_status,
    containerFilter.detailsTab_containers_type,
    containerFilter.detailsTab_containers_orderBy,
    containerFilter.detailsTab_containers_limit,
    containerFilter.detailsTab_containers_offset
  ]);

  const parseImageLink = (text: string) => {
    text = text ?? '';
    const chunks = text.split('/');
    let link = `https://hub.docker.com/u/${text}`;
    if (chunks.length >= 3) {
      if (chunks[0] === 'quay.io') {
        link = text.replace(/^quay\.io/, 'https://quay.io/repository');
      } else {
        link = `https://${text}`;
      }
    } else if (chunks.length == 2) {
      link = `https://hub.docker.com/r/${text}`;
    }
    return link;
  };

  const descriptionsItems: DescriptionsProps['items'] = [
    {
      label: 'Image Name',
      children: (
        <a href={parseImageLink(image?.image?.name || '')} target='_blank' className={'underline'}>
          {image?.image?.name || ''}:{image?.image?.tag || ''}
          <FontAwesomeIcon icon={faExternalLinkAlt} className={'ml-2'} />
        </a>
      )
    },
    {
      label: 'Digest',
      children: (
        <Flex>
          {filter.imageDigest ? (
            <TruncateText
              maxWidth={'100%'}
              customTooltip={<div>Click to copy</div>}
              alwaysShowTooltip={true}
            >
              <a
                className={'underline'}
                onKeyDown={() => {}}
                onClick={async () => {
                  await navigator.clipboard.writeText(image?.image?.digest ?? '');
                  notification.success({ message: 'Digest is copied to clipboard.' });
                }}
              >
                {image?.image?.digest ?? ''}
                <FontAwesomeIcon icon={faCopy} className={'ml-2'} />
              </a>
            </TruncateText>
          ) : (
            'N/A'
          )}
        </Flex>
      )
    },
    {
      label: 'Container Count',
      children: <div>{image?.image?.containerCount || 0}</div>
    },
    {
      label: 'Vulnerability Count',
      children: (() => {
        if (!!filter.instance && !kubeVisionConfig?.cveScanConfig?.scanEnabled) {
          return (
            <div>
              Disabled
              <Tooltip title='Please enable CVE Scan in the Akuity Intelligence settings.'>
                <FontAwesomeIcon className='ml-2' icon={faInfoCircle} />
              </Tooltip>
            </div>
          );
        }

        if (!image?.image?.cveScanResult) {
          return (
            <div>
              <Tag
                bordered={false}
                color={isDarkTheme ? '#262626' : '#e5e5e5'}
                className={'py-1 px-2 text-xs'}
                style={{ color: isDarkTheme ? '#e5e5e5' : '#262626' }}
              >
                N/A
              </Tag>
              {!filter.instance && (
                <Tooltip
                  classNames={{ root: 'w-[400px]' }}
                  title={
                    'CVEs are unavailable for this image. Ensure CVE scanning is enabled in Akuity Intelligence and only public images are scanned.'
                  }
                >
                  <FontAwesomeIcon className='ml-2' icon={faInfoCircle} />
                </Tooltip>
              )}
            </div>
          );
        }

        return <div>{image?.image?.cveScanResult?.cveCount || 0}</div>;
      })()
    }
  ];

  const tabs = [
    {
      key: TabType.Containers,
      label: (
        <div className={'px-1 flex items-center'}>
          <FontAwesomeIcon icon={faBox} className={'mr-2'} />
          Containers
        </div>
      ),
      children: (
        <div>
          {containerFilter.image && (
            <div className={'grow overflow-auto'}>
              <KubeVisionContainerTable
                instanceId={instanceId}
                filter={containerFilter}
                onFilterChange={(filter) =>
                  setContainerFilter(mergeFilter(containerFilter, filter))
                }
                noInteractive={true}
                onClick={(container) =>
                  setSearchParams(
                    {
                      tab: 'kubevision',
                      dashboard: DashboardType.Containers,
                      containerId: container.id,
                      containerInstance: enabledClustersInfo.instanceName(container.instanceId),
                      containerCluster: enabledClustersInfo.clusterName(container.clusterId)
                    },
                    { overwrite: true }
                  )
                }
              />
            </div>
          )}
        </div>
      )
    },
    {
      key: TabType.Vulnerabilities,
      label: (
        <div className={'px-1 flex items-center'}>
          <FontAwesomeIcon icon={faCircleExclamation} className={'mr-2'} />
          Vulnerabilities
        </div>
      ),
      children:
        !!filter.instance && !kubeVisionConfig?.cveScanConfig?.scanEnabled ? (
          <div
            className={`mx-auto my-40 px-20 py-10 w-[65%] rounded-2xl ${isDarkTheme ? 'bg-[#1f1f1f]' : 'bg-[#fafafa]'}`}
          >
            <IconLabel
              icon={faInfoCircle}
              iconSize={'xl'}
              iconClass='mr-8'
              className='text-lg leading-loose'
              label={'CVE scanning is not enabled. Please enable it in Intelligence settings.'}
            />
          </div>
        ) : (
          <div>
            {image?.image?.cveScanResult?.cveLastScanTime && (
              <div className='text-sm text-gray-500'>
                Last Scan:{' '}
                {image?.image?.cveScanResult?.cveLastScanTime?.toDate()?.toISOString() || 'N/A'}
              </div>
            )}
            {image?.image?.cveScanResult?.cveCount > 100 && (
              <div className='text-sm text-gray-500'>
                A total of <b>{image?.image?.cveScanResult?.cveCount}</b> vulnerabilities were
                identified in this image. Only the first 100 are displayed.
              </div>
            )}
            <Table
              className='mt-2'
              columns={[
                {
                  key: 'cve',
                  title: 'CVE',
                  width: 200,
                  render: (_, record) => record.vulnerabilityId || 'N/A'
                },
                {
                  key: 'severity',
                  title: 'Severity',
                  width: 160,
                  render: (_, record) => (
                    <Tag color={getSeverityColor(record.severity)}>
                      {record.severity?.toUpperCase() || 'N/A'}
                    </Tag>
                  ),
                  filtered: !!vulnerabilitySeverityFilter,
                  filterDropdown: ({ close }: { close: () => void }) => {
                    return (
                      <KubernetesTableRadioFilter
                        label={'Severity'}
                        defaultValue={vulnerabilitySeverityFilter}
                        options={[
                          { label: 'Critical', value: 'CRITICAL' },
                          { label: 'High', value: 'HIGH' }
                          // At this moment, we only scan critical and high severity vulnerabilities.
                          // It may enable for medium and low severity vulnerabilities in the future.
                          // { label: 'Medium', value: 'MEDIUM' },
                          // { label: 'Low', value: 'LOW' }
                        ]}
                        onClearFilter={() => {
                          setVulnerabilitySeverityFilter(null);
                          onFilterChange({
                            [convertToTabPrefix(TabType.Vulnerabilities, 'severity')]: undefined
                          });
                          close();
                        }}
                        onApplyFilter={(value: string) => {
                          setVulnerabilitySeverityFilter(value);
                          onFilterChange({
                            [convertToTabPrefix(TabType.Vulnerabilities, 'severity')]: value
                          });
                          close();
                        }}
                      />
                    );
                  }
                },
                {
                  key: 'title',
                  title: 'Title',
                  width: 400,
                  render: (_, record) => record.title || 'N/A'
                },
                {
                  key: 'installedVersion',
                  title: 'Installed Version',
                  width: 200,
                  render: (_, record) => record.installedVersion || 'N/A'
                },
                {
                  key: 'fixedVersion',
                  title: 'Fixed Version',
                  width: 200,
                  render: (_, record) => record.fixedVersion || 'N/A'
                },
                {
                  key: 'details',
                  title: 'Details',
                  width: 300,
                  render: (_, record) => (
                    <a href={record.primaryUrl} target='_blank' className={'underline'}>
                      {record.primaryUrl}
                      <FontAwesomeIcon icon={faExternalLinkAlt} className={'ml-2'} />
                    </a>
                  )
                }
              ]}
              scroll={{ x: 'max-content' }}
              rowKey={(record) => record.vulnerabilityId}
              dataSource={
                image?.image?.cveScanResult?.cves?.filter((cve) => {
                  if (!vulnerabilitySeverityFilter) {
                    return true;
                  }
                  return cve.severity === vulnerabilitySeverityFilter;
                }) ?? []
              }
              pagination={{
                pageSizeOptions: [20, 50],
                current: filter.detailsTab_vulnerabilities_page || 1,
                pageSize: filter.detailsTab_vulnerabilities_pageSize || 20,
                total: Math.min(image?.image?.cveScanResult?.cveCount || 0, 100),
                onChange: (page, pageSize) => {
                  onFilterChange({
                    [convertToTabPrefix(TabType.Vulnerabilities, 'page')]: page,
                    [convertToTabPrefix(TabType.Vulnerabilities, 'pageSize')]: pageSize
                  });
                }
              }}
            />
          </div>
        )
    }
  ];

  return (
    <Drawer
      open={!!filter.image && !!filter.imageTag}
      onClose={() => {
        onFilterChange({
          ...clearQueryParams(filter, 'detailsTab'),
          image: undefined,
          imageTag: undefined,
          imageDigest: undefined
        });
      }}
      title={
        <Row justify='space-between' align='middle'>
          <span>
            <FontAwesomeIcon icon={faBox} className='mr-2' /> {`${filter.image}:${filter.imageTag}`}
          </span>

          <RecentViews key={pathname + search} />
        </Row>
      }
      width='90%'
    >
      <div className={'flex flex-col h-full'}>
        {isLoadingImage ? (
          <Flex justify={'center'} className='w-full mt-20'>
            <Loading />
          </Flex>
        ) : image ? (
          <>
            <Descriptions
              styles={{ label: { fontWeight: 'bold', padding: '12px' } }}
              className='mb-6'
              size='small'
              bordered
              column={2}
              items={descriptionsItems}
            />
            <Tabs
              destroyOnHidden
              activeKey={filter.detailsTab || TabType.Containers}
              items={tabs}
              onChange={(key) => {
                const updates: Partial<ImageFilter> = { detailsTab: key as TabType };
                if (key !== 'vulnerabilities') {
                  updates.detailsTab_vulnerabilities_severity = undefined;
                  setVulnerabilitySeverityFilter(null);
                }
                onFilterChange({ detailsTab: key as TabType });
              }}
            />
          </>
        ) : (
          <ErrorNotFound />
        )}
      </div>
    </Drawer>
  );
};

export { KubeVisionImageDrawer };
