import { FilterBar } from '@ui/feature//kubevision/components/shared/filter-bar';
import { FilterTag } from '@ui/feature/kubevision/components/shared/filter-tag';
import { usePlatformContext } from '@ui/feature/shared/context/platform-context';
import { getQueryParamsAsString } from '@ui/lib/apiclient/utils';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { ClusterSelect } from '../shared/cluster-select';
import { InstanceSelect } from '../shared/instance-select';

import {
  defaultImageFilter,
  filterToListKubernetesImagesRequest,
  ImageFilter
} from './kubevision-image-filter';

type Props = {
  instanceId: string;
  filter: ImageFilter;
  onFilterChange: (filter: Partial<ImageFilter>, overwrite?: boolean) => void;
};

export const KubeVisionImageFilterBar = ({ instanceId, filter, onFilterChange }: Props) => {
  const { apiPathPrefix, openUrlWithCookies } = usePlatformContext();
  const { organizationMode, organizationId, enabledClustersInfo } = useAkuityIntelligenceContext();

  const onExportToCSV = () => {
    const params = filterToListKubernetesImagesRequest(enabledClustersInfo, filter, instanceId);
    const url = `${apiPathPrefix}stream/orgs/${organizationId}/k8s/images-csv?${getQueryParamsAsString(params)}`;
    openUrlWithCookies(url, '_blank');
  };

  const clusterId = enabledClustersInfo.clusterId({
    instanceId,
    clusterName: filter.cluster
  });

  return (
    <FilterBar
      onExportToCSV={onExportToCSV}
      onReset={() => onFilterChange(defaultImageFilter(), true)}
      firstRow={
        <>
          {organizationMode && (
            <InstanceSelect
              instanceId={instanceId}
              showAllOption={true}
              onChange={(instanceId) =>
                onFilterChange({
                  instance: enabledClustersInfo.instanceName(instanceId),
                  cluster: undefined,
                  offset: 0
                })
              }
            />
          )}

          <ClusterSelect
            instanceId={instanceId}
            clusterId={clusterId}
            showAllOption={true}
            onChange={(clusterId, instanceId) =>
              onFilterChange({
                instance: enabledClustersInfo.instanceName(instanceId),
                cluster: enabledClustersInfo.clusterName(clusterId),
                offset: 0
              })
            }
          />
        </>
      }
      secondRow={
        filter.digest && (
          <>
            <FilterTag
              hidden={!filter.digest}
              text={
                <>
                  <strong className='mr-1'>Digest: </strong>
                  {filter.digest == '_empty' ? 'empty digest' : filter.digest}
                </>
              }
              onClear={() =>
                onFilterChange({
                  digest: undefined,
                  offset: 0
                })
              }
            />
          </>
        )
      }
    />
  );
};
