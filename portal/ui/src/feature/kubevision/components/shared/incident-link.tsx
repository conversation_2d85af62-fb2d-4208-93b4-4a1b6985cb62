import { faExternalLink, faFire } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Tag } from 'antd';
import { CSSProperties } from 'react';
import { useSearchParams } from 'react-router-dom';

import { useListAIConversationsQuery } from '@ui/lib/apiclient/organization/ai-queries';
import { IncidentStatus } from '@ui/lib/apiclient/organization/v1/organization_pb';
type Props = {
  instanceId?: string;
  clusterId?: string;
  namespace?: string;
  clusterName?: string;
  applicationName?: string;
  isArgoApplication?: boolean;
};

export const IncidentLink = ({
  instanceId = '',
  clusterId = '',
  namespace = '',
  clusterName = '',
  applicationName = '',
  isArgoApplication = false
}: Props) => {
  const [, setSearchParams] = useSearchParams();
  const { data, isLoading } = useListAIConversationsQuery({
    incidentOnly: true,
    incidentStatus: IncidentStatus.UNRESOLVED,
    instanceId,
    application: applicationName,
    namespace: namespace,
    clusterId: clusterId
  });

  const { data: conversationData } = useListAIConversationsQuery({
    incidentOnly: false,
    incidentStatus: IncidentStatus.UNSPECIFIED,
    application: '',
    namespace: '',
    titleContains: '',
    offset: 0,
    limit: 1
  });

  const unresolved = Number(data?.count ?? 0);
  const firstUnresolved = data?.conversations?.[0]?.id;
  const firstConversation = conversationData?.conversations?.[0];

  const handleIncidentLink = () => {
    if (isArgoApplication) {
      const newSearchParams = new URLSearchParams(window.location.search);
      // use dummy value to open the drawer
      newSearchParams.set('akuity-chat', firstUnresolved || firstConversation?.id);
      newSearchParams.set('akuity-chat-history', 'true');
      newSearchParams.set('akuity-chat-incidentOnly', 'true');
      newSearchParams.set('akuity-chat-incidentStatus', 'unresolved');
      newSearchParams.set('akuity-chat-application', applicationName);
      newSearchParams.set('akuity-chat-namespace', namespace || '');
      newSearchParams.set('akuity-chat-offset', '0');
      setSearchParams(newSearchParams);
    } else {
      const newSearchParams = new URLSearchParams(window.location.search);
      newSearchParams.set('tab', 'kubevision');
      newSearchParams.set('dashboard', 'Incidents');
      newSearchParams.set('namespace', namespace || '');
      newSearchParams.set('cluster', clusterName || '');
      setSearchParams(newSearchParams);
    }
  };

  const IncidentCount = ({ style }: { style?: CSSProperties }) => (
    <span style={{ ...style }}>
      <FontAwesomeIcon icon={faFire} className='mr-1' />
      {unresolved}
    </span>
  );

  return (
    <>
      {!isLoading &&
        (isArgoApplication ? (
          <button
            className={`argo-button argo-button--base cursor-pointer w-full ${unresolved > 0 ? 'bg-danger-button' : ''}`}
            onClick={handleIncidentLink}
          >
            <IncidentCount style={{ marginRight: '5px' }} />
            Incident{unresolved > 1 ? 's' : ''}
            <FontAwesomeIcon icon={faExternalLink} style={{ marginLeft: '10px' }} />
          </button>
        ) : (
          <Tag
            className={`cursor-pointer rounded-2xl text-sm px-4 py-1 ${unresolved > 0 ? 'bg-danger-button' : ''}`}
            onClick={handleIncidentLink}
          >
            <IncidentCount style={{ marginRight: '5px' }} />
            Incident{unresolved > 1 ? 's' : ''}
            <FontAwesomeIcon icon={faExternalLink} style={{ marginLeft: '10px' }} />
          </Tag>
        ))}
    </>
  );
};
