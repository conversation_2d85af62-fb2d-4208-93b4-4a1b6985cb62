import { fa<PERSON><PERSON><PERSON><PERSON> } from '@fortawesome/free-regular-svg-icons/faFaceLaugh';
import { faFaceMeh } from '@fortawesome/free-regular-svg-icons/faFaceMeh';
import { faCheck } from '@fortawesome/free-solid-svg-icons/faCheck';
import { faPaperPlane } from '@fortawesome/free-solid-svg-icons/faPaperPlane';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { keepPreviousData } from '@tanstack/react-query';
import { Button, Input, InputRef } from 'antd';
import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';

import {
  useGetKubernetesAssistantSuggestionQuery,
  useResolveAssistantConversationMutation
} from '@ui/lib/apiclient/organization/kubevision-queries';
import { AssistantMessage } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { renderMarkdown } from '@ui/lib/utils';

import './ai-assistant.less';

export const AIAssistant = ({
  instanceId,
  clusterId,
  applicationName,
  resourceId,
  resourceGroup,
  resourceVersion,
  resourceKind,
  resourceNamespace,
  resourceName
}: {
  instanceId: string;
  clusterId: string;
  applicationName: string;
  resourceId: string;
  resourceGroup: string;
  resourceVersion: string;
  resourceKind: string;
  resourceNamespace: string;
  resourceName: string;
}) => {
  const { isDarkTheme, enabledClustersInfo } = useAkuityIntelligenceContext();
  const shard = enabledClustersInfo.instanceShard({ instanceId });

  const [reqId, setReqId] = useState<number>(0);
  const [req, setReq] = useState<string>();
  const [conversationState, setConversationState] = useState<string>();
  const [messages, setMessages] = useState<AssistantMessage[]>([]);
  const [userInput, setUserInput] = useState<string>('');
  const inputRef = useRef<InputRef>(null);

  const {
    mutate: sendConversationFeedback,
    isPending: sendingConversationFeedback,
    error: sendConversationFeedbackError,
    isSuccess: sentConversationFeedback
  } = useResolveAssistantConversationMutation({
    instanceId: instanceId,
    clusterId: clusterId,
    resourceId: resourceId
  });

  const {
    data: assistantResponse,
    isLoading,
    isPlaceholderData,
    error: assistantResponseError,
    isError
  } = useGetKubernetesAssistantSuggestionQuery(
    reqId,
    shard,
    {
      instanceId: instanceId,
      clusterId: clusterId,
      resourceId: resourceId,
      applicationName: applicationName,
      question: req,
      resource: {
        apiVersion: `${resourceGroup}/${resourceVersion}`,
        kind: resourceKind,
        namespace: resourceNamespace,
        name: resourceName
      },
      state: conversationState,
      messages: messages
    },
    {
      placeholderData: keepPreviousData
    }
  );

  useEffect(() => {
    if (isError) {
      let errMsg: string = (assistantResponseError as { message: string })?.message || '';
      if (!errMsg.includes('reached OpenAI tokens limit')) {
        errMsg = 'Internal error, please try again.';
      }
      setConversationState(errMsg);
      setMessages([
        ...messages,
        AssistantMessage.fromJson({
          content: errMsg,
          role: 'assistant'
        })
      ]);
    }
  }, [isError, assistantResponseError]);

  useEffect(() => {
    setConversationState(assistantResponse?.state);
    setMessages(assistantResponse?.messages || []);
  }, [isLoading, assistantResponse]);

  let totalPromptsFromUser = 0;
  let totalResponseFromAssistant = 0;

  for (const message of assistantResponse?.messages || []) {
    switch (message.role) {
      case 'user':
        totalPromptsFromUser++;
        break;
      case 'assistant':
        if (totalPromptsFromUser >= 1) {
          totalResponseFromAssistant++;
        }
        break;
    }
  }

  const showFeedbackWizard =
    !sentConversationFeedback &&
    totalPromptsFromUser + totalResponseFromAssistant >= 2 &&
    !sendingConversationFeedback &&
    !sendConversationFeedbackError;

  const loading = isLoading || isPlaceholderData;

  const sendReq = (input: string) => {
    setMessages(
      (messages || []).concat(AssistantMessage.fromJson({ content: input, role: 'user' }))
    );
    setReq(input);
    setReqId(reqId + 1);
  };

  const sendUserInput = () => {
    if (userInput !== '' && !loading) {
      sendReq(userInput);
      setUserInput('');
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  const onSendConversationFeedback = () =>
    sendConversationFeedback({ resolved: true, state: assistantResponse.state });

  return (
    <div className='ai-assistant'>
      <ul className='ai-assistant__chat'>
        {(messages || [])
          .filter((item) => ['assistant', 'user'].includes(item.role) && !!item.content)
          .map((msg, i) => (
            <li key={i} className={msg.role}>
              <span className={isDarkTheme ? 'theme-dark' : ''} style={{ maxWidth: '100%' }}>
                <div style={{ zIndex: 100, position: 'relative' }}>
                  <div
                    className={'assistant-message'}
                    dangerouslySetInnerHTML={{ __html: renderMarkdown(msg.content) }}
                  ></div>
                </div>
              </span>
            </li>
          ))}
        {loading && (
          <li className='assistant'>
            <span style={{ maxWidth: '100%' }}>
              <div className='typing'>
                <i></i>
                <i></i>
                <i></i>
              </div>
            </span>
          </li>
        )}
      </ul>
      <div>
        <div className='relative'>
          <Input
            disabled={loading}
            ref={inputRef}
            value={userInput}
            placeholder='Type your question...'
            onChange={(e) => setUserInput(e.target.value)}
            onPressEnter={(e) => {
              // Prevent default behavior if shift key is pressed or if the input is in composition mode
              // is composition mode is important for asian languages
              if (!e.shiftKey && !e.nativeEvent.isComposing) {
                e.preventDefault();
                sendUserInput();
              }
            }}
          />
          <button className='ai-assistant__send' onClick={sendUserInput}>
            <FontAwesomeIcon icon={faPaperPlane} />
          </button>
        </div>
        {showFeedbackWizard && (
          <div className='ai-assistant__feedback'>
            <span>Is this conversation helpful?</span>
            <a onClick={onSendConversationFeedback} onKeyDown={() => {}} className={'underline'}>
              <FontAwesomeIcon icon={faFaceLaugh} /> yes
            </a>
            <a onClick={onSendConversationFeedback} onKeyDown={() => {}} className={'underline'}>
              <FontAwesomeIcon icon={faFaceMeh} /> no
            </a>
          </div>
        )}
        {!showFeedbackWizard && sentConversationFeedback && (
          <div className='ai-assistant__feedback'>
            <FontAwesomeIcon icon={faCheck} className='mr-1' /> Thank you for your feedback
          </div>
        )}
        <div className='ml-4 mt-2'>
          <span>Ask me to</span>
          {assistantResponse?.suggestedQuestions?.map((q, i) => (
            <Button
              size={'small'}
              className={classNames('ml-1 mr-1', { 'cursor-default': loading })}
              onClick={() => sendReq(q)}
              onKeyDown={() => {}}
              key={i}
            >
              {q.toLocaleLowerCase()}
            </Button>
          ))}
          or ask any other question.
        </div>
      </div>
    </div>
  );
};
