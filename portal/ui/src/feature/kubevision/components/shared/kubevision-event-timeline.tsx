import { Timestamp } from '@bufbuild/protobuf';
import { ChartData, ChartDataset, ScriptableLineSegmentContext } from 'chart.js';
import moment from 'moment';
import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { Line } from 'react-chartjs-2';
import { useSearchParams } from 'react-router-dom';

import 'chartjs-adapter-moment';

import { HoverSegmentPlugin } from '@ui/feature/kubevision/components/shared/kubevision-event-timeline/chart-hover-plugin';
import { TimelinePlugin } from '@ui/feature/kubevision/components/shared/kubevision-event-timeline/chart-timeline-plugin';
import { EventInfoPanel } from '@ui/feature/kubevision/components/shared/kubevision-event-timeline/event-info-panel';
import { EventsFilterBar } from '@ui/feature/kubevision/components/shared/kubevision-event-timeline/events-filter-bar';
import { EventsTable } from '@ui/feature/kubevision/components/shared/kubevision-event-timeline/events-table';
import {
  chartTimeUnit,
  eventTypeToString,
  filterEventsByTimeRange,
  formatChartTimeUnit,
  groupByEventType
} from '@ui/feature/kubevision/components/shared/kubevision-event-timeline/helper';
import {
  TimeRangeOption,
  useTimeRangeFilter
} from '@ui/feature/kubevision/components/shared/time-range';
import { useGetKubernetesTimelineEventsQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import {
  TimelineEvent,
  TimelineEventSeverity,
  TimelineEventType
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components/loading';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { TabType } from '../../const';
import { useUrlStates } from '../../hooks/use-url-states';
import { formatTime } from '../../utils';

import { EventModal } from './kubevision-event-timeline/event-modal';
import { useUTCTimeContext } from './utctime-context';

type KubeVisionEventTimelineProps = {
  scope: 'namespace' | 'resource';
  instanceId: string;
  clusterId: string;
  applicationNames: string[];
  namespace: string;
  name: string;
  group: string;
  kind: string;
};

type CustomChartContext = ScriptableLineSegmentContext & {
  p0?: {
    raw?: {
      event?: TimelineEvent;
    };
  };
};

export const KubeVisionEventTimeline = ({
  scope,
  instanceId,
  clusterId,
  applicationNames,
  namespace,
  name,
  group,
  kind
}: KubeVisionEventTimelineProps) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [hasAdjustedTimeRange, setHasAdjustedTimeRange] = useState(false);
  const defaultTimeRange = useMemo(
    () => [moment().subtract(6, 'hours').toDate(), moment().toDate()],
    []
  );

  const { isDarkTheme } = useAkuityIntelligenceContext();
  const { useUTCTime } = useUTCTimeContext();

  const [urlStates, setUrlStates] = useUrlStates(
    {
      startTime: {
        key: 'startTime',
        defaultValue: defaultTimeRange[0],
        serialize: (date: Date) => date.toISOString(),
        deserialize: (value: string) => new Date(value)
      },
      endTime: {
        key: 'endTime',
        defaultValue: defaultTimeRange[1],
        serialize: (date: Date) => date.toISOString(),
        deserialize: (value: string) => new Date(value)
      },
      limit: {
        key: 'limit',
        defaultValue: 20,
        serialize: String,
        deserialize: (value: string) => parseInt(value)
      },
      offset: {
        key: 'offset',
        defaultValue: 0,
        serialize: String,
        deserialize: (value: string) => parseInt(value)
      }
    },
    TabType.EventTimeline
  );

  const timeFilter = useTimeRangeFilter({
    defaultOptionValue: useMemo(
      () => ({
        value:
          urlStates.startTime !== defaultTimeRange[0] || urlStates.endTime !== defaultTimeRange[1]
            ? 'custom'
            : '6-hours',
        label:
          urlStates.startTime !== defaultTimeRange[0] || urlStates.endTime !== defaultTimeRange[1]
            ? 'Custom'
            : 'Past 6 Hours',
        startTime: urlStates.startTime,
        endTime: urlStates.endTime
      }),
      [urlStates.startTime, urlStates.endTime, defaultTimeRange]
    )
  });

  const [timeRangeStack, setTimeRangeStack] = useState([
    [timeFilter.startTime, timeFilter.endTime]
  ]);
  const [hoveredIdx, setHoveredIdx] = useState<{
    eventId: string;
    onTimeline: boolean;
  } | null>(null);
  const isDragging = useRef(false);
  const [specChangesModalEvent, setSpecChangesModalEvent] = useState<TimelineEvent | null>(null);
  const timelineHeight = 30;
  const chartContainerRef = useRef<HTMLDivElement>(null);

  const { data: timelineEvents, isLoading: isTimelineEventsLoading } =
    useGetKubernetesTimelineEventsQuery({
      instanceId,
      clusterIds: [clusterId],
      namespaces: scope === 'namespace' ? [namespace] : [],
      timelineResourceIds:
        scope === 'resource' && applicationNames.length === 0
          ? [`${group}|${kind}|${namespace}|${name}`]
          : [],
      applicationNames: scope === 'resource' && applicationNames.length > 0 ? applicationNames : [],
      startTime: timeFilter.startTime ? Timestamp.fromDate(timeFilter.startTime) : undefined,
      endTime: timeFilter.endTime ? Timestamp.fromDate(timeFilter.endTime) : undefined
    });

  const updateTimeParams = useCallback(
    (startTime: Date, endTime: Date) => {
      setUrlStates((prev) => ({
        ...prev,
        startTime,
        endTime,
        offset: 0 // Reset offset when time range changes
      }));
    },
    [setUrlStates]
  );

  const resetToDefaultTimeRange = useCallback(() => {
    const defaultOption: TimeRangeOption = {
      value: '6-hours',
      label: 'Past 6 Hours',
      startTime: defaultTimeRange[0],
      endTime: defaultTimeRange[1]
    };

    timeFilter.onChange(defaultOption);
    setTimeRangeStack([[defaultTimeRange[0], defaultTimeRange[1]]]);
    updateTimeParams(defaultTimeRange[0], defaultTimeRange[1]);
  }, [defaultTimeRange, timeFilter, updateTimeParams]);

  // Handle initial time parameters
  useEffect(() => {
    const currentTab = searchParams.get('detailsTab');
    if (currentTab === TabType.EventTimeline) {
      const hasTimeParams =
        searchParams.has('detailsTab_eventTimeline_startTime') &&
        searchParams.has('detailsTab_eventTimeline_endTime');

      if (!hasTimeParams && !hasAdjustedTimeRange) {
        updateTimeParams(defaultTimeRange[0], defaultTimeRange[1]);
      }
    }
  }, [searchParams, defaultTimeRange, updateTimeParams, hasAdjustedTimeRange]);

  // Handle tab changes
  useEffect(() => {
    const currentTab = searchParams.get('detailsTab');
    const previousTab = searchParams.get('previousTab');

    if (currentTab !== TabType.EventTimeline && previousTab === TabType.EventTimeline) {
      timeFilter.onClearFilter();
      setTimeRangeStack([[defaultTimeRange[0], defaultTimeRange[1]]]);
      updateTimeParams(defaultTimeRange[0], defaultTimeRange[1]);
    }
  }, [searchParams, defaultTimeRange, timeFilter, updateTimeParams]);

  // Handle critical events in the last hour
  useEffect(() => {
    if (!hasAdjustedTimeRange && timelineEvents?.events) {
      // Don't auto-adjust if user already has a custom time range (e.g., from page refresh)
      const isCustomRange = timeFilter.rangeOption.value === 'custom';
      if (isCustomRange) {
        return;
      }

      const lastHourEvents = timelineEvents.events.filter(
        (e) =>
          e.severity === TimelineEventSeverity.CRITICAL &&
          moment(e.timestamp?.toDate()).isAfter(moment().subtract(1, 'hours'))
      );

      if (lastHourEvents.length > 0) {
        const newStartTime = moment().subtract(1, 'hours').toDate();
        const newEndTime = moment().toDate();
        const newOption: TimeRangeOption = {
          value: '1-hour',
          label: 'Past 1 Hour',
          startTime: newStartTime,
          endTime: newEndTime
        };

        timeFilter.onChange(newOption);
        setTimeRangeStack([[newStartTime, newEndTime]]);
        setHasAdjustedTimeRange(true);
        updateTimeParams(newStartTime, newEndTime);
      }
    }
  }, [timelineEvents, hasAdjustedTimeRange, timeFilter, updateTimeParams]);

  useEffect(() => {
    const currentTab = searchParams.get('detailsTab');
    if (currentTab === TabType.EventTimeline) {
      const hasTimeParams =
        searchParams.has('detailsTab_eventTimeline_startTime') &&
        searchParams.has('detailsTab_eventTimeline_endTime');

      if (!hasTimeParams) {
        const now = moment();
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set(
          'detailsTab_eventTimeline_startTime',
          now.clone().subtract(6, 'hours').toDate().toISOString()
        );
        newSearchParams.set('detailsTab_eventTimeline_endTime', now.toDate().toISOString());
        setSearchParams(newSearchParams, { replace: true });
      }
    }
  }, [searchParams]);

  const uiGroupByTimelineName = scope === 'namespace' || applicationNames?.length > 0;
  const uiShowNamespace = applicationNames?.length > 0;
  const events = filterEventsByTimeRange(
    timelineEvents?.events,
    timeRangeStack[timeRangeStack.length - 1]
  );
  const eventGroups = groupByEventType(
    events,
    hoveredIdx?.eventId,
    uiGroupByTimelineName,
    uiShowNamespace
  );

  const data: ChartData<'line', { x: string; y: string; event: TimelineEvent }[]> = {
    datasets: eventGroups.map(
      (
        e: { eventType: TimelineEventType; events: TimelineEvent[]; timelineName?: string },
        idx: number
      ): ChartDataset<'line', { x: string; y: string; event: TimelineEvent }[]> => {
        const dataPoints: Array<{ x: string; y: string; event: TimelineEvent }> = [];
        for (let i = 0; i < e.events.length; i++) {
          const timestampString = e.events[i].timestamp?.toDate()?.toISOString();
          const lastTimestampString = e.events[i].lastTimestamp?.toDate()?.toISOString();
          const timestamp = timestampString;
          const lastTimestamp =
            lastTimestampString || timeRangeStack[timeRangeStack.length - 1][1].toISOString();
          dataPoints.push({
            x: timestamp,
            y: idx.toString(),
            event: e.events[i]
          });
          dataPoints.push({
            x: lastTimestamp,
            y: idx.toString(),
            event: e.events[i]
          });
          dataPoints.push({
            x: null,
            y: idx.toString(),
            event: e.events[i]
          });
        }

        return {
          label: e.eventType.toString(),
          data: dataPoints,
          pointRadius: 0,
          segment: {
            borderCapStyle: 'round' as const,
            borderWidth: timelineHeight,
            borderColor: (ctx: CustomChartContext) => {
              let hovered = false;
              const event = ctx?.p0?.raw?.event;
              if (hoveredIdx?.eventId == event?.id && !!hoveredIdx?.eventId) {
                hovered = true;
              }
              if (
                event?.eventType != TimelineEventType.HEALTH_CHANGED &&
                event?.severity == TimelineEventSeverity.INFO
              ) {
                return !hovered ? '#d9d9d9' : '#bfbfbf';
              }
              if (event?.severity == TimelineEventSeverity.INFO)
                return !hovered ? '#b7eb8f' : '#95de64';
              if (event?.severity == TimelineEventSeverity.WARNING)
                return !hovered ? '#ffe799' : '#ffd666';
              if (event?.severity == TimelineEventSeverity.CRITICAL)
                return !hovered ? '#ffccc7' : '#ffa39e';
              return !hovered ? '#d9d9d9' : '#bfbfbf';
            }
          }
        };
      }
    )
  };

  const options: unknown = {
    animation: false,
    maintainAspectRatio: false,
    scales: {
      x: {
        type: 'time',
        time: {
          unit: chartTimeUnit(timeRangeStack[timeRangeStack.length - 1]),
          timezone: useUTCTime ? 'UTC' : undefined
        },
        adapters: {
          date: {
            zone: useUTCTime ? 'UTC' : undefined
          }
        },
        min: timeRangeStack[timeRangeStack.length - 1][0],
        max: timeRangeStack[timeRangeStack.length - 1][1],
        grid: {
          display: false
        },
        ticks: {
          minRotation: 0,
          maxRotation: 0,
          autoSkip: true,
          callback: function (value: number) {
            const time = moment(value);
            const format = formatChartTimeUnit(timeRangeStack[timeRangeStack.length - 1]);
            return formatTime(time.toDate(), { useUTCTime, format });
          }
        }
      },
      y: {
        type: 'category',
        labels: ['', ...eventGroups.map((e, idx) => idx.toString()), ''],
        ticks: {
          display: true,
          callback: (idx: number) => {
            if (idx == 0 || idx == eventGroups.length + 1) return '';
            const group = eventGroups[idx - 1];
            if (uiGroupByTimelineName && group.eventType == TimelineEventType.HEALTH_CHANGED) {
              return group.timelineName;
            }
            return eventTypeToString(group?.eventType, true);
          }
        },
        border: { display: false },
        grid: {
          display: true,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          lineWidth: (ctx: any) => {
            if (!ctx?.tick?.label) return 0;
            return 1;
          }
        }
      }
    },
    plugins: {
      tooltip: { enabled: false },
      legend: { display: false },
      zoom: {
        zoom: {
          drag: { enabled: true },
          mode: 'x',
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onZoomComplete: (opt: any) => {
            isDragging.current = true;
            const xScale = opt.chart.scales.x;
            const currentRange = xScale.max - xScale.min;
            const minRange = 0.5 * 60 * 1000; // 30 seconds

            let min = xScale.min;
            let max = xScale.max;
            if (currentRange < minRange) {
              const center = (min + max) / 2;
              min = center - minRange / 2;
              max = center + minRange / 2;
            }

            // zoom cannot be less or greater than the current time range
            if (min < timeRangeStack[timeRangeStack.length - 1][0].valueOf()) {
              min = timeRangeStack[timeRangeStack.length - 1][0].valueOf();
            }
            if (max > timeRangeStack[timeRangeStack.length - 1][1].valueOf()) {
              max = timeRangeStack[timeRangeStack.length - 1][1].valueOf();
            }

            const xMin = moment(min);
            const xMax = moment(max);

            setTimeRangeStack([...timeRangeStack, [xMin.toDate(), xMax.toDate()]]);

            opt.chart.zoomScale('x', { min, max }, 'none');
            opt.chart.update();
            setUrlStates({ offset: 0 });
          }
        }
      },
      timeline: {
        dataLabel: (datasetIdx: number) => {
          const group = eventGroups?.[datasetIdx];
          return (
            eventTypeToString(group?.eventType, true) +
            `${group.timelineName ? ' - ' + group.timelineName : ''}`
          );
        }
      }
    }
  };

  return (
    <>
      <EventModal
        isDarkTheme={isDarkTheme}
        event={specChangesModalEvent}
        onCancel={() => setSpecChangesModalEvent(null)}
      />
      <EventsFilterBar
        rangeOption={timeFilter.rangeOption}
        startTime={timeRangeStack[timeRangeStack.length - 1][0]}
        endTime={timeRangeStack[timeRangeStack.length - 1][1]}
        onChange={(opt) => {
          timeFilter.onChange(opt);
          setTimeRangeStack([[opt.startTime, opt.endTime]]);
          updateTimeParams(opt.startTime, opt.endTime);
        }}
        showBackButton={timeRangeStack.length > 1}
        onBack={() => {
          const newStack = [...timeRangeStack];
          newStack.pop();
          setTimeRangeStack(newStack);
          const [lastStartTime, lastEndTime] = newStack[newStack.length - 1];
          updateTimeParams(lastStartTime, lastEndTime);
        }}
        onReset={resetToDefaultTimeRange}
      />
      {isTimelineEventsLoading ? (
        <div className='flex justify-center items-center h-full'>
          <Loading />
        </div>
      ) : (
        <>
          <div
            ref={chartContainerRef}
            style={{
              width: '100%',
              height: `${150 + (eventGroups?.length ?? 0) * timelineHeight}px`,
              marginTop: `${-timelineHeight}px`
            }}
            onKeyDown={() => {}}
            onClick={() => {
              if (!hoveredIdx?.eventId || !hoveredIdx.onTimeline || isDragging.current) return;
              setSpecChangesModalEvent(
                timelineEvents?.events?.find((e) => e.id == hoveredIdx?.eventId) ?? null
              );
              setHoveredIdx(null);
            }}
          >
            <EventInfoPanel
              enabled={hoveredIdx != null && hoveredIdx.onTimeline}
              timelineElement={chartContainerRef.current}
              event={timelineEvents?.events?.find((e) => e.id == hoveredIdx?.eventId)}
            />
            <Line
              height={150 + (eventGroups?.length ?? 0) * timelineHeight}
              data={data}
              options={options}
              plugins={[
                TimelinePlugin({
                  lineHeight: timelineHeight
                }),
                HoverSegmentPlugin({
                  container: () => chartContainerRef.current,
                  lineHeight: timelineHeight,
                  onHover: (hovered, event) => {
                    if (!hovered) {
                      setHoveredIdx(null);
                    } else if (event && event.id != hoveredIdx?.eventId) {
                      setHoveredIdx(hovered ? { eventId: event.id, onTimeline: true } : null);
                      isDragging.current = false;
                    }
                    return false;
                  }
                })
              ]}
            />
          </div>
          <EventsTable
            className='mt-2'
            events={events}
            showNamespace={uiShowNamespace}
            onClickDetail={(record) => setSpecChangesModalEvent(record)}
            rowClassName={(record) =>
              record.id == hoveredIdx?.eventId
                ? isDarkTheme
                  ? 'bg-[#252525AA]'
                  : 'bg-[#fafafa]'
                : ''
            }
            onMouseEnter={(record) => setHoveredIdx({ eventId: record.id, onTimeline: false })}
            onMouseLeave={() => setHoveredIdx(null)}
          />
        </>
      )}
    </>
  );
};
