import { Tag, Tooltip } from 'antd';
import * as React from 'react';

import { TruncateText } from '@ui/feature/kubevision/components/shared/truncate-text';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { akpTextDarkThemeColor, akpTextPrimaryColor } from '../../const';

type Props = {
  onClick?: React.MouseEventHandler<HTMLElement>;
  tooltip?: React.ReactNode;
  text: React.ReactNode;
  maxWidth?: number;
  className?: string;
};

export const KubernetesButton = ({ onClick, tooltip, text, maxWidth, className }: Props) => {
  const { isDarkTheme } = useAkuityIntelligenceContext();

  return (
    <Tag
      className={`${className || 'rounded-2xl text-sm px-2 py-1'}  ${onClick ? 'cursor-pointer' : 'bg-transparent'}`}
      bordered={!!onClick}
      onClick={onClick}
    >
      <div
        className='px-2'
        style={{ color: isDarkTheme ? akpTextDarkThemeColor : akpTextPrimaryColor }}
      >
        {tooltip ? (
          <Tooltip
            styles={{ body: { width: 'fit-content' } }}
            title={
              <div
                className='whitespace-nowrap overflow-hidden text-ellipsis'
                style={{ maxWidth: '400px' }}
              >
                {tooltip}
              </div>
            }
          >
            <div style={{ width: 'fit-content', maxWidth: maxWidth ? `${maxWidth}px` : '' }}>
              <TruncateText maxWidth={maxWidth} disableTooltip={true}>
                {text}
              </TruncateText>
            </div>
          </Tooltip>
        ) : (
          <div style={{ width: 'fit-content', maxWidth: maxWidth ? `${maxWidth}px` : '' }}>
            <TruncateText maxWidth={maxWidth}>{text}</TruncateText>
          </div>
        )}
      </div>
    </Tag>
  );
};
