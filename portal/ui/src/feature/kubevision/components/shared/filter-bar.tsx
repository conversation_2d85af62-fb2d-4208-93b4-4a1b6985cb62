import { faFileCsv } from '@fortawesome/free-solid-svg-icons/faFileCsv';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Flex, Row } from 'antd';
import { ReactNode } from 'react';

import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

type Props = {
  firstRow?: ReactNode;
  secondRow?: ReactNode;
  treeView?: boolean;
  onExportToCSV?: () => void;
  onReset?: () => void;
  hideResetButton?: boolean;
};

export const FilterBar = ({
  firstRow,
  secondRow,
  treeView,
  onReset,
  onExportToCSV,
  hideResetButton
}: Props) => {
  const { isDarkTheme } = useAkuityIntelligenceContext();

  return (
    <Flex
      className={`w-full mb-3 p-4 rounded-lg overflow-x-auto ${isDarkTheme ? 'bg-[#141414]' : 'bg-[#fafafa]'}`}
      justify='start'
      align='middle'
      wrap={false}
    >
      <Flex className='grow' wrap={false} vertical={true} justify='start' align='middle'>
        {firstRow && (
          <Row justify='start' align='middle' wrap={false}>
            {firstRow}
          </Row>
        )}
        {secondRow && (
          <Row
            className='mt-3 pt-3 border-t border-b-0 border-l-0 border-r-0 border-solid border-[#e5e7eb]'
            justify='start'
            align='middle'
            wrap={false}
          >
            {secondRow}
          </Row>
        )}
      </Flex>

      {!treeView && onExportToCSV && (
        <Flex className='ml-2 w-[160px]' vertical={true} justify='center' align='middle'>
          <Button
            type='default'
            size='middle'
            style={{ width: 'fit-content' }}
            onClick={() => onExportToCSV?.()}
          >
            <FontAwesomeIcon icon={faFileCsv} />
            Export as CSV
          </Button>
        </Flex>
      )}

      {!hideResetButton && (
        <Flex className='ml-2 w-[80px]' vertical={true} justify='center' align='middle'>
          <Button type='primary' size='middle' onClick={() => onReset?.()}>
            Reset
          </Button>
        </Flex>
      )}
    </Flex>
  );
};
