import { faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { ReactNode, useMemo } from 'react';

import { usePlatformContext } from '@ui/feature/shared/context/platform-context';
import { IconLabel } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { featureNotAllowed, featureNotEnabledDescription } from '../../const';

type KubeVisionCheckEnabledClustersProps = {
  children: ReactNode;
};

export const KubeVisionCheckEnabledClusters = ({
  children
}: KubeVisionCheckEnabledClustersProps) => {
  const { platform } = usePlatformContext();
  const { isDarkTheme, instanceId, enabledClustersInfo, permissionCheckerAkp } =
    useAkuityIntelligenceContext();
  const enabledClusterIds = enabledClustersInfo.clusterIds({ instanceId });

  const label = useMemo(() => {
    if (platform !== 'akuity-platform') {
      return featureNotEnabledDescription;
    }

    return permissionCheckerAkp.can({
      action: 'get',
      object: 'organization/kubernetes-dashboard',
      resource: '*'
    })
      ? featureNotEnabledDescription
      : featureNotAllowed;
  }, [platform, permissionCheckerAkp]);

  if (enabledClusterIds?.length) {
    return <div>{children}</div>;
  }

  return (
    <div
      className={`mx-auto my-40 px-20 py-10 w-[65%] rounded-2xl ${isDarkTheme ? 'bg-[#1f1f1f]' : 'bg-[#fafafa]'}`}
    >
      <IconLabel
        icon={faInfoCircle}
        iconSize={'xl'}
        iconClass='mr-8'
        className='text-lg leading-loose'
        label={label}
      />
    </div>
  );
};
