import { Tabs } from 'antd';
import { useCallback, useEffect, useRef, useState, useMemo } from 'react';

import { DashboardType, TabType } from '@ui/feature/kubevision/const';
import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import { formatTime } from '@ui/feature/kubevision/utils';
import { ClusterResource } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { useSpotlightSearch } from '@ui/lib/hooks/use-spotlight-search';
import { getResourceKey } from '@ui/lib/utils';

import { isEventTimelineEnabled } from '../kubevision-event-timeline/helper';

import { ResultItem } from './result-item';

type SearchResourcesProps = {
  instanceId: string;
  query: string;
  closeModal: () => void;
  onLoadingChanged?: (isLoading: boolean) => void;
};

type ResultTabProps = {
  key: string;
  name: string;
};

const resultTabs: ResultTabProps[] = [
  { key: 'all', name: 'All' },
  { key: 'resources', name: 'Resources' },
  { key: 'applications', name: 'ArgoCD Applications' },
  { key: 'projects', name: 'Kargo Projects' },
  { key: 'containers', name: 'Containers' },
  { key: 'images', name: 'Images' },
  { key: 'nodes', name: 'Nodes' }
];

const isK8sResource = (resource: ClusterResource) => {
  return resource && resource.group !== 'dashboard.akuity.io';
};

const isContainer = (resource: ClusterResource) => {
  return resource && resource.kind === 'Container' && resource.group === 'dashboard.akuity.io';
};

const isImage = (resource: ClusterResource) => {
  return resource && resource.kind === 'Image' && resource.group === 'dashboard.akuity.io';
};

const isNode = (resource: ClusterResource) => {
  return resource && resource.kind === 'Node' && resource.group === '';
};

const isKargoProject = (resource: ClusterResource) => {
  return resource && resource.kind === 'Project' && resource.group === 'kargo.akuity.io';
};

const isAkuityManagedApplication = (resource: ClusterResource) => {
  return resource && resource.kind === 'Application' && resource.group === 'dashboard.akuity.io';
};

export const SearchResources = ({
  instanceId,
  query,
  closeModal,
  onLoadingChanged
}: SearchResourcesProps) => {
  const { setSearchParams } = useCustomSearchParams();
  const { enabledClustersInfo, organizationMode, organizationId } = useAkuityIntelligenceContext();
  const [activeTab, setActiveTab] = useState<string>('all');

  const { resources, isLoading, setQuery } = useSpotlightSearch(organizationId, instanceId);

  useEffect(() => {
    setQuery(query);
  }, [query]);

  useEffect(() => {
    onLoadingChanged?.(isLoading);
  }, [isLoading, onLoadingChanged]);

  const onAction = useCallback(
    (resource: ClusterResource) => {
      if (isAkuityManagedApplication(resource) && resource.argocdApplicationInfo?.link) {
        window.open(resource.argocdApplicationInfo.link, '_blank');
      } else if (isContainer(resource)) {
        setSearchParams(
          {
            tab: 'kubevision',
            dashboard: DashboardType.Containers,
            containerId: resource.uid,
            containerInstance: enabledClustersInfo.instanceName(resource.instanceId),
            containerCluster: enabledClustersInfo.clusterName(resource.clusterId)
          },
          { overwrite: true }
        );
      } else if (isImage(resource)) {
        setSearchParams(
          {
            tab: 'kubevision',
            dashboard: DashboardType.Images,
            image: resource.columns['image'],
            imageTag: resource.columns['tag'],
            imageDigest: resource.columns['digest']
          },
          { overwrite: true }
        );
      } else if (isNode(resource)) {
        setSearchParams(
          {
            tab: 'kubevision',
            dashboard: DashboardType.Infrastructure,
            nodeId: resource.uid,
            nodeInstance: enabledClustersInfo.instanceName(resource.instanceId),
            nodeCluster: enabledClustersInfo.clusterName(resource.clusterId)
          },
          { overwrite: true }
        );
      } else if (isKargoProject(resource)) {
        window.open(resource.columns['Link'], '_blank');
      } else {
        setSearchParams(
          {
            tab: 'kubevision',
            dashboard: DashboardType.Explorer,
            resourceId: resource.uid,
            resourceInstance: enabledClustersInfo.instanceName(resource.instanceId),
            resourceCluster: enabledClustersInfo.clusterName(resource.clusterId),
            detailsTab: isEventTimelineEnabled(resource.group, resource.kind)
              ? TabType.EventTimeline
              : TabType.Manifest
          },
          { overwrite: true }
        );
      }
      closeModal();
    },
    [enabledClustersInfo, setSearchParams, closeModal]
  );

  const getFilteredResources = (tabKey: string) => {
    if (tabKey === 'all') {
      return resources;
    }
    return resources.filter((resource) => {
      if (tabKey === 'applications') {
        return isAkuityManagedApplication(resource);
      }
      if (tabKey === 'resources') {
        return isK8sResource(resource);
      }
      if (tabKey === 'containers') {
        return isContainer(resource);
      }
      if (tabKey === 'images') {
        return isImage(resource);
      }
      if (tabKey === 'nodes') {
        return isNode(resource);
      }
      if (tabKey === 'projects') {
        return isKargoProject(resource);
      }
      return false;
    });
  };

  const filteredResources = useMemo(() => {
    return getFilteredResources(activeTab);
  }, [resources, activeTab]);

  const localResourcesLength = filteredResources?.length ?? 0;
  const [selectedIdx, setSelectedIdx] = useState(-1);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelectedIdx(0);
  }, [query]);

  useEffect(() => {
    const scrollTo = (idx: number) => {
      const parent = scrollContainerRef.current;
      const children = parent?.getElementsByClassName('spotlight-search-result-item');
      const child = children?.[idx] as HTMLDivElement;
      if (!parent || !child) return;
      const scrollPosition = child.offsetTop - parent.clientHeight / 2 + child.clientHeight / 2;
      parent.scrollTo({ top: scrollPosition, behavior: 'smooth' });
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        event.preventDefault();
        event.stopPropagation();
        const currentIndex = resultTabs.findIndex((tab) => tab.key === activeTab);
        const nextIndex = event.shiftKey
          ? (currentIndex - 1 + resultTabs.length) % resultTabs.length
          : (currentIndex + 1) % resultTabs.length;
        setActiveTab(resultTabs[nextIndex].key);
        return;
      }

      if (event.key === 'ArrowDown' || (event.ctrlKey && event.key === 'n')) {
        event.preventDefault();
        event.stopPropagation();
        if (selectedIdx < localResourcesLength - 1) {
          setSelectedIdx((prev) => prev + 1);
          scrollTo(selectedIdx + 1);
        }
      }
      if (event.key === 'ArrowUp' || (event.ctrlKey && event.key === 'p')) {
        event.preventDefault();
        event.stopPropagation();
        if (selectedIdx > 0) {
          setSelectedIdx((prev) => prev - 1);
          scrollTo(selectedIdx - 1);
        }
      }
      if (event.key === 'Enter') {
        event.preventDefault();
        event.stopPropagation();
        onAction(filteredResources?.[selectedIdx]);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [query, scrollContainerRef, selectedIdx, filteredResources, localResourcesLength, activeTab]);

  const items = resultTabs.map((tab) => ({
    key: tab.key,
    label: tab.name
  }));

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  return (
    <>
      <Tabs activeKey={activeTab} onChange={handleTabChange} items={items} size='small' />
      <div
        className='overflow-y-auto'
        style={{ height: '400px', maxHeight: '400px' }}
        ref={scrollContainerRef}
      >
        {filteredResources?.map((resource, idx) => {
          const isImageResource = isImage(resource);
          const isContainerResource = isContainer(resource);
          const isKargoProjectResource = isKargoProject(resource);
          return (
            <ResultItem
              key={getResourceKey(resource)}
              name={resource.name}
              suffix={
                resource.columns['Age'] && !isImageResource
                  ? formatTime(resource.columns['Age'], { fromNow: true })
                  : undefined
              }
              tags={[
                ...(organizationMode && !isImageResource
                  ? [{ key: 'Instance', value: resource.columns['Instance'] }]
                  : []),
                ...(resource.columns['Cluster'] && !isImageResource && !isKargoProjectResource
                  ? [{ key: 'Cluster', value: resource.columns['Cluster'] }]
                  : []),
                ...(resource.columns['Namespace'] && !isImageResource && !isContainerResource
                  ? [{ key: 'Namespace', value: resource.columns['Namespace'] }]
                  : []),
                ...(resource.columns['podName'] && !isKargoProjectResource
                  ? [{ key: 'Pod', value: resource.columns['podName'] }]
                  : [])
              ]}
              type={resource.kind}
              isSelected={idx === selectedIdx}
              onClick={() => onAction(resource)}
            />
          );
        })}
      </div>
    </>
  );
};
