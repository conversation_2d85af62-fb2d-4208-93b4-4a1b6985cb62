import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

type ResultItemProps = {
  name: string;
  suffix?: string;
  tags?: Array<{ key: string; value: string }>;
  type?: string;
  isSelected?: boolean;
  onClick?: () => void;
};

export const ResultItem = ({ name, suffix, tags, type, isSelected, onClick }: ResultItemProps) => {
  const { isDarkTheme } = useAkuityIntelligenceContext();
  const hoverBgColor = isDarkTheme ? 'hover:bg-gray-600' : 'hover:bg-gray-100';
  const selectedBgColor = isDarkTheme ? 'bg-gray-700' : 'bg-gray-200';

  return (
    <div
      className={`spotlight-search-result-item cursor-pointer flex items-center justify-start rounded-lg p-3 
         ${hoverBgColor} ${isSelected ? selectedBgColor : ''}`}
      onClick={onClick}
      onKeyDown={onClick}
    >
      <div className='grow flex flex-col'>
        <div>
          {name} {suffix && <span className='text-xs text-gray-500'>{suffix}</span>}
        </div>
        <div className='flex flex-wrap gap-x-2 gap-y-0'>
          {tags &&
            tags.map((tag) => (
              <div className='text-xs text-gray-400' key={tag.key + tag.value}>
                <span className='font-semibold'>{tag.key}:</span> {tag.value}
              </div>
            ))}
        </div>
      </div>
      {type && <div className='text-xs text-gray-500'>{type}</div>}
    </div>
  );
};
