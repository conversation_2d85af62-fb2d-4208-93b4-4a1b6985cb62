import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

type KeyboardIconComponentProps = {
  className?: string;
};

export type KeyboardIconComponent = (props: KeyboardIconComponentProps) => React.ReactNode;

const ic = (iconName: string): KeyboardIconComponent => {
  return ({ className }: KeyboardIconComponentProps) => {
    const { isDarkTheme } = useAkuityIntelligenceContext();
    return (
      <span
        className={`ml-2 px-2 py-1 rounded-md ${isDarkTheme ? 'bg-gray-600' : 'bg-gray-300'} ${className}`}
      >
        {iconName}
      </span>
    );
  };
};

export const KeyboardIconUp = ic('↑');
export const KeyboardIconDown = ic('↓');
export const KeyboardIconEnter = ic('⏎');
export const KeyboardIconEscape = ic('Esc');
export const KeyboardIconSpotlight = ic('⌘ + P');
