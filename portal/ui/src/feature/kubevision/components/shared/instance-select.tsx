import { Flex, Select } from 'antd';
import { useMemo } from 'react';

import { useGetKubernetesEnabledClustersQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { EnabledClustersInfo } from '@ui/lib/apiclient/utils';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

type InstanceSelectProps = {
  instanceId?: string;
  hasDeprecatedApis?: boolean;
  onChange?: (value: string) => void;
  showAllOption?: boolean;
};

export const InstanceSelect = ({
  instanceId,
  hasDeprecatedApis,
  onChange,
  showAllOption
}: InstanceSelectProps) => {
  const { enabledClustersInfo } = useAkuityIntelligenceContext();
  const { data, isLoading } = useGetKubernetesEnabledClustersQuery(
    { hasDeprecatedApis },
    {
      enabled: hasDeprecatedApis,
      staleTime: 5000
    }
  );

  const options = useMemo(() => {
    const info = data ? new EnabledClustersInfo(data) : enabledClustersInfo;
    const instanceOptions = info.instances().map((instance) => ({
      label: instance.name,
      value: instance.id
    }));
    return [...(showAllOption ? [{ value: '', label: '*' }] : []), ...(instanceOptions ?? [])];
  }, [enabledClustersInfo, data]);

  return (
    <Flex className='items-center my-1 mr-4' justify='start' align='middle'>
      <strong className='mr-2'>Instance:</strong>
      <Select
        className='w-auto !rounded-md'
        popupMatchSelectWidth={false}
        placeholder='Cluster'
        loading={isLoading}
        value={instanceId || ''}
        style={{ minWidth: 180 }}
        onChange={(value) => onChange?.(value)}
        options={options}
        showSearch={true}
        autoClearSearchValue={true}
        filterOption={(input, option) => {
          if (!option) return false;
          return option.label?.toString().toLowerCase().includes(input.toLowerCase());
        }}
      />
    </Flex>
  );
};
