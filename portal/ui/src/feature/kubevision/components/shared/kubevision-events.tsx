import { Timestamp } from '@bufbuild/protobuf';
import { Table, TableColumnsType, Tag } from 'antd';

import { useGetKubernetesResourceEventsQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { KubernetesEventsData } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { momentParse } from '@ui/lib/timezone';

import { formatTime } from '../../utils';

type Props = {
  instanceId: string;
  clusterId: string;
  resourceId: string;
};

export const KubeVisionEvents = ({ instanceId, clusterId, resourceId }: Props) => {
  const { enabledClustersInfo } = useAkuityIntelligenceContext();
  const shard = enabledClustersInfo.instanceShard({ instanceId });
  const { data, isLoading } = useGetKubernetesResourceEventsQuery(shard, {
    instanceId,
    clusterId,
    resourceId
  });

  const typeTagColor = (value: string) => {
    if (value.toLowerCase() == 'normal') return 'success';
    if (value.toLowerCase() == 'warning') return 'warning';
    return 'default';
  };

  const columns: TableColumnsType<KubernetesEventsData> = [
    {
      key: 'type',
      title: 'Type',
      dataIndex: 'type',
      render: (value) => <Tag color={typeTagColor(value)}>{value}</Tag>
    },
    {
      key: 'reason',
      title: 'Reason',
      dataIndex: 'reason'
    },
    {
      key: 'message',
      title: 'Message',
      dataIndex: 'message'
    },
    {
      key: 'count',
      title: 'Count',
      dataIndex: 'count',
      render: (value: number) => {
        if (!value) return <></>;
        return <>{value}</>;
      }
    },
    {
      key: 'firstOccurred',
      title: 'First Occurred',
      dataIndex: 'firstTimestamp',
      render: (value: Timestamp, record: KubernetesEventsData) => {
        const timestamp = value ? value : record?.eventTimestamp;
        if (!timestamp) return <>N/A</>;
        return (
          <>
            <div>{formatTime(timestamp, { fromNow: true })}</div>
            <div className={'text-xs'}>{formatTime(timestamp)}</div>
          </>
        );
      }
    },
    {
      key: 'LastOccurred',
      title: 'Last Occurred',
      dataIndex: 'lastTimestamp',
      render: (value: Timestamp, record: KubernetesEventsData) => {
        const timestamp = value ? value : record?.eventTimestamp;
        if (!timestamp) return <>N/A</>;
        return (
          <>
            <div>{formatTime(timestamp, { fromNow: true })}</div>
            <div className={'text-xs'}>{formatTime(timestamp)}</div>
          </>
        );
      }
    }
  ];

  const events = (data?.events || []).sort((lhs, rhs) => {
    const lhsTimestamp = lhs.lastTimestamp
      ? momentParse.withGlobalFormat(lhs.lastTimestamp.toDate())
      : momentParse.withGlobalFormat('0001-01-01T00:00:00Z');
    const rhsTimestamp = rhs.lastTimestamp
      ? momentParse.withGlobalFormat(rhs.lastTimestamp.toDate())
      : momentParse.withGlobalFormat('0001-01-01T00:00:00Z');
    return -lhsTimestamp.diff(rhsTimestamp);
  });

  return (
    <>
      {isLoading ? (
        <div className={'w-full mt-5 flex justify-center'}>
          <Loading />
        </div>
      ) : (
        <Table
          columns={columns}
          rowKey={'uid'}
          dataSource={events}
          pagination={false}
          scroll={{ x: 'max-content' }}
        />
      )}
    </>
  );
};
