import { faMagnifyingGlass, faSpinner } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Checkbox, ConfigProvider, Flex, Input, Row, Select, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { NotificationInstance } from 'antd/lib/notification/interface';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

import {
  useGetKubernetesLogsQuery,
  useGetKubernetesResourceDetailQuery
} from '@ui/lib/apiclient/organization/kubevision-queries';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { useNotificationContext } from '@ui/lib/context/notification-context';

import { KubernetesLogType, TabType } from '../../const';
import { useUrlStates } from '../../hooks/use-url-states';

interface Props {
  instanceId: string;
  clusterId: string;
  resourceId: string;
  logType: KubernetesLogType;
  toTop: number;
}

interface KubernetesLog {
  index: number;
  podName: string;
  containerName: string;
  timestamp: string;
  content: string;
}

interface PodSelectOption {
  value: string;
  label: string;
  id: string;
}

export const copyToClipboard = async (notification: NotificationInstance, text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    notification.success({
      message: 'Logs are copied to clipboard!',
      placement: 'bottomRight'
    });
  } catch (err) {
    notification.error({
      message: 'Failed to copy',
      placement: 'bottomRight'
    });
  }
};

export const KubeVisionLogs = ({ instanceId, clusterId, resourceId, logType, toTop }: Props) => {
  const notification = useNotificationContext();
  const [searchParams] = useSearchParams();
  const { isDarkTheme } = useAkuityIntelligenceContext();

  const [urlStates, setUrlStates] = useUrlStates(
    {
      content: {
        key: 'content',
        defaultValue: ''
      },
      pod: {
        key: 'pod',
        defaultValue: ''
      },
      container: {
        key: 'container',
        defaultValue: ''
      },
      tailLines: {
        key: 'tailLines',
        defaultValue: '500'
      }
    },
    TabType.Logs
  );

  const kubernetesLogDefaultParams = {
    instanceId,
    clusterId,
    resourceId,
    follow: false,
    tailLines: BigInt(500)
  };

  const isPodLog = logType === KubernetesLogType.Pod;

  const [logParams, setLogParams] = useState(kubernetesLogDefaultParams);

  const { data: resourceDetail, isLoading: isResourceDetailLoading } =
    useGetKubernetesResourceDetailQuery(
      {
        instanceId,
        resourceId,
        clusterId
      },
      { enabled: isPodLog, staleTime: 1000 }
    );

  const { enabledClustersInfo } = useAkuityIntelligenceContext();
  const shard = enabledClustersInfo.instanceShard({ instanceId });
  const { data: logsData } = useGetKubernetesLogsQuery(shard, logParams);

  const kubernetesLogs = useMemo(() => {
    return (
      logsData?.map((log, index) => ({
        index,
        podName: log.podName,
        containerName: log.containerName,
        timestamp: log.timestamp,
        content: log.content,
        tailLines: log.tailLines
      })) || []
    );
  }, [logsData]);

  const [partialContent, setPartialContent] = useState<string>(urlStates.content);
  const [debouncedContent] = useDebounce(partialContent, 300);

  const [filteredLogs, setFilteredLogs] = useState(logsData);

  const [hasUserScrolled, setHasUserScrolled] = useState(false);

  const [scrollY, setScrollY] = useState<number>(window.innerHeight - toTop);
  const [isWrapLog, setIsWrapLog] = useState<boolean>(false);

  const tableRef: Parameters<typeof Table>[0]['ref'] = useRef(null);

  const cellComponent = ({ text, color }: { text: string | JSX.Element; color?: string }) => (
    <span className='text-xs' style={{ color: color ? color : '#0451a5' }}>
      {text}
    </span>
  );

  const getHighlightResult = ({
    text,
    highlightText,
    color
  }: {
    text: string;
    highlightText: string;
    color?: string;
  }) => {
    if (!highlightText) {
      return cellComponent({ text, color });
    }

    const parts = text.split(new RegExp(`(${highlightText})`, 'gi'));

    const highlightResult = parts.map((part, index) =>
      part.toLowerCase() === highlightText.toLowerCase() ? (
        <span key={index} className='bg-slate-500 text-white'>
          {part}
        </span>
      ) : (
        part
      )
    );

    return cellComponent({
      text: <>{highlightResult}</>,
      color
    });
  };

  const hashStringToColor = (str: string): string => {
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
      hash = hash & hash;
    }

    // Convert hash to a hex color code
    const blue = (hash & 0xff).toString(16).padStart(2, '0');
    const green = ((hash >> 8) & 0x7f).toString(16).padStart(2, '0');
    const red = ((hash >> 16) & 0x3f).toString(16).padStart(2, '0');

    // Convert hash to a brighter hex color code
    const hue = hash % 360;
    const saturation = 70 + (hash % 30);
    const lightness = 60 + (hash % 20);

    return !isDarkTheme ? `#${red}${green}${blue}` : `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  };

  const podNameOption = useMemo(() => {
    if (resourceDetail?.podInfo?.length > 0) {
      return [{ value: '', label: '*', id: '' }].concat(
        resourceDetail?.podInfo.map((info) => ({
          value: info.pod.name,
          label: info.pod.name,
          id: info.pod.id
        }))
      );
    } else {
      return [{ value: '', label: '*', id: '' }];
    }
  }, [resourceDetail?.podInfo]);

  const containerNameOption = useMemo(() => {
    if (resourceDetail?.podInfo[0]?.containers?.length > 0) {
      return [{ value: '', label: '*' }].concat(
        resourceDetail?.podInfo[0]?.containers.map((container) => ({
          value: container.name,
          label: container.name
        }))
      );
    } else {
      return [{ value: '', label: '*' }];
    }
  }, [resourceDetail?.podInfo[0]?.containers]);

  const charWidth = 7.5;

  const maxPodNameLength = useMemo(() => {
    if (resourceDetail?.podInfo.length > 0) {
      return Math.max(...resourceDetail.podInfo.map((info) => info.pod.name.length));
    }
  }, [resourceDetail?.podInfo]);

  const maxContainerNameLength = useMemo(() => {
    if (resourceDetail?.podInfo[0]?.containers.length > 0) {
      return Math.max(
        ...resourceDetail.podInfo[0].containers.map((container) => container.name.length)
      );
    }
  }, [resourceDetail?.podInfo[0]?.containers]);

  const podLogColumns = [
    {
      title: 'Pod',
      dataIndex: 'podName',
      key: 'podName',
      fixed: true,
      width: Math.max(maxPodNameLength, 'Pod'.length) * charWidth + 16,
      render: (text: string) => cellComponent({ text, color: hashStringToColor(text) })
    },
    {
      title: 'Container',
      dataIndex: 'containerName',
      key: 'containerName',
      width: Math.max(maxContainerNameLength, 'Container'.length) * charWidth + 16,
      render: (text: string) => cellComponent({ text, color: hashStringToColor(text) })
    }
  ];

  const baseColumn = [
    {
      title: 'Timestamp',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 240,
      render: (text: string) => cellComponent({ text, color: '#237893' })
    },
    {
      title: 'Content',
      dataIndex: 'content',
      key: 'content',
      render: (text: string) => getHighlightResult({ text, highlightText: partialContent })
    }
  ];

  const columns = isPodLog ? [...podLogColumns, ...baseColumn] : baseColumn;

  const defaultCheckedList = ['podName', 'containerName'];
  const [checkedList, setCheckedList] = useState(defaultCheckedList);

  const podColumnOptions = [
    { label: 'Pod', value: 'podName' },
    { label: 'Container', value: 'containerName' }
  ];

  const basicColumnOption = [{ label: 'Timestamp', value: 'timestamp' }];

  const columnOptions = isPodLog ? [...podColumnOptions, ...basicColumnOption] : basicColumnOption;

  const newColumns: ColumnsType = useMemo(
    () =>
      columns.map((item) => ({
        ...item,
        hidden: !checkedList.includes(item.key as string) && item.key !== 'content'
      })),
    [checkedList, partialContent, maxPodNameLength, maxContainerNameLength]
  );

  // filter logs when url search or debounced search changes
  useEffect(() => {
    const filteredLogs = kubernetesLogs.filter((log) => {
      const matchesContainerName = urlStates.container
        ? log.containerName === urlStates.container
        : true;
      const matchesContent = debouncedContent
        ? log.content.toLowerCase().includes(debouncedContent.toLowerCase())
        : true;

      return matchesContainerName && matchesContent;
    });

    setFilteredLogs(filteredLogs);
  }, [kubernetesLogs, urlStates.container, debouncedContent]);

  const handleFilterLogs = () => {
    const filteredLogs = kubernetesLogs.filter((log) => {
      const matchesContainerName = urlStates.container
        ? log.containerName === urlStates.container
        : true;
      const matchesContent = partialContent
        ? log.content.toLowerCase().includes(partialContent.toLowerCase())
        : true;

      return matchesContainerName && matchesContent;
    });

    setFilteredLogs(filteredLogs);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleFilterLogs();
    }
  };

  const handlePodId = (_: string, option: PodSelectOption | PodSelectOption[]) => {
    if (Array.isArray(option)) {
      const firstOption = option[0];
      setLogParams((prev) => ({ ...prev, resourceId: firstOption.id }));
    } else {
      const { id } = option;
      if (id) {
        setLogParams((prev) => ({ ...prev, resourceId: id }));
      } else {
        setLogParams((prev) => ({ ...prev, resourceId }));
      }
    }
  };

  const handleTailLines = (value: string) => {
    setLogParams((prev) => ({ ...prev, tailLines: BigInt(value) }));
    setUrlStates({
      tailLines: value
    });
  };

  const handleFollowToggle = () => {
    setHasUserScrolled(false);
    setLogParams((prev) => ({ ...prev, follow: !prev.follow }));
  };

  const handleScroll = () => {
    if (tableRef?.current) {
      const tableBody = tableRef.current.nativeElement.querySelector(
        '.ant-table-tbody-virtual-holder'
      );
      if (tableBody) {
        // Check if the user has scrolled up by comparing scrollTop with the scrollHeight minus the height of the visible part
        if (tableBody.scrollTop + tableBody.clientHeight < tableBody.scrollHeight) {
          setHasUserScrolled(true);
        } else {
          setHasUserScrolled(false);
        }
      }
    }
  };

  const handleToBottom = () => {
    if (tableRef?.current && filteredLogs?.length) {
      tableRef.current?.scrollTo({ index: filteredLogs?.length - 1 });
    }
  };

  const handleWrapLog = () => {
    setIsWrapLog((prev) => !prev);
  };

  const handleCopyAll = () => {
    const displayedData = filteredLogs
      .map((log: KubernetesLog) =>
        newColumns
          .filter((col) => !col.hidden)
          .map((col) => log[col.key as keyof KubernetesLog])
          .join(',')
      )
      .join('\n');
    copyToClipboard(notification, displayedData);
  };

  // scroll to the last row after each 1st load
  useEffect(() => {
    if (!hasUserScrolled) {
      setTimeout(() => handleToBottom(), 0);
    }
  }, [filteredLogs, hasUserScrolled]);

  useEffect(() => {
    const updateScrollY = () => {
      setScrollY(window.innerHeight - toTop);
    };
    window.addEventListener('resize', updateScrollY);
    updateScrollY();

    return () => {
      window.removeEventListener('resize', updateScrollY);
    };
  }, []);

  const scrollX: number = useMemo(() => {
    const panelWidth = window.innerWidth * 0.85 - 100;
    return isWrapLog ? panelWidth : 25000;
  }, [window.innerWidth, isWrapLog]);

  // reset logs when switch to other tab
  useEffect(() => {
    if (searchParams.get('detailsTab') !== TabType.Logs) {
      setUrlStates({
        content: '',
        pod: '',
        container: '',
        tailLines: '500'
      });
      setPartialContent('');
      setFilteredLogs(logsData);
    }
  }, [searchParams]);

  // Update URL state when debounced content changes
  useEffect(() => {
    setUrlStates({
      content: debouncedContent
    });
  }, [debouncedContent]);

  return (
    <>
      {!isResourceDetailLoading && (
        <div>
          <Flex
            className={`mb-2 px-4 py-2 ${isDarkTheme ? 'bg-[#141414]' : 'bg-[#fafafa]'} rounded-lg`}
          >
            <Flex vertical={true} className='max-w-fit'>
              <Row className='mb-4' align='middle'>
                <Input
                  className='mt-1 mr-6 w-[200px]'
                  addonBefore={<FontAwesomeIcon icon={faMagnifyingGlass} />}
                  placeholder='Search Content'
                  value={partialContent}
                  onChange={(e) => {
                    setPartialContent(e.target.value);
                  }}
                  onKeyDown={handleKeyDown}
                  allowClear={true}
                />

                {isPodLog && (
                  <Row className='mt-1 mr-6' align='middle'>
                    <strong className='mr-2'>Pod:</strong>
                    <Select
                      placeholder='Pod'
                      value={urlStates.pod}
                      style={{ width: 180 }}
                      popupMatchSelectWidth={false}
                      onChange={(value, option) => {
                        handlePodId(value, option);
                        setUrlStates({
                          pod: value
                        });
                      }}
                      options={podNameOption}
                      showSearch
                    />
                  </Row>
                )}

                {isPodLog && (
                  <Row className='mt-1 mr-6' align='middle'>
                    <strong className='mr-2'>Container:</strong>
                    <Select
                      placeholder='Container'
                      value={urlStates.container}
                      style={{ width: 180 }}
                      popupMatchSelectWidth={false}
                      onChange={(value) => {
                        setUrlStates({
                          container: value
                        });
                      }}
                      options={containerNameOption}
                      showSearch
                    />
                  </Row>
                )}

                <Row className='mt-1' align='middle'>
                  <strong className='mr-2'>Tail:</strong>
                  <Select
                    defaultValue='500'
                    value={urlStates.tailLines}
                    style={{ width: 100 }}
                    onChange={handleTailLines}
                    options={[
                      { value: '500', label: '500' },
                      { value: '1000', label: '1000' },
                      { value: '1500', label: '1500' },
                      { value: '2000', label: '2000' }
                    ]}
                  />
                </Row>
              </Row>

              <Row align='middle' justify='space-between' className='pt-3 border-t '>
                <Row align='middle' className='mr-16'>
                  <strong className='mr-2'>Show: </strong>
                  <Checkbox.Group
                    value={checkedList}
                    options={columnOptions}
                    onChange={(value) => {
                      setCheckedList(value as string[]);
                    }}
                  />
                </Row>

                <Row align='middle'>
                  <Button
                    type='primary'
                    className='min-w-[70px] border'
                    onClick={handleFollowToggle}
                  >
                    {logParams.follow ? (
                      <span>
                        Following <FontAwesomeIcon icon={faSpinner} spin />
                      </span>
                    ) : (
                      'Follow'
                    )}
                  </Button>
                  <Button type='primary' className='ml-4 w-[70px]' onClick={handleToBottom}>
                    Bottom
                  </Button>
                  <Button
                    type={isWrapLog ? 'default' : 'primary'}
                    className='ml-4 w-[70px]'
                    onClick={handleWrapLog}
                  >
                    Wrap
                  </Button>
                  <Button type='primary' className='ml-4 w-[70px]' onClick={handleCopyAll}>
                    Copy
                  </Button>
                </Row>
              </Row>
            </Flex>
          </Flex>

          <ConfigProvider
            theme={{
              components: {
                Table: {
                  borderColor: 'transparent',
                  cellPaddingBlockSM: 0,
                  cellFontSize: 10
                }
              }
            }}
          >
            <Table
              rowClassName='font-mono text-xs'
              size='small'
              virtual
              rowKey={(record) => record.index}
              columns={newColumns}
              dataSource={filteredLogs}
              pagination={false}
              scroll={{ x: scrollX, y: scrollY, scrollToFirstRowOnChange: false }}
              onScroll={handleScroll}
              ref={tableRef}
            />
          </ConfigProvider>
        </div>
      )}
    </>
  );
};
