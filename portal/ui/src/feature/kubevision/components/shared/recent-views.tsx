import { faClockRotateLeft, faLink } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Dropdown, MenuProps, Row, Tag, Tooltip } from 'antd';
import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { HistoryItem, useHistoryStack } from '@ui/feature/kubevision/hooks/use-history-stack';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { dashboardTitle, DashboardType, heatmapView } from '../../const';
import {
  getNamespaceGroupByName,
  getNodeGroupByName,
  getPodGroupByName
} from '../kubevision-infrastructure-dashboard/group-by-select';
import {
  getNamespaceFillerName,
  getNodeFillerName,
  getPodFillerName
} from '../kubevision-infrastructure-dashboard/kubevision-infrastructure-heatmap';

type TagsProps = {
  item: HistoryItem;
  instanceId: string;
};

type Props = {
  kind?: string;
  resourceName?: string;
  resourceNamespace?: string;
  containerName?: string;
  nodeName?: string;
  podName?: string;
  namespaceName?: string;
  clusterName?: string;
};

export const RecentViews = ({
  kind,
  resourceName,
  resourceNamespace,
  containerName,
  nodeName,
  podName,
  namespaceName,
  clusterName
}: Props) => {
  const { organizationId, instanceId } = useAkuityIntelligenceContext();

  const { pathname, search } = useLocation();

  // explorer and container dashboard sub-tabs params
  const ignoreLogsParams = [
    'detailsTab_logs_content',
    'detailsTab_logs_pod',
    'detailsTab_logs_container'
  ];

  const ignoredEventTimelineParams = [
    'detailsTab_eventTimeline_startTime',
    'detailsTab_eventTimeline_endTime',
    'detailsTab_eventTimeline_page',
    'detailsTab_eventTimeline_eventType',
    'detailsTab_eventTimeline_severity',
    'detailsTab_eventTimeline_orderBy',
    'detailsTab_eventTimeline_pageSize',
    'detailsTab_eventTimeline_page'
  ];

  // image dashboard sub-tabs params
  const ignoredContainerParams = [
    'detailsTab_containers_status',
    'detailsTab_containers_type',
    'detailsTab_containers_nameContains',
    'detailsTab_containers_orderBy',
    'detailsTab_containers_limit',
    'detailsTab_containers_offset'
  ];

  const ignoredVulnerabilitiesParams = [
    'detailsTab_vulnerabilities_severity',
    'detailsTab_vulnerabilities_pageSize',
    'detailsTab_vulnerabilities_page'
  ];

  const { historyStack, clearHistory, updateHistoryItemNames } = useHistoryStack({
    storageName: `kubeVisionNavHistoryStack/${organizationId}/${instanceId}`,
    maxEntries: 13,
    storeInLocalStorage: true,
    replace: true,
    ignoredParams: [
      'orderBy',
      'offset',
      'limit',
      'ownerId',
      'podId',
      ...ignoreLogsParams,
      ...ignoredEventTimelineParams,
      ...ignoredContainerParams,
      ...ignoredVulnerabilitiesParams
    ]
  });

  const navigate = useNavigate();

  // for saving Resource name, Container name, Node name, etc
  useEffect(() => {
    const currentPath = pathname + search;
    const namesToUpdate = {
      ...(kind && { kind }),
      ...(resourceName && { resourceName }),
      ...(resourceNamespace && { resourceNamespace }),
      ...(containerName && { containerName }),
      ...(nodeName && { nodeName }),
      ...(podName && { podName }),
      ...(namespaceName && { namespaceName }),
      ...(clusterName && { clusterName })
    };

    if (Object.keys(namesToUpdate).length > 0) {
      updateHistoryItemNames(currentPath, namesToUpdate);
    }
  }, [
    kind,
    resourceName,
    resourceNamespace,
    containerName,
    nodeName,
    podName,
    namespaceName,
    clusterName,
    pathname,
    search
  ]);

  const HistoryItemTags: React.FC<TagsProps> = ({ item }) => {
    const urlParams = new URLSearchParams(item.path.split('?')[1]);

    const renderTag = (key: string, color: string, label: string, value: string | null) => {
      if (!value) return null;
      return (
        <Tag className='m-0' color={color} key={key}>
          <strong>{label}: </strong>
          {value}
        </Tag>
      );
    };

    const isDetailView =
      urlParams.get('resourceInstance') ||
      urlParams.get('resourceCluster') ||
      item.resourceNamespace ||
      item.resourceName ||
      urlParams.get('containerCluster') ||
      item.containerName ||
      urlParams.get('image') ||
      item.nodeName ||
      item.namespaceName ||
      item.podName ||
      item.clusterName ||
      urlParams.get('dpApiVersion');

    const isFilterBy =
      urlParams.get('kind') ||
      item.kind ||
      urlParams.get('instance') ||
      urlParams.get('cluster') ||
      urlParams.get('namespace') ||
      urlParams.get('nameContains') ||
      item.ownerTagKey ||
      item.ownerTagValue ||
      urlParams.get('view') ||
      urlParams.get('groupBy') ||
      urlParams.get('nodeFiller') ||
      urlParams.get('namespaceFiller') ||
      urlParams.get('podFiller');

    const filterColor = 'geekblue';
    const detailViewColor = 'lime';

    const getGroupByName = (view: string | null, groupBy: number): string => {
      if (!view) return '';

      switch (view) {
        case heatmapView.Nodes:
          return getNodeGroupByName(groupBy);
        case heatmapView.Namespaces:
          return getNamespaceGroupByName(groupBy);
        case heatmapView.Pods:
        default:
          return getPodGroupByName(groupBy);
      }
    };

    return (
      <Row className='gap-x-1 gap-y-2 flex-wrap flex-1 items-center'>
        {urlParams.get('dashboard') && (
          <Tag className='m-0' color='blue' key='dashboard'>
            <FontAwesomeIcon icon={faLink} className='mr-1' />
            <strong>{dashboardTitle[urlParams.get('dashboard') as DashboardType]}</strong>
          </Tag>
        )}

        {!isDetailView && (
          <>
            {isFilterBy && (
              <Tag
                className='m-0 bg-transparent'
                color={filterColor}
                bordered={false}
                key='filter-by'
              >
                <strong>Filter By</strong>
              </Tag>
            )}
            {renderTag('kind', filterColor, 'Kind', urlParams.get('kind'))}
            {renderTag('instance', filterColor, 'Instance', urlParams.get('instance'))}
            {renderTag('cluster', filterColor, 'Cluster', urlParams.get('cluster'))}
            {renderTag('namespace', filterColor, 'Namespace', urlParams.get('namespace'))}
            {renderTag('nameContains', filterColor, 'Name Contains', urlParams.get('nameContains'))}

            {renderTag('view', filterColor, 'View', urlParams.get('view'))}

            {urlParams.get('groupBy') && (
              <Tag className='m-0' color={filterColor} key='groupBy'>
                <strong className='text-nowrap'>Group By: </strong>
                {getGroupByName(urlParams.get('view'), Number(urlParams.get('groupBy')))}
              </Tag>
            )}

            {urlParams.get('nodeFiller') && (
              <Tag className='m-0' color={filterColor} key='nodeFiller'>
                <strong>Filler: </strong>
                {getNodeFillerName(Number(urlParams.get('nodeFiller')))}
              </Tag>
            )}

            {urlParams.get('namespaceFiller') && (
              <Tag className='m-0' color={filterColor} key='namespaceFiller'>
                <strong>Filler: </strong>
                {getNamespaceFillerName(Number(urlParams.get('namespaceFiller')))}
              </Tag>
            )}

            {urlParams.get('podFiller') && (
              <Tag className='m-0' color={filterColor} key='podFiller'>
                <strong>Filler: </strong>
                {getPodFillerName(Number(urlParams.get('podFiller')))}
              </Tag>
            )}
          </>
        )}

        {isDetailView && (
          <>
            {renderTag(
              'resourceInstance',
              detailViewColor,
              'Instance',
              urlParams.get('resourceInstance')
            )}
            {renderTag(
              'resourceCluster',
              detailViewColor,
              'Cluster',
              urlParams.get('resourceCluster')
            )}
            {renderTag('namespace', detailViewColor, 'Namespace', item.resourceNamespace)}
            {renderTag(
              'resource',
              detailViewColor,
              urlParams.get('kind') || item.kind,
              item.resourceName
            )}
            {renderTag(
              'containerCluster',
              detailViewColor,
              'Cluster',
              urlParams.get('containerCluster')
            )}
            {renderTag('containerName', detailViewColor, 'Container', item.containerName)}
            {renderTag('imageName', detailViewColor, 'Image', urlParams.get('image'))}
            {renderTag('nodeName', detailViewColor, 'Node', item.nodeName)}
            {renderTag('namespaceName', detailViewColor, 'Namespace', item.namespaceName)}
            {renderTag('podName', detailViewColor, 'Pod', item.podName)}
            {renderTag('clusterName', detailViewColor, 'Cluster', item.clusterName)}
            {renderTag('apiVersion', detailViewColor, 'API Version', urlParams.get('dpApiVersion'))}

            {renderTag(
              'detailsTab',
              detailViewColor,
              'Tab',
              urlParams.get('detailsTab')?.replace('detailsTab_', '')
            )}
          </>
        )}
      </Row>
    );
  };

  const items: MenuProps['items'] =
    historyStack.length === 0
      ? [
          {
            key: 'empty-history',
            label: (
              <Row justify='center' align='middle' className='min-w-[550px] h-40'>
                <span className='text-[#626f7e]'>No history yet</span>
              </Row>
            )
          }
        ]
      : [
          ...historyStack.map((item: HistoryItem, index: number) => {
            return {
              key: index,
              label: (
                <Tooltip
                  styles={{ body: { width: 'max-content', maxWidth: '600px' } }}
                  title={
                    <>
                      <strong className='whitespace-nowrap'>Jump To:</strong>
                      <span className='ml-2 break-all tracking-wider'>{item.path}</span>
                    </>
                  }
                  placement='topLeft'
                >
                  <Row
                    className='py-1 w-full flex-wrap cursor-pointer'
                    justify='space-between'
                    onClick={() => navigate(item.path)}
                  >
                    <HistoryItemTags item={item} instanceId={instanceId} />
                    {index === 0 && historyStack?.length > 1 && (
                      <div>
                        <Tag className='ml-4 px-2 rounded-xl' color='#2db7f5'>
                          Latest
                        </Tag>
                      </div>
                    )}
                  </Row>
                </Tooltip>
              )
            };
          }),
          {
            key: 'clear-history',
            label: (
              <Row justify='center' className='pt-1 w-full border-t border-[#e5e7eb]'>
                <Button
                  className='w-full'
                  type='link'
                  onClick={(e) => {
                    e.stopPropagation();
                    clearHistory();
                  }}
                >
                  <span className='min-w-[550px] text-[#626f7e]'>Clear History</span>
                </Button>
              </Row>
            )
          }
        ];

  return (
    <Dropdown menu={{ items }} placement='bottomRight'>
      <Button>
        <FontAwesomeIcon icon={faClockRotateLeft} />
        <span>Navigation History</span>
      </Button>
    </Dropdown>
  );
};
