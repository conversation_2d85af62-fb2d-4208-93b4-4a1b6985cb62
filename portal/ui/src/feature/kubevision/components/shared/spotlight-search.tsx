import { faSearch } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Modal, Input, InputRef, Button } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import {
  KeyboardIconDown,
  KeyboardIconEnter,
  KeyboardIconEscape,
  KeyboardIconSpotlight,
  KeyboardIconUp
} from './spotlight-search/keyboard-icons';
import { SearchResources } from './spotlight-search/search-resources';

type SpotlightSearchProps = {
  instanceId: string;
  className?: string;
};

export const SpotlightSearch = forwardRef(
  ({ instanceId, className }: SpotlightSearchProps, ref) => {
    const { isDarkTheme } = useAkuityIntelligenceContext();
    const [isOpen, setIsOpen] = useState(false);
    const [searchValue, setSearchValue] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    useImperativeHandle(ref, () => ({
      open: () => setIsOpen(true),
      close: () => setIsOpen(false),
      toggle: () => setIsOpen((prev) => !prev),
      setSearchValue: (value: string) => setSearchValue(value)
    }));

    const searchInputRef = useRef<InputRef>(null);

    useEffect(() => {
      if (isOpen) {
        setSearchValue('');
        setTimeout(() => {
          searchInputRef.current?.focus();
        }, 500);
      }
    }, [isOpen]);

    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if ((event.metaKey || event.ctrlKey) && event.key === 'p') {
          event.preventDefault();
          event.stopPropagation();
          setIsOpen(true);
        }
        if (event.key === 'Escape') {
          event.preventDefault();
          event.stopPropagation();
          setIsOpen(false);
        }
      };

      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }, []);

    return (
      <>
        <Button className={className} onClick={() => setIsOpen(true)}>
          <FontAwesomeIcon icon={faSearch} />
          <span>
            <span>Spotlight Search</span>
            <KeyboardIconSpotlight
              className={`ml-1 ${isDarkTheme ? '!bg-gray-900' : '!bg-gray-100'}`}
            />
          </span>
        </Button>
        <Modal
          open={isOpen}
          onCancel={() => setIsOpen(false)}
          title={false}
          footer={
            <div
              className={`flex gap-4 items-center justify-end p-4 mt-0 text-xs ${isDarkTheme ? 'bg-gray-800' : 'bg-gray-200'}`}
              style={{ borderBottomLeftRadius: '8px', borderBottomRightRadius: '8px' }}
            >
              <div className='flex items-center'>
                <span>Close</span>
                <KeyboardIconEscape className='ml-1' />
              </div>
              <div className='flex items-center'>
                <span>Up</span>
                <KeyboardIconUp className='ml-1' />
              </div>
              <div className='flex items-center'>
                <span>Down</span>
                <KeyboardIconDown className='ml-1' />
              </div>
              <div className='flex items-center'>
                <span>Open Details</span>
                <KeyboardIconEnter className='ml-1' />
              </div>
            </div>
          }
          closeIcon={null}
          width={600}
          classNames={{ content: '!p-0 !m-0', footer: '!p-0 !m-0' }}
          destroyOnHidden
        >
          <div className='p-4'>
            <Input
              ref={searchInputRef}
              className='mb-2'
              placeholder='Search all resources...'
              autoFocus
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              suffix={
                <div className='flex justify-start items-center align-center'>
                  {isLoading && (
                    <Loading text=' ' className='text-gray-500 text-xs !m-0 !mr-[-.5rem]' />
                  )}
                </div>
              }
            />
            <SearchResources
              instanceId={instanceId}
              query={searchValue}
              closeModal={() => setIsOpen(false)}
              onLoadingChanged={setIsLoading}
            />
          </div>
        </Modal>
      </>
    );
  }
);
