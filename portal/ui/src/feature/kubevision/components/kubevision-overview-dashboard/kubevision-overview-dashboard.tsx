import {
  faServer,
  faNetworkWired,
  faDatabase,
  faCubes,
  faExclamationTriangle,
  faShieldVirus,
  faTrashAlt,
  faList,
  faShareNodes,
  faCompactDisc,
  faHardDrive,
  faFire
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Row, Col, Card, Typography, Flex } from 'antd';

import { paths } from '@ui/config/paths';
import { usePlatformContext } from '@ui/feature/shared/context/platform-context';
import { useGetKubernetesSummaryQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { Loading } from '@ui/lib/components/loading';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { DashboardType } from '../../const';
import useCustomSearchParams from '../../hooks/use-custom-search-params';
import { TruncateText } from '../shared/truncate-text';

type Props = {
  instanceId: string;
  isAISREFeatureOn: boolean;
};

interface StatCardProps {
  title: string | React.ReactNode;
  value: string | number | React.ReactNode | undefined;
  prefix: React.ReactNode;
  isDanger?: boolean;
  onCardClick?: () => void;
}

const StatCard = ({ title, value, prefix, isDanger, onCardClick }: StatCardProps) => {
  return (
    <Card
      hoverable
      type='inner'
      title={<span className='text-lg'>{title}</span>}
      onClick={(e) => {
        e.stopPropagation();
        onCardClick();
      }}
    >
      <Typography.Title level={2} type={isDanger ? 'danger' : 'secondary'}>
        <Flex align='center' gap={8}>
          {prefix}
          {value}
        </Flex>
      </Typography.Title>
    </Card>
  );
};

const TitleWithHealthyBadge = ({ title }: { title: string }) => {
  return (
    <Flex align='center' gap={8}>
      <span className='text-lg'>{title}</span>
    </Flex>
  );
};

export const KubevisionOverviewDashboard = ({ instanceId, isAISREFeatureOn }: Props) => {
  const { platform } = usePlatformContext();
  const isInAKP = platform === 'akuity-platform';
  const isInKargo = platform === 'kargo';
  const { isDarkTheme, enabledClustersInfo } = useAkuityIntelligenceContext();

  const { data: overview, isLoading } = useGetKubernetesSummaryQuery(instanceId);

  const { setSearchParams } = useCustomSearchParams();

  const themeClass = isDarkTheme ? 'dashboard-dark' : 'dashboard-light';

  return (
    <div className='max-w-screen-xl'>
      {isLoading ? (
        <Loading />
      ) : (
        <div className={`kubevision-dashboard ${themeClass}`}>
          {/* Top Level Stats */}
          <Row gutter={[24, 24]}>
            {/* Instances Count */}
            {isInAKP && (
              <>
                {!instanceId ? (
                  <Col xs={24} sm={12} md={isInAKP ? 6 : 8}>
                    <StatCard
                      title='Instances'
                      value={overview?.instanceCount}
                      prefix={<FontAwesomeIcon icon={faNetworkWired} />}
                      onCardClick={() => {
                        if (!instanceId) {
                          window.location.href = paths.instances;
                        } else {
                          setSearchParams({ tab: 'summary' }, { overwrite: true });
                        }
                      }}
                    />
                  </Col>
                ) : (
                  <Col xs={24} sm={12} md={isInAKP ? 6 : 8}>
                    <StatCard
                      title='Instance Name'
                      value={
                        <TruncateText maxWidth={250}>
                          {enabledClustersInfo.instanceName(instanceId)}
                        </TruncateText>
                      }
                      prefix={<FontAwesomeIcon icon={faNetworkWired} />}
                      onCardClick={() => {
                        setSearchParams({ tab: 'summary' }, { overwrite: true });
                      }}
                    />
                  </Col>
                )}
              </>
            )}

            {/* Clusters Count */}
            <Col xs={24} sm={12} md={isInAKP ? 6 : 8}>
              <StatCard
                title='Clusters'
                value={overview?.clusterCount}
                prefix={<FontAwesomeIcon icon={faServer} />}
                onCardClick={() => {
                  if (isInAKP || isInKargo) {
                    if (!instanceId) {
                      setSearchParams(
                        { tab: 'kubevision', dashboard: DashboardType.Infrastructure },
                        { overwrite: true }
                      );
                    } else {
                      setSearchParams({ tab: 'clusters' }, { overwrite: true });
                    }
                  } else {
                    window.location.href = '/settings/clusters';
                  }
                }}
              />
            </Col>
            {/* API Resources Count */}
            <Col xs={24} sm={12} md={isInAKP ? 6 : 8}>
              <StatCard
                title='API Resources'
                value={overview?.apiResourceCount}
                prefix={<FontAwesomeIcon icon={faList} />}
                onCardClick={() => {
                  setSearchParams(
                    { tab: 'kubevision', dashboard: DashboardType.Explorer },
                    { overwrite: true }
                  );
                }}
              />
            </Col>
            {/* Objects Count */}
            <Col xs={24} sm={12} md={isInAKP ? 6 : 8}>
              <StatCard
                title='Objects'
                value={overview?.objectCount}
                prefix={<FontAwesomeIcon icon={faDatabase} />}
                onCardClick={() => {
                  setSearchParams(
                    { tab: 'kubevision', dashboard: DashboardType.Explorer },
                    { overwrite: true }
                  );
                }}
              />
            </Col>
          </Row>

          {/* Infrastructure Section */}
          <div className='section-container mt-8'>
            <Row gutter={[24, 24]}>
              {/* Nodes Count */}
              <Col xs={24} sm={12} md={6}>
                <StatCard
                  title='Nodes'
                  value={overview?.nodeCount}
                  prefix={<FontAwesomeIcon icon={faHardDrive} />}
                  onCardClick={() => {
                    setSearchParams(
                      { tab: 'kubevision', dashboard: DashboardType.Infrastructure },
                      { overwrite: true }
                    );
                  }}
                />
              </Col>

              {/* Pods Count */}
              <Col xs={24} sm={12} md={6}>
                <StatCard
                  title='Pods'
                  value={overview?.podCount}
                  prefix={<FontAwesomeIcon icon={faShareNodes} />}
                  onCardClick={() => {
                    setSearchParams(
                      {
                        tab: 'kubevision',
                        dashboard: DashboardType.Infrastructure,
                        view: 'Pods'
                      },
                      { overwrite: true }
                    );
                  }}
                />
              </Col>

              {/* Containers Count */}
              <Col xs={24} sm={12} md={6}>
                <StatCard
                  title='Containers'
                  value={overview?.containerCount}
                  prefix={<FontAwesomeIcon icon={faCubes} />}
                  onCardClick={() => {
                    setSearchParams(
                      { tab: 'kubevision', dashboard: DashboardType.Containers },
                      { overwrite: true }
                    );
                  }}
                />
              </Col>

              {/* Images Count */}
              <Col xs={24} sm={12} md={6}>
                <StatCard
                  title='Images'
                  value={overview?.imageCount}
                  prefix={<FontAwesomeIcon icon={faCompactDisc} />}
                  onCardClick={() => {
                    setSearchParams(
                      { tab: 'kubevision', dashboard: DashboardType.Images },
                      { overwrite: true }
                    );
                  }}
                />
              </Col>
            </Row>
          </div>

          {/* Security Section */}
          <div className='section-container mt-8'>
            <Row gutter={[24, 24]}>
              {/* Incidents Count */}
              {isAISREFeatureOn && (
                <Col xs={24} sm={12} md={6}>
                  <StatCard
                    title={
                      overview?.incidentCount === 0 ? (
                        <TitleWithHealthyBadge title='Incidents' />
                      ) : (
                        'Incidents'
                      )
                    }
                    value={overview?.incidentCount}
                    prefix={<FontAwesomeIcon icon={faFire} />}
                    isDanger={overview?.incidentCount > 0}
                    onCardClick={() => {
                      setSearchParams(
                        {
                          tab: 'kubevision',
                          dashboard: DashboardType.Incidents
                        },
                        { overwrite: true }
                      );
                    }}
                  />
                </Col>
              )}

              {/* CVE Count */}
              <Col xs={24} sm={12} md={6}>
                <StatCard
                  title={
                    overview?.cveCount === 0 ? (
                      <TitleWithHealthyBadge title='CVE Images' />
                    ) : (
                      'CVE Images'
                    )
                  }
                  value={overview?.cveCount}
                  prefix={<FontAwesomeIcon icon={faShieldVirus} />}
                  isDanger={overview?.cveCount > 0}
                  onCardClick={() => {
                    setSearchParams(
                      {
                        tab: 'kubevision',
                        dashboard: DashboardType.Images,
                        orderBy: 'cveCount desc'
                      },
                      { overwrite: true }
                    );
                  }}
                />
              </Col>

              {/* Resources Stuck in Deletion Count */}
              <Col xs={24} sm={12} md={6}>
                <StatCard
                  title={
                    overview?.stuckInDeletionCount === 0 ? (
                      <TitleWithHealthyBadge title='Stuck-in-Deletion Resources' />
                    ) : (
                      'Stuck-in-Deletion Resources'
                    )
                  }
                  value={overview?.stuckInDeletionCount}
                  prefix={<FontAwesomeIcon icon={faTrashAlt} />}
                  isDanger={overview?.stuckInDeletionCount > 0}
                  onCardClick={() => {
                    setSearchParams(
                      { tab: 'kubevision', dashboard: DashboardType.Deletion },
                      { overwrite: true }
                    );
                  }}
                />
              </Col>

              {/* Deprecated APIs Count */}
              <Col xs={24} sm={12} md={6}>
                <StatCard
                  title={
                    overview?.deprecatedApiCount === 0 ? (
                      <TitleWithHealthyBadge title='Deprecated APIs' />
                    ) : (
                      'Deprecated APIs'
                    )
                  }
                  value={overview?.deprecatedApiCount}
                  prefix={<FontAwesomeIcon icon={faExclamationTriangle} />}
                  isDanger={overview?.deprecatedApiCount > 0}
                  onCardClick={() => {
                    setSearchParams(
                      { tab: 'kubevision', dashboard: DashboardType.Deprecated },
                      { overwrite: true }
                    );
                  }}
                />
              </Col>
            </Row>
          </div>
        </div>
      )}
    </div>
  );
};
