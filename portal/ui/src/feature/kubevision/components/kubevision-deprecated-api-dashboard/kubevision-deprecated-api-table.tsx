import { ConfigProvider, Flex, Table, TableColumnsType, Tag, Tooltip } from 'antd';
import semver from 'semver';

import { KubernetesTableStringFilter } from '@ui/feature/kubevision/components/shared/kubevision-table-string-filter';
import { handleTableChange, parseSortOrder } from '@ui/feature/kubevision/filter';
import { getAPIVersion, getSeverityColor } from '@ui/feature/kubevision/utils';
import { useListKubernetesDeprecatedAPIsQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import {
  DeprecatedInfo,
  DeprecatedInfoSeverity,
  GroupVersionKind
} from '@ui/lib/apiclient/types/k8s/v1/k8s_pb';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { akpTextDarkThemeColor, akpTextPrimaryColor } from '../../const';
import { KubernetesButton } from '../shared/kubevision-button';
import { createDetailsColumn } from '../shared/kubevision-table-details-column';
import KubernetesTableRadioFilter from '../shared/kubevision-table-radio-filter';

import {
  DeprecatedAPIFilter,
  filterToListKubernetesDeprecatedAPIsRequest
} from './kubevison-deprecated-api-filter';

type Props = {
  filter: DeprecatedAPIFilter;
  onFilterChange: (filter: Partial<DeprecatedAPIFilter>, overwrite?: boolean) => void;
};

export const KubevisionDeprecatedAPITable = ({ filter, onFilterChange }: Props) => {
  const { isDarkTheme, organizationMode, instanceId, enabledClustersInfo } =
    useAkuityIntelligenceContext();
  const { data: deprecatedAPIs, isLoading: isLoadingDeprecatedAPIs } =
    useListKubernetesDeprecatedAPIsQuery(
      filterToListKubernetesDeprecatedAPIsRequest(enabledClustersInfo, filter, instanceId)
    );

  const normalizeK8sVersion = (version: string): string => {
    const normalizedVersion = semver.valid(semver.coerce(version));
    if (!normalizedVersion) {
      throw new Error(`Invalid version format: ${version}`);
    }
    return normalizedVersion;
  };

  const severityTagColor = (record: DeprecatedInfo) => {
    return getSeverityColor(severityTagText(record));
  };

  const severityTagText = (record: DeprecatedInfo) => {
    if (record?.severity === DeprecatedInfoSeverity.CRITICAL) return 'CRITICAL';
    if (record?.severity === DeprecatedInfoSeverity.HIGH) return 'HIGH';
    if (record?.severity === DeprecatedInfoSeverity.MEDIUM) return 'MEDIUM';
    return 'LOW';
  };

  const severityTagTooltip = (record: DeprecatedInfo) => {
    if (record?.severity === DeprecatedInfoSeverity.CRITICAL)
      return 'This API is deprecated, please migrate the existing resources to the new API.';
    if (record?.severity === DeprecatedInfoSeverity.HIGH) return 'This API is deprecated.';
    if (record?.severity === DeprecatedInfoSeverity.MEDIUM)
      return 'This API will be deprecated in the next Kubernetes minor version, please plan to migrate the existing resources to the new API.';
    return 'This API will be deprecated in the next Kubernetes minor version.';
  };

  const onRowClick = (record: DeprecatedInfo) => {
    onFilterChange({
      deprecatedApiVersion: getAPIVersion({
        group: record.groupVersionKind.group,
        version: record.groupVersionKind.version
      }),
      deprecatedKind: record.groupVersionKind.kind,
      deprecatedInstance: enabledClustersInfo.instanceName(record.instanceId),
      deprecatedCluster: enabledClustersInfo.clusterName(record.clusterId),
      deprecatedKubernetesVersion: record.kubernetesVersion,
      deprecatedDeprecatedIn: record.deprecatedIn,
      deprecatedUnavailableIn: record.unavailableIn,
      deprecatedMigrateTo: getAPIVersion({
        group: record.migrateTo.group,
        version: record.migrateTo.version
      })
    });
  };

  const columns: TableColumnsType<DeprecatedInfo> = [
    createDetailsColumn({ onRowClick: onRowClick }),
    {
      key: 'instance',
      title: 'Instance',
      width: 150,
      dataIndex: 'instanceId',
      hidden: !organizationMode || !!filter.instance,
      render: (instanceId: string) => {
        const instanceName = enabledClustersInfo.instanceName(instanceId);
        return (
          <KubernetesButton
            tooltip={
              <>
                <b className={'mr-1'}>Filter by instance:</b>
                {instanceName}
              </>
            }
            text={instanceName}
            maxWidth={200}
            onClick={(e) => {
              e.stopPropagation();
              onFilterChange({
                instance: instanceName,
                cluster: undefined,
                offset: 0
              });
            }}
          />
        );
      }
    },
    {
      key: 'cluster',
      title: 'Cluster',
      dataIndex: 'clusterId',
      width: 150,
      hidden: !!filter.cluster,
      render: (clusterId: string, record: DeprecatedInfo) => {
        const clusterName = enabledClustersInfo.clusterName(clusterId);
        return (
          <KubernetesButton
            tooltip={
              <>
                <b className={'mr-1'}>Filter by cluster:</b>
                {clusterName}
              </>
            }
            text={clusterName}
            maxWidth={200}
            onClick={(e) => {
              e.stopPropagation();
              onFilterChange({
                instance: enabledClustersInfo.instanceName(record.instanceId),
                cluster: clusterName,
                offset: 0
              });
            }}
          />
        );
      }
    },
    {
      key: 'apiVersion',
      title: 'API Version',
      dataIndex: 'groupVersionKind',
      filtered: !!filter.apiVersionContains,
      filterDropdown: ({ close }: { close: () => void }) => (
        <KubernetesTableStringFilter
          label='API Version'
          defaultValue={filter.apiVersionContains ?? ''}
          onClearFilter={() => {
            onFilterChange({ apiVersionContains: undefined, offset: 0 });
            close();
          }}
          onApplyFilter={(apiVersionContains: string) => {
            onFilterChange({ apiVersionContains, offset: 0 });
            close();
          }}
        />
      ),
      render: (groupVersionKind: GroupVersionKind) =>
        getAPIVersion({ group: groupVersionKind.group, version: groupVersionKind.version })
    },
    {
      key: 'kind',
      title: 'Kind',
      dataIndex: 'groupVersionKind',
      filtered: !!filter.kind,
      filterDropdown: ({ close }: { close: () => void }) => (
        <KubernetesTableStringFilter
          label='Kind'
          defaultValue={filter.kind ?? ''}
          onClearFilter={() => {
            onFilterChange({ kind: undefined, offset: 0 });
            close();
          }}
          onApplyFilter={(kind: string) => {
            onFilterChange({ kind, offset: 0 });
            close();
          }}
        />
      ),
      render: (groupVersionKind: GroupVersionKind) => groupVersionKind.kind
    },
    {
      key: 'severity',
      title: 'Severity',
      filtered: !!filter.severity,
      filterDropdown: ({ close }: { close: () => void }) => (
        <KubernetesTableRadioFilter
          label={'Severity'}
          defaultValue={filter.severity?.toString() ?? ''}
          options={[
            { label: 'Critical', value: DeprecatedInfoSeverity.CRITICAL.toString() },
            { label: 'High', value: DeprecatedInfoSeverity.HIGH.toString() },
            { label: 'Medium', value: DeprecatedInfoSeverity.MEDIUM.toString() },
            { label: 'Low', value: DeprecatedInfoSeverity.LOW.toString() }
          ]}
          onClearFilter={() => {
            onFilterChange({
              severity: undefined,
              offset: 0
            });
            close();
          }}
          onApplyFilter={(value: string) =>
            onFilterChange({
              severity: Number.parseInt(value) as DeprecatedInfoSeverity,
              offset: 0
            })
          }
        />
      ),
      render: (deprecatedInfo: DeprecatedInfo) => (
        <Tooltip title={severityTagTooltip(deprecatedInfo)}>
          <Tag className='cursor-pointer' color={severityTagColor(deprecatedInfo)}>
            {severityTagText(deprecatedInfo)}
          </Tag>
        </Tooltip>
      )
    },
    {
      key: 'kubernetesVersion',
      title: 'Kubernetes Version',
      dataIndex: 'kubernetesVersion',
      sorter: true,
      sortOrder: parseSortOrder(filter.orderBy, 'kubernetesVersion'),
      render: (kubernetesVersion: string) => kubernetesVersion
    },
    {
      key: 'deprecatedIn',
      title: 'Deprecated In',
      dataIndex: 'deprecatedIn',
      sorter: true,
      sortOrder: parseSortOrder(filter.orderBy, 'deprecatedIn'),
      render: (deprecatedIn: string) => {
        const normalizedVersion = normalizeK8sVersion(deprecatedIn);
        if (normalizedVersion === normalizeK8sVersion('0')) {
          return 'N/A';
        }
        return deprecatedIn;
      }
    },
    {
      key: 'unavailableIn',
      title: 'Unavailable In',
      dataIndex: 'unavailableIn',
      sorter: true,
      sortOrder: parseSortOrder(filter.orderBy, 'unavailableIn'),
      render: (unavailableIn: string) => {
        const normalizedVersion = normalizeK8sVersion(unavailableIn);
        if (normalizedVersion === normalizeK8sVersion('0')) {
          return 'N/A';
        }
        return unavailableIn;
      }
    },
    {
      key: 'migrateTo',
      title: 'Migrate To',
      dataIndex: 'migrateTo',
      render: (migrateTo: GroupVersionKind) =>
        getAPIVersion({ group: migrateTo.group, version: migrateTo.version })
    },
    {
      key: 'resourceCount',
      title: 'Resource Count',
      dataIndex: 'resourceCount',
      sorter: true,
      sortOrder: parseSortOrder(filter.orderBy, 'resourceCount'),
      render: (resourceCount: number) => resourceCount
    }
  ];

  return isLoadingDeprecatedAPIs ? (
    <Flex justify={'center'} className='w-full mt-20' style={{ color: akpTextPrimaryColor }}>
      <Loading />
    </Flex>
  ) : (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            colorText: isDarkTheme ? akpTextDarkThemeColor : akpTextPrimaryColor
          }
        }
      }}
    >
      <Table
        columns={columns}
        rowKey={(record) =>
          `${record.clusterId}/${JSON.stringify(record.groupVersionKind)}/${record.kubernetesVersion}`
        }
        dataSource={deprecatedAPIs?.apis}
        rowClassName={'cursor-pointer'}
        onRow={(record) => ({
          onClick: () => onRowClick(record)
        })}
        onChange={handleTableChange(onFilterChange)}
        pagination={{
          pageSizeOptions: [20, 50, 100],
          pageSize: filter.limit,
          total: 0,
          current: filter.offset / filter.limit + 1,
          showSizeChanger: true
        }}
        scroll={{ x: 'max-content' }}
      />
    </ConfigProvider>
  );
};
