import { faBox } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Descriptions, DescriptionsProps, Drawer, Row } from 'antd';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { DeprecatedAPIFilter } from '@ui/feature/kubevision/components/kubevision-deprecated-api-dashboard/kubevison-deprecated-api-filter';
import { ExplorerFilter } from '@ui/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter';
import { KubeVisionExplorerTable } from '@ui/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-table';
import { DeprecatedTag } from '@ui/feature/kubevision/components/shared/deprecated-tag';
import { RecentViews } from '@ui/feature/kubevision/components/shared/recent-views';
import { DashboardType } from '@ui/feature/kubevision/const';
import { mergeFilter } from '@ui/feature/kubevision/filter';
import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import { getGroupVersion } from '@ui/feature/kubevision/utils';
import { useGetKubernetesResourceListQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

type Props = {
  filter: DeprecatedAPIFilter;
  onFilterChange: (filter: Partial<DeprecatedAPIFilter>, overwrite?: boolean) => void;
};

export const KubeVisionDeprecatedAPIDrawer = ({ filter, onFilterChange }: Props) => {
  const { organizationMode, enabledClustersInfo } = useAkuityIntelligenceContext();

  const { pathname, search } = useLocation();

  const { group, version } = getGroupVersion(filter.deprecatedApiVersion);
  const [explorerFilter, setExplorerFilter] = useState<ExplorerFilter>({
    group,
    version,
    kind: filter.deprecatedKind,
    cluster: filter.deprecatedCluster,
    limit: 20,
    offset: 0
  });

  const { setSearchParams } = useCustomSearchParams();
  const { data: resourceList, isLoading } = useGetKubernetesResourceListQuery(
    {
      instanceId: enabledClustersInfo.instanceId(filter.deprecatedInstance),
      clusterIds: [
        enabledClustersInfo.clusterId({
          instanceId: enabledClustersInfo.instanceId(filter.deprecatedInstance),
          clusterName: filter.deprecatedCluster
        })
      ],
      group,
      version,
      kind: filter.deprecatedKind,
      namespaces: [],
      limit: 20,
      offset: 0,
      treeViewNameContains: '',
      treeViewResourceKinds: [],
      treeViewHealthStatuses: []
    },
    { enabled: !!filter.deprecatedCluster }
  );

  useEffect(() => {
    setExplorerFilter({
      group,
      version,
      kind: filter.deprecatedKind,
      cluster: filter.deprecatedCluster,
      limit: 20,
      offset: 0
    });
  }, [filter]);

  const descriptionsItems: DescriptionsProps['items'] = [
    ...(organizationMode
      ? [
          {
            label: 'Instance',
            children: <div>{filter.deprecatedInstance}</div>
          }
        ]
      : []),
    {
      label: 'Cluster',
      children: <div>{filter.deprecatedCluster}</div>
    },
    {
      label: 'API Version',
      children: (
        <Row align='middle'>
          <span className='mr-2'>{filter.deprecatedApiVersion}</span>
          <DeprecatedTag />
        </Row>
      )
    },
    {
      label: 'Kind',
      children: <div>{filter.deprecatedKind}</div>
    },
    {
      label: 'Kubernetes Version',
      children: <div>{filter.deprecatedKubernetesVersion}</div>
    },
    {
      label: 'Deprecated in',
      children: <div>{filter.deprecatedDeprecatedIn}</div>
    },
    {
      label: 'Unavailable in',
      children: <div>{filter.deprecatedUnavailableIn}</div>
    },
    {
      label: 'Migrate to',
      children: (
        <Row align='middle'>
          <span className='mr-2'>{filter.deprecatedMigrateTo}</span>
        </Row>
      )
    },
    {
      label: 'Resource Count',
      children: <div>{isLoading ? <Loading /> : <div>{resourceList?.count || 0}</div>}</div>
    }
  ];

  return (
    <Drawer
      open={!!filter.deprecatedKind}
      onClose={() =>
        onFilterChange({
          deprecatedApiVersion: undefined,
          deprecatedKind: undefined,
          deprecatedCluster: undefined,
          deprecatedKubernetesVersion: undefined,
          deprecatedDeprecatedIn: undefined,
          deprecatedUnavailableIn: undefined,
          deprecatedMigrateTo: undefined
        })
      }
      title={
        <Row justify='space-between' align='middle'>
          <div>
            <FontAwesomeIcon icon={faBox} className='mr-2' />
            {filter.deprecatedApiVersion}
          </div>

          <RecentViews key={pathname + search} />
        </Row>
      }
      width='80%'
    >
      <div className={'flex flex-col h-full'}>
        <Descriptions
          styles={{ label: { fontWeight: 'bold', padding: '12px' } }}
          className='mb-8'
          size='small'
          bordered
          column={3}
          items={descriptionsItems}
        />

        <div className={'mb-2'}>
          <b>Resources</b>
        </div>

        {explorerFilter.group && (
          <div className={'grow overflow-auto'}>
            <KubeVisionExplorerTable
              instanceId={enabledClustersInfo.instanceId(filter.deprecatedInstance)}
              filter={explorerFilter}
              onFilterChange={(filter) => setExplorerFilter(mergeFilter(explorerFilter, filter))}
              onClickResource={(record) =>
                setSearchParams(
                  {
                    tab: 'kubevision',
                    dashboard: DashboardType.Explorer,
                    resourceId: record.uid,
                    resourceClusterId: filter.deprecatedCluster,
                    group,
                    version,
                    kind: filter.deprecatedKind
                  },
                  { overwrite: true }
                )
              }
              isDeprecatedApiDashboard={true}
            />
          </div>
        )}
      </div>
    </Drawer>
  );
};
