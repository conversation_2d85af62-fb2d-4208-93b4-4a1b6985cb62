import { mergeFilter } from '@ui/feature/kubevision/filter';
import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import { PageTitle } from '@ui/lib/components/page-title';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { KubeVisionCheckEnabledClusters } from '../shared/kubevision-check-enabled-clusters';

import { KubeVisionDeprecatedAPIDrawer } from './kubevision-deprecated-api-drawer';
import { KubeVisionDeprecatedAPIFilterBar } from './kubevision-deprecated-api-filter-bar';
import { KubevisionDeprecatedAPITable } from './kubevision-deprecated-api-table';
import { defaultDeprecatedAPIFilter, DeprecatedAPIFilter } from './kubevison-deprecated-api-filter';

export const KubevisonDeprecatedAPIDashboard = () => {
  const { organizationMode } = useAkuityIntelligenceContext();
  const { getSearchParam, getSearchParamPagination, setSearchParams } = useCustomSearchParams();

  const initFilter: Record<string, string | number> = {};
  const params = [
    'apiVersion',
    'kind',
    'severity',
    'instance',
    'cluster',
    'dpApiVersion',
    'dpKind',
    'dpInstance',
    'dpCluster',
    'dpKubernetesVersion',
    'dpDeprecatedIn',
    'dpUnavailableIn',
    'dpMigrateTo'
  ];

  for (const param of params) {
    if (getSearchParam(param)) {
      // To make the URL params as shorter as possible
      switch (param) {
        case 'apiVersion':
          initFilter['apiVersionContains'] = getSearchParam(param);
          break;
        case 'dpApiVersion':
          initFilter['deprecatedApiVersion'] = getSearchParam(param);
          break;
        case 'dpKind':
          initFilter['deprecatedKind'] = getSearchParam(param);
          break;
        case 'dpInstance':
          initFilter['deprecatedInstance'] = getSearchParam(param);
          break;
        case 'dpCluster':
          initFilter['deprecatedCluster'] = getSearchParam(param);
          break;
        case 'dpKubernetesVersion':
          initFilter['deprecatedKubernetesVersion'] = getSearchParam(param);
          break;
        case 'dpDeprecatedIn':
          initFilter['deprecatedDeprecatedIn'] = getSearchParam(param);
          break;
        case 'dpUnavailableIn':
          initFilter['deprecatedUnavailableIn'] = getSearchParam(param);
          break;
        case 'dpMigrateTo':
          initFilter['deprecatedMigrateTo'] = getSearchParam(param);
          break;
        default:
          initFilter[param] = getSearchParam(param);
      }
    }
  }
  // they must be assigned together
  if (initFilter['group'] || initFilter['version'] || initFilter['kind']) {
    initFilter['group'] = initFilter['group'] ?? '';
    initFilter['version'] = initFilter['version'] ?? '';
    initFilter['kind'] = initFilter['kind'] ?? '';
  }

  const { limit, offset, orderBy } = getSearchParamPagination();
  initFilter.limit = limit;
  initFilter.offset = offset;
  initFilter.orderBy = orderBy;

  const deprecatedAPIFilter = mergeFilter(defaultDeprecatedAPIFilter(), initFilter);

  const handleTableFilterChange = (filter: Partial<DeprecatedAPIFilter>, overwrite?: boolean) => {
    const newFilter = overwrite
      ? mergeFilter(defaultDeprecatedAPIFilter(), filter)
      : mergeFilter(deprecatedAPIFilter, filter);
    setSearchParams({
      apiVersion: newFilter?.apiVersionContains,
      kind: newFilter?.kind,
      severity: newFilter?.severity?.toString(),
      instance: organizationMode ? newFilter?.instance : undefined,
      cluster: newFilter?.cluster,
      dpApiVersion: newFilter?.deprecatedApiVersion,
      dpKind: newFilter?.deprecatedKind,
      dpInstance: newFilter?.deprecatedInstance,
      dpCluster: newFilter?.deprecatedCluster,
      dpKubernetesVersion: newFilter?.deprecatedKubernetesVersion,
      dpDeprecatedIn: newFilter?.deprecatedDeprecatedIn,
      dpUnavailableIn: newFilter?.deprecatedUnavailableIn,
      dpMigrateTo: newFilter?.deprecatedMigrateTo,
      limit: newFilter?.limit?.toString(),
      offset: newFilter?.offset?.toString(),
      orderBy: newFilter?.orderBy?.toString()
    });
  };

  return (
    <>
      <PageTitle>Akuity Intelligence Deprecated APIs</PageTitle>

      <KubeVisionDeprecatedAPIDrawer
        filter={deprecatedAPIFilter}
        onFilterChange={handleTableFilterChange}
      />
      <KubeVisionDeprecatedAPIFilterBar
        filter={deprecatedAPIFilter}
        onFilterChange={handleTableFilterChange}
      />

      <KubeVisionCheckEnabledClusters>
        <KubevisionDeprecatedAPITable
          filter={deprecatedAPIFilter}
          onFilterChange={handleTableFilterChange}
        />
      </KubeVisionCheckEnabledClusters>
    </>
  );
};
