import {
  defaultDeprecatedAPIFilter,
  DeprecatedAPIFilter,
  filterToListKubernetesDeprecatedAPIsRequest
} from '@ui/feature/kubevision/components/kubevision-deprecated-api-dashboard/kubevison-deprecated-api-filter';
import { ClusterSelect } from '@ui/feature/kubevision/components/shared/cluster-select';
import { FilterBar } from '@ui/feature/kubevision/components/shared/filter-bar';
import { usePlatformContext } from '@ui/feature/shared/context/platform-context';
import { getQueryParamsAsString } from '@ui/lib/apiclient/utils';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { InstanceSelect } from '../shared/instance-select';

type Props = {
  filter: DeprecatedAPIFilter;
  onFilterChange: (filter: Partial<DeprecatedAPIFilter>, overwrite?: boolean) => void;
};

export const KubeVisionDeprecatedAPIFilterBar = ({ filter, onFilterChange }: Props) => {
  const { apiPathPrefix, openUrlWithCookies } = usePlatformContext();
  const { organizationMode, instanceId, organizationId, enabledClustersInfo } =
    useAkuityIntelligenceContext();

  const onExportToCSV = () => {
    const params = filterToListKubernetesDeprecatedAPIsRequest(
      enabledClustersInfo,
      filter,
      instanceId
    );
    const url = `${apiPathPrefix}stream/orgs/${organizationId}/k8s/deprecated-apis-csv?${getQueryParamsAsString(params)}`;
    openUrlWithCookies(url, '_blank');
  };

  const clusterId = enabledClustersInfo.clusterId({
    instanceId,
    clusterName: filter.cluster
  });

  return (
    <FilterBar
      firstRow={
        <>
          {organizationMode && (
            <InstanceSelect
              instanceId={instanceId}
              showAllOption={true}
              hasDeprecatedApis={true}
              onChange={(instanceId) =>
                onFilterChange({
                  instance: enabledClustersInfo.instanceName(instanceId),
                  cluster: undefined,
                  offset: 0
                })
              }
            />
          )}

          <ClusterSelect
            instanceId={instanceId}
            hasDeprecatedApis={true}
            clusterId={clusterId}
            showAllOption={true}
            onChange={(clusterId, instanceId) =>
              onFilterChange({
                instance: enabledClustersInfo.instanceName(instanceId),
                cluster: enabledClustersInfo.clusterName(clusterId),
                offset: 0
              })
            }
          />
        </>
      }
      onExportToCSV={onExportToCSV}
      onReset={() => onFilterChange(defaultDeprecatedAPIFilter(), true)}
    />
  );
};
