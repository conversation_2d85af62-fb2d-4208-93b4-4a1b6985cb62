import {
  faBook,
  faCheckCircle,
  faExternalLink,
  faFire,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Modal, Steps, StepsProps, Tag } from 'antd';
import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';

import {
  useGetAIConversationQuery,
  useUpdateIncidentMutation
} from '@ui/lib/apiclient/organization/ai-queries';
import {
  AIConversationStepStatus,
  UpdateAIConversationRequest
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { useModal } from '@ui/lib/hooks';
import { renderMarkdown } from '@ui/lib/utils';

import { DashboardType } from '../../const';
import useCustomSearchParams from '../../hooks/use-custom-search-params';
import { formatTime } from '../../utils';

type IncidentDetailsProps = {
  conversationId: string;
  onIncidentUpdated: () => void;
};

export const IncidentDetails = ({ conversationId, onIncidentUpdated }: IncidentDetailsProps) => {
  const { enabledClustersInfo } = useAkuityIntelligenceContext();
  const { setSearchParams } = useCustomSearchParams();
  const [nativeSearchParams, setNativeSearchParams] = useSearchParams();
  const {
    data: incidentConversation,
    isLoading,
    refetch
  } = useGetAIConversationQuery(conversationId);
  const incident = incidentConversation?.conversation;
  const { mutateAsync: updateIncident } = useUpdateIncidentMutation();
  const { show } = useModal();
  const isResolved = useMemo(() => !!incident?.incident?.resolvedAt, [incident]);
  const steps = useMemo(
    () => incident?.messages?.flatMap((message) => message.steps) || [],
    [incident]
  );

  const stepItems: StepsProps['items'] = useMemo(() => {
    const items: StepsProps['items'] = [
      {
        title: <strong className='!text-red-500'>Incident Occurred</strong>,
        description: (
          <span className='text-gray-500'>
            Occurred at {formatTime(incident?.createTime?.toDate())},{' '}
            {formatTime(incident?.createTime?.toDate(), { fromNow: true })}
          </span>
        ),
        icon: <FontAwesomeIcon icon={faFire} className='w-[32px] h-[32px] text-red-500' />,
        status: 'finish'
      }
    ];
    steps.forEach((step) => {
      items.push({
        title: <strong>{step.name}</strong>,
        description: (
          <>
            <div
              className='text-gray-500 mb-1'
              dangerouslySetInnerHTML={{ __html: renderMarkdown(step.summary) }}
            />
            <span className='text-gray-500'>
              {step.endTime
                ? formatTime(step.endTime?.toDate())
                : formatTime(step.startTime?.toDate())}
            </span>
          </>
        ),
        icon: step.status == AIConversationStepStatus.AI_CONVERSATION_STEP_STATUS_RUNNING && (
          <FontAwesomeIcon color='#9ca3af' icon={faSpinner} spin={true} />
        ),
        status:
          (step.status == AIConversationStepStatus.AI_CONVERSATION_STEP_STATUS_RUNNING &&
            'process') ||
          (step.status == AIConversationStepStatus.AI_CONVERSATION_STEP_STATUS_FAILED && 'error') ||
          'finish'
      });
    });
    if (incident?.incident.resolvedAt) {
      items.push({
        title: <strong className='!text-green-500'>Incident Resolved</strong>,
        description: (
          <span className='text-gray-500'>
            Resolved at {formatTime(incident?.incident.resolvedAt?.toDate())},{' '}
            {formatTime(incident?.incident.resolvedAt?.toDate(), { fromNow: true })}
          </span>
        ),
        icon: <FontAwesomeIcon icon={faCheckCircle} className='w-[32px] h-[32px] text-green-500' />,
        status: 'finish'
      });
    }
    return items;
  }, [steps, incident]);

  return isLoading ? (
    <div className='w-full h-full flex items-center justify-center'>
      <Loading />
    </div>
  ) : (
    <div className='w-full h-full flex flex-col'>
      <div className='flex flex-row items-center justify-between gap-2 text-xl mb-2'>
        <div className='text-xl font-bold'>{incident?.title || 'Untitled Incident'}</div>

        <div className='w-fit flex flex-row gap-1'>
          <Button
            type='primary'
            onClick={() => {
              show((props) => (
                <Modal
                  title={isResolved ? 'Mark as Unresolved' : 'Mark as Resolved'}
                  open={props.visible}
                  onOk={async () => {
                    await updateIncident(
                      UpdateAIConversationRequest.fromJson({
                        id: incident?.id,
                        instanceId: incident?.instanceId,
                        title: incident?.title,
                        public: incident?.public,
                        contexts: incident?.contexts.map((context) => context.toJson()),
                        incident: {
                          resolved: !isResolved
                        }
                      })
                    );
                    refetch();
                    onIncidentUpdated();
                    props.hide();
                  }}
                  onCancel={props.hide}
                >
                  <p>Mark this incident as {isResolved ? 'unresolved' : 'resolved'}?</p>
                </Modal>
              ));
            }}
          >
            {isResolved ? 'Mark as Unresolved' : 'Mark as Resolved'}
          </Button>
          <Button
            type='default'
            icon={<FontAwesomeIcon icon={faExternalLink} />}
            onClick={() => {
              if (incident?.incident?.namespace) {
                const newSearchParams = new URLSearchParams(nativeSearchParams);
                newSearchParams.set('akuity-chat', conversationId);
                setNativeSearchParams(newSearchParams);
                setSearchParams(
                  {
                    tab: 'kubevision',
                    dashboard: DashboardType.Explorer,
                    namespace: incident?.incident?.namespace,
                    instance: enabledClustersInfo.instanceName(incident?.incident?.instanceId),
                    cluster: enabledClustersInfo.clusterName(incident?.incident?.clusterId),
                    treeView: 'true',
                    'akuity-chat': conversationId
                  },
                  { overwrite: true }
                );
              } else if (incident?.incident?.application) {
                window.open(
                  `https://${incident?.incident?.instanceHostname}/applications/argocd/${incident?.incident?.application}?akuity-chat=${conversationId}`,
                  '_blank'
                );
              } else {
                const newSearchParams = new URLSearchParams(nativeSearchParams);
                newSearchParams.set('akuity-chat', conversationId);
                setNativeSearchParams(newSearchParams);
              }
            }}
          >
            Open in Akuity Intelligence
          </Button>
        </div>
      </div>

      <div className='mb-2'>
        <Tag color={incident?.incident?.resolvedAt ? 'green' : 'red'}>
          {incident?.incident?.resolvedAt ? 'Resolved' : 'Unresolved'}
        </Tag>
      </div>

      <div className='mb-2'>
        <div className='font-bold'>Summary</div>
        <div className='my-2 flex flex-row flex-wrap gap-2'>
          {incident?.incident?.namespace && (
            <Button
              type='default'
              icon={<FontAwesomeIcon icon={faExternalLink} />}
              onClick={() => {
                setSearchParams(
                  {
                    tab: 'kubevision',
                    dashboard: DashboardType.Explorer,
                    namespace: incident?.incident?.namespace,
                    instance: enabledClustersInfo.instanceName(incident?.incident?.instanceId),
                    cluster: enabledClustersInfo.clusterName(incident?.incident?.clusterId),
                    treeView: 'true'
                  },
                  { overwrite: true }
                );
              }}
            >
              <span className='font-bold'>Namespace: </span>
              {incident?.incident?.namespace || 'N/A'}
            </Button>
          )}
          {incident?.incident?.application && incident?.incident?.instanceHostname && (
            <Button
              type='default'
              icon={<FontAwesomeIcon icon={faExternalLink} />}
              onClick={() => {
                window.open(
                  `https://${incident?.incident?.instanceHostname}/applications/argocd/${incident?.incident?.application}`,
                  '_blank'
                );
              }}
            >
              <span className='font-bold'>Application: </span>
              {incident?.incident?.application || 'N/A'}
            </Button>
          )}
          {incident?.runbooks?.map((runbook) => (
            <Button
              key={`${runbook.instanceId}-${runbook.name}`}
              type='default'
              icon={<FontAwesomeIcon icon={faBook} />}
              onClick={() => {
                setSearchParams({
                  runbook: runbook.name,
                  instanceId: incident?.incident?.instanceId,
                  incidentTab: 'runbooks'
                });
              }}
            >
              <span className='font-bold'>Runbook: </span>
              {runbook?.name || 'N/A'}
            </Button>
          ))}
        </div>
        <div>{incident?.incident?.summary || 'N/A'}</div>
      </div>
      <div className='mb-2'>
        <div className='font-bold'>Root Cause</div>
        <div>{incident?.incident?.rootCause || 'Unknown'}</div>
      </div>
      {incident?.incident?.resolution && (
        <div>
          <div className='font-bold'>Resolution</div>
          <div>{incident?.incident?.resolution}</div>
        </div>
      )}
      {steps.length > 0 && (
        <Steps
          direction='vertical'
          size='default'
          items={stepItems}
          className='mt-4 ai-message-steps'
        />
      )}
    </div>
  );
};
