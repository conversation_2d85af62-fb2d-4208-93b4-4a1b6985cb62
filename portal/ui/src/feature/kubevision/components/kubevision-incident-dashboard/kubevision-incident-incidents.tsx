import {
  faCheckCircle,
  faChevronCircleLeft,
  faChevronCircleRight,
  faFire
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Flex, Layout, Select } from 'antd';
import { useEffect, useMemo, useState } from 'react';

import { ResourceTag } from '@ui/feature/ai-support-engineer/components/resource-tag/resource-tag';
import { contextToResource } from '@ui/feature/ai-support-engineer/utils';
import { usePlatformContext } from '@ui/feature/shared/context/platform-context';
import { useListAIConversationsQuery } from '@ui/lib/apiclient/organization/ai-queries';
import { AIConversation, IncidentStatus } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { useSpotlightSearch } from '@ui/lib/hooks/use-spotlight-search';

import useCustomSearchParams from '../../hooks/use-custom-search-params';
import { formatTime } from '../../utils';

import { IncidentDetails } from './incident-details';

const { Content, Sider, Header } = Layout;

type IncidentStatusType = 'resolved' | 'unresolved' | '*';

type KubevisionIncidentIncidentsProps = {
  instanceId: string;
};

export const KubevisionIncidentIncidents = ({ instanceId }: KubevisionIncidentIncidentsProps) => {
  const { platform } = usePlatformContext();
  const isInAKP = platform === 'akuity-platform';
  const isInKargo = platform === 'kargo';
  const { isDarkTheme, enabledClustersInfo, organizationId } = useAkuityIntelligenceContext();
  const borderColor = isDarkTheme ? 'border-zinc-800' : 'border-zinc-200';
  const backgroundColor = isDarkTheme ? 'bg-zinc-800' : 'bg-zinc-100';
  const { getSearchParam, setSearchParams } = useCustomSearchParams();
  const status = ['resolved', 'unresolved'].includes(getSearchParam('status'))
    ? (getSearchParam('status') as IncidentStatusType)
    : '*';
  const application = getSearchParam('application') ? getSearchParam('application') : '';
  const namespace = getSearchParam('namespace') ? getSearchParam('namespace') : '';
  const cluster = getSearchParam('cluster') ? getSearchParam('cluster') : '';

  const filters = useMemo(() => {
    return {
      status,
      application,
      namespace,
      cluster
    };
  }, [status, application, namespace, cluster]);

  const { resources } = useSpotlightSearch(organizationId, instanceId, true);

  const [offset, setOffset] = useState(0);
  const limit = 20;

  const { data, isLoading, refetch } = useListAIConversationsQuery({
    incidentOnly: true,
    offset,
    limit,
    incidentStatus:
      filters.status === '*'
        ? undefined
        : filters.status === 'resolved'
          ? IncidentStatus.RESOLVED
          : IncidentStatus.UNRESOLVED,
    application: filters.application,
    namespace: filters.namespace,
    clusterId:
      enabledClustersInfo.clusterId({
        instanceId,
        clusterName: filters.cluster
      }) || ''
  });

  const incidents = data?.conversations ?? [];
  const totalCount = Number(data?.count || 0);

  const [selectedIncident, setSelectedIncident] = useState<AIConversation | null>(null);

  useEffect(() => {
    const searchParam = getSearchParam('incidentId');
    const incident = incidents?.find((incident) => incident.id === searchParam);
    if (incident && !selectedIncident) {
      setSelectedIncident(incident);
    }
  }, [getSearchParam, data, selectedIncident]);

  const applications = useMemo(() => {
    const applications: string[] = [];
    resources.forEach((resource) => {
      if (resource.kind === 'Application') {
        applications.push(resource.name);
      }
    });
    return applications;
  }, [resources]);

  const namespaces = useMemo(() => {
    const namespaces: { namespace: string; clusterId: string }[] = [];
    resources.forEach((resource) => {
      if (resource.kind === 'Namespace') {
        namespaces.push({ namespace: resource.name, clusterId: resource.clusterId });
      }
    });
    return namespaces;
  }, [resources]);

  return (
    <Layout className='h-[calc(100vh-310px)] bg-transparent'>
      <Header className='flex items-center justify-start bg-transparent  px-0 h-fit pb-2 overflow-x-auto'>
        <div className='flex items-center gap-4'>
          <div className='flex items-center gap-1'>
            <span className='text-sm'>Status:</span>
            <Select
              title='Status'
              size='middle'
              className='w-auto'
              style={{ minWidth: 180 }}
              options={[
                { label: 'All', value: '*' },
                { label: 'Resolved', value: 'resolved' },
                { label: 'Unresolved', value: 'unresolved' }
              ]}
              value={filters.status}
              onChange={(value) => {
                setSearchParams({ status: value === '*' ? '' : value });
              }}
              showSearch
            />
          </div>
          <div className='flex items-center gap-1'>
            <span className='text-sm'>Application:</span>
            <Select
              title='Application'
              size='middle'
              className='w-auto'
              style={{ minWidth: 200 }}
              options={[
                { label: 'All', value: '' },
                ...applications.map((application) => ({
                  label: application,
                  value: application
                }))
              ]}
              value={filters.application}
              onChange={(value) => {
                setSearchParams({ application: value });
              }}
              showSearch
            />
          </div>
          <div className='flex items-center gap-1'>
            <span className='text-sm'>Namespace:</span>
            <Select
              title='Namespace'
              size='middle'
              className='w-auto'
              style={{ minWidth: 200 }}
              options={[
                { label: 'All', value: '' },
                ...namespaces.map((namespace) => ({
                  label: (
                    <div className='flex flex-col'>
                      <div>{namespace.namespace}</div>
                      <div className='text-xs'>
                        <span className='text-xs text-gray-400'>Cluster:</span>{' '}
                        <span className='text-xs text-[#64748b]'>
                          {enabledClustersInfo.clusterName(namespace.clusterId)}
                        </span>
                      </div>
                    </div>
                  ),
                  value: namespace.clusterId + '/' + namespace.namespace
                }))
              ]}
              value={filters.namespace}
              onChange={(value) => {
                if (value) {
                  const [clusterId, namespace] = value.split('/');
                  const clusterName = enabledClustersInfo.clusterName(clusterId);
                  setSearchParams({ namespace: namespace, cluster: clusterName });
                } else {
                  setSearchParams({ namespace: '', cluster: '' });
                }
              }}
              showSearch
            />
          </div>
        </div>
      </Header>
      <Layout className='flex-grow bg-transparent'>
        <Sider
          width={320}
          className={`bg-transparent border-0 border-r border-solid ${borderColor}`}
        >
          {isLoading ? (
            <Loading></Loading>
          ) : (
            <Flex vertical className='h-full'>
              <Flex className='justify-between items-center px-2 py-1' gap={2}>
                <div>
                  {!isLoading && (
                    <div className='result-text'>
                      {totalCount} incident{totalCount > 1 ? 's' : ''}
                    </div>
                  )}
                </div>
                <div>
                  <Button
                    type='text'
                    icon={<FontAwesomeIcon icon={faChevronCircleLeft} />}
                    onClick={() => {
                      let newOffset = offset - limit;
                      if (newOffset < 0) {
                        newOffset = 0;
                      }
                      setOffset(newOffset);
                    }}
                    disabled={offset === 0}
                  />
                  <Button
                    type='text'
                    icon={<FontAwesomeIcon icon={faChevronCircleRight} />}
                    onClick={() => {
                      let newOffset = offset + limit;
                      if (newOffset > totalCount) {
                        newOffset = totalCount;
                      }
                      setOffset(newOffset);
                    }}
                    disabled={offset + limit >= totalCount}
                  />
                </div>
              </Flex>
              <div className='flex-grow overflow-y-auto'>
                {incidents.map((incident) => (
                  <div
                    key={incident.id}
                    className={`py-2 px-4 ${isDarkTheme ? 'hover:bg-zinc-800' : 'hover:bg-zinc-100'} cursor-pointer flex flex-row items-center ${selectedIncident?.id === incident.id ? backgroundColor : ''}`}
                    onClick={() => {
                      setSelectedIncident(incident);
                      setSearchParams({ incidentId: incident.id });
                    }}
                  >
                    <div className='mr-4'>
                      {incident.incident.resolvedAt ? (
                        <FontAwesomeIcon
                          icon={faCheckCircle}
                          className='text-green-500 w-[24px] h-[24px]'
                        />
                      ) : (
                        <FontAwesomeIcon icon={faFire} className='text-red-500 w-[24px] h-[24px]' />
                      )}
                    </div>
                    <div>
                      <div>{incident.title || 'Untitled Incident'}</div>
                      <div className='mt-1 mb-3 text-xs text-gray-500 flex flex-row flex-wrap gap-1 '>
                        {incident.contexts.map((context, index) => {
                          const resource = contextToResource(context, {
                            isInAKP,
                            isInKargo,
                            organizationName: organizationId,
                            instanceName: enabledClustersInfo.instanceName(instanceId),
                            clusterName: context.k8sNamespace
                              ? enabledClustersInfo.clusterName(context.k8sNamespace.clusterId)
                              : undefined
                          });
                          return (
                            <ResourceTag
                              key={index}
                              enabledClustersInfo={enabledClustersInfo}
                              resource={{ ...resource, link: '' }}
                            />
                          );
                        })}
                      </div>
                      <div className='text-xs text-gray-500'>
                        {incident.incident.resolvedAt
                          ? `Resolved at ${formatTime(incident.incident.resolvedAt.toDate(), {
                              fromNow: true
                            })}`
                          : `Occurred at ${formatTime(incident.createTime.toDate(), { fromNow: true })}`}
                      </div>
                    </div>
                  </div>
                ))}
                {incidents.length === 0 && (
                  <div className='flex w-full h-full items-center justify-center text-sm text-gray-500'>
                    No incidents found
                  </div>
                )}
              </div>
            </Flex>
          )}
        </Sider>
        <Content className='overflow-y-auto p-4 bg-transparent'>
          {selectedIncident && (
            <IncidentDetails
              conversationId={selectedIncident.id}
              onIncidentUpdated={() => refetch()}
            />
          )}
          {!selectedIncident && (
            <div className='flex w-full h-full items-center justify-center text-sm text-gray-500'>
              No incident selected
            </div>
          )}
        </Content>
      </Layout>
    </Layout>
  );
};
