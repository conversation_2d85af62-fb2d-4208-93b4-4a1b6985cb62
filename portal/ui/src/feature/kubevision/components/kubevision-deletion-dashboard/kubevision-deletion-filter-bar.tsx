import { usePlatformContext } from '@ui/feature/shared/context/platform-context';
import { GroupVersionKind } from '@ui/lib/apiclient/types/k8s/v1/k8s_pb';
import { getQueryParamsAsString } from '@ui/lib/apiclient/utils';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { ClusterSelect } from '../shared/cluster-select';
import { FilterBar } from '../shared/filter-bar';
import { InstanceSelect } from '../shared/instance-select';
import { KindSelect } from '../shared/kind-select';
import { NamespaceSelect } from '../shared/namespace-select';

import {
  defaultDeletionFilter,
  DeletionFilter,
  filterToListKubernetesDeletionRequest
} from './kubevision-deletion-filter';

type Props = {
  filter: DeletionFilter;
  onFilterChange: (filter: Partial<DeletionFilter>, overwrite?: boolean) => void;
};

export const KubevisionDeletionFilterBar = ({ filter, onFilterChange }: Props) => {
  const { apiPathPrefix, openUrlWithCookies } = usePlatformContext();
  const { organizationMode, instanceId, organizationId, enabledClustersInfo } =
    useAkuityIntelligenceContext();

  const onExportToCSV = () => {
    const params = filterToListKubernetesDeletionRequest(enabledClustersInfo, filter, instanceId);
    const url = `${apiPathPrefix}stream/orgs/${organizationId}/k8s/resources-csv?${getQueryParamsAsString(params)}`;

    openUrlWithCookies(url, '_blank');
  };

  const clusterId = enabledClustersInfo.clusterId({
    instanceId,
    clusterName: filter.cluster
  });

  return (
    <FilterBar
      firstRow={
        <>
          {organizationMode && (
            <InstanceSelect
              instanceId={instanceId}
              showAllOption={true}
              onChange={(instanceId) =>
                onFilterChange({
                  instance: enabledClustersInfo.instanceName(instanceId),
                  cluster: undefined,
                  offset: 0
                })
              }
            />
          )}

          <ClusterSelect
            instanceId={instanceId}
            clusterId={clusterId}
            showAllOption={true}
            onChange={(clusterId, instanceId) =>
              onFilterChange({
                instance: enabledClustersInfo.instanceName(instanceId),
                cluster: enabledClustersInfo.clusterName(clusterId),
                offset: 0
              })
            }
          />

          <NamespaceSelect
            instanceId={instanceId}
            clusterId={clusterId}
            namespace={filter.namespace}
            groupVersionKind={GroupVersionKind.fromJson({
              group: filter.group || '',
              version: filter.version || '',
              kind: filter.kind || ''
            })}
            onChange={(namespace) =>
              onFilterChange({
                namespace: namespace,
                offset: 0
              })
            }
          />

          <KindSelect
            instanceId={instanceId}
            kind={filter.kind}
            showAllOption={true}
            onChange={(gvk, clusterScoped) =>
              onFilterChange({
                group: gvk.group,
                version: gvk.version,
                kind: gvk.kind,
                offset: 0,
                namespace: !clusterScoped ? filter.namespace : undefined
              })
            }
          />
        </>
      }
      onExportToCSV={onExportToCSV}
      onReset={() => onFilterChange(defaultDeletionFilter(), true)}
    />
  );
};
