import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import { PageTitle } from '@ui/lib/components/page-title';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { TabType } from '../../const';
import { clearQueryParams, mergeFilter } from '../../filter';
import { KubeVisionExplorerDrawer } from '../kubevision-explorer-dashboard/kubevision-explorer-drawer';
import { ExplorerFilter } from '../kubevision-explorer-dashboard/kubevision-explorer-filter';
import { KubeVisionCheckEnabledClusters } from '../shared/kubevision-check-enabled-clusters';
import { isEventTimelineEnabled } from '../shared/kubevision-event-timeline/helper';

import { defaultDeletionFilter, DeletionFilter } from './kubevision-deletion-filter';
import { KubevisionDeletionFilterBar } from './kubevision-deletion-filter-bar';
import { KubeVisionDeletionTable } from './kubevision-deletion-table';

export const KubevisionDeletionDashboard = () => {
  const { organizationMode, enabledClustersInfo } = useAkuityIntelligenceContext();
  const { getSearchParam, getSearchParamPagination, setSearchParams } = useCustomSearchParams();

  const initFilter: Record<string, string | number> = {};
  const params = [
    'resourceId',
    'resourceInstance',
    'resourceCluster',
    'group',
    'version',
    'kind',
    'instance',
    'cluster',
    'namespace',
    'detailsTab'
  ];

  for (const param of params) {
    if (getSearchParam(param)) initFilter[param] = getSearchParam(param);
  }
  // they must be assigned together
  if (initFilter['group'] || initFilter['version'] || initFilter['kind']) {
    initFilter['group'] = initFilter['group'] ?? '';
    initFilter['version'] = initFilter['version'] ?? '';
    initFilter['kind'] = initFilter['kind'] ?? '';
  }

  const { limit, offset, orderBy } = getSearchParamPagination();
  initFilter.limit = limit;
  initFilter.offset = offset;
  initFilter.orderBy = orderBy;

  const deletedFilter = mergeFilter(defaultDeletionFilter(), initFilter);
  const handleTableFilterChange = (filter: Partial<DeletionFilter>, overwrite?: boolean) => {
    const newFilter = overwrite
      ? mergeFilter(defaultDeletionFilter(), filter)
      : mergeFilter(deletedFilter, filter);

    let filterToUse = newFilter;

    // clear logs params
    if (newFilter.detailsTab !== TabType.Logs) {
      filterToUse = clearQueryParams(filterToUse, TabType.Logs);
    }

    // clear eventTimeline params
    if (newFilter.detailsTab !== TabType.EventTimeline) {
      filterToUse = clearQueryParams(filterToUse, TabType.EventTimeline);
    }

    setSearchParams({
      resourceId: filterToUse?.resourceId,
      resourceInstance: filterToUse?.resourceInstance,
      resourceCluster: filterToUse?.resourceCluster,
      group: filterToUse?.group,
      version: filterToUse?.version,
      kind: filterToUse?.kind,
      instance: organizationMode ? filterToUse?.instance : undefined,
      cluster: filterToUse?.cluster,
      namespace: filterToUse?.namespace,
      limit: filterToUse?.limit?.toString(),
      offset: filterToUse?.offset?.toString(),
      orderBy: filterToUse?.orderBy?.toString(),
      detailsTab: filterToUse?.detailsTab
    });
  };

  return (
    <>
      <PageTitle>Akuity Intelligence Stuck-in-Deletion Resources</PageTitle>

      <KubeVisionExplorerDrawer
        isUnderDeletion={true}
        instanceId={enabledClustersInfo.instanceId(deletedFilter.resourceInstance)}
        filter={deletedFilter as ExplorerFilter}
        onFilterChange={handleTableFilterChange}
      />
      <KubevisionDeletionFilterBar
        filter={deletedFilter}
        onFilterChange={handleTableFilterChange}
      />
      <KubeVisionCheckEnabledClusters>
        <KubeVisionDeletionTable
          filter={deletedFilter}
          onFilterChange={handleTableFilterChange}
          onClickResource={(record) =>
            handleTableFilterChange({
              resourceId: record.uid,
              resourceInstance: enabledClustersInfo.instanceName(record.instanceId),
              resourceCluster: enabledClustersInfo.clusterName(record.clusterId),
              detailsTab: isEventTimelineEnabled(record.group, record.kind)
                ? TabType.EventTimeline
                : TabType.Manifest
            })
          }
        />
      </KubeVisionCheckEnabledClusters>
    </>
  );
};
