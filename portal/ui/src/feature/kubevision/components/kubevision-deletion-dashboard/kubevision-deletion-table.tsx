import { ConfigProvider, Flex, Table, TableColumnsType } from 'antd';

import { handleTableChange, parseSortOrder } from '@ui/feature/kubevision/filter';
import { useGetKubernetesResourceListQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { ClusterResource } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { getResourceKey } from '@ui/lib/utils';

import { akpTextDarkThemeColor, akpTextPrimaryColor } from '../../const';
import { formatTime } from '../../utils';
import { KubernetesButton } from '../shared/kubevision-button';
import { createDetailsColumn } from '../shared/kubevision-table-details-column';

import {
  DeletionFilter,
  filterToListKubernetesDeletionRequest
} from './kubevision-deletion-filter';

type Props = {
  filter: DeletionFilter;
  onFilterChange: (filter: Partial<DeletionFilter>, overwrite?: boolean) => void;
  onClickResource: (resource: ClusterResource) => void;
};

export const KubeVisionDeletionTable = ({ filter, onFilterChange, onClickResource }: Props) => {
  const { organizationMode, instanceId, isDarkTheme, enabledClustersInfo } =
    useAkuityIntelligenceContext();

  const { data: resources, isLoading: isLoadingResources } = useGetKubernetesResourceListQuery(
    filterToListKubernetesDeletionRequest(enabledClustersInfo, filter, instanceId)
  );

  const columns: TableColumnsType<ClusterResource> = [
    createDetailsColumn({ onRowClick: onClickResource }),
    {
      key: 'instance',
      title: 'Instance',
      dataIndex: 'instanceId',
      width: 150,
      hidden: !organizationMode || !!filter.instance,
      render: (instanceId: string) => {
        const instanceName = enabledClustersInfo.instanceName(instanceId);
        return (
          <KubernetesButton
            tooltip={
              <>
                <b className={'mr-1'}>Filter by instance:</b>
                {instanceName}
              </>
            }
            text={instanceName}
            maxWidth={200}
            onClick={(e) => {
              e.stopPropagation();
              onFilterChange({
                instance: instanceName,
                cluster: undefined,
                offset: 0
              });
            }}
          />
        );
      }
    },
    {
      key: 'cluster',
      title: 'Cluster',
      dataIndex: 'clusterId',
      width: 150,
      hidden: !!filter.cluster,
      render: (clusterId: string, record: ClusterResource) => {
        const clusterName = enabledClustersInfo.clusterName(clusterId);
        return (
          <KubernetesButton
            tooltip={
              <>
                <b className={'mr-1'}>Filter by cluster:</b>
                {clusterName}
              </>
            }
            text={clusterName}
            maxWidth={200}
            onClick={(e) => {
              e.stopPropagation();
              onFilterChange({
                instance: enabledClustersInfo.instanceName(record.instanceId),
                cluster: clusterName,
                offset: 0
              });
            }}
          />
        );
      }
    },
    {
      key: 'kind',
      title: 'Kind',
      dataIndex: 'kind',
      render: (kind: string) => kind
    },
    {
      key: 'namespace',
      title: 'Namespace',
      dataIndex: 'namespace',
      render: (namespace: string) => namespace
    },
    {
      key: 'name',
      title: 'Name',
      dataIndex: 'name',
      render: (name: string) => name
    },
    {
      key: 'deletionTimestamp',
      title: 'Deletion Timestamp',
      dataIndex: 'deleteTime',
      sorter: true,
      sortOrder: parseSortOrder(filter.orderBy, 'deletionTimestamp'),
      render: (deletionTimestamp: string) => formatTime(deletionTimestamp)
    },
    {
      key: 'deletionDuration',
      title: 'Deletion Duration',
      dataIndex: 'deleteTime',
      render: (deletionTimestamp: string) =>
        formatTime(deletionTimestamp, { fromNow: true }).replace('ago', '')
    }
  ];

  return isLoadingResources ? (
    <Flex justify={'center'} className='w-full mt-20' style={{ color: akpTextPrimaryColor }}>
      <Loading />
    </Flex>
  ) : (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            colorText: isDarkTheme ? akpTextDarkThemeColor : akpTextPrimaryColor
          }
        }
      }}
    >
      <Table
        columns={columns}
        dataSource={resources?.resources}
        rowKey={(record) => getResourceKey(record)}
        onChange={handleTableChange(onFilterChange)}
        pagination={{
          pageSizeOptions: [20, 50, 100],
          pageSize: filter.limit,
          total: resources?.count ?? 0,
          current: filter.offset / filter.limit + 1,
          showSizeChanger: true
        }}
        scroll={{ x: 'max-content' }}
        rowClassName={'cursor-pointer'}
        onRow={(record) => ({
          onClick: () => onClickResource?.(record as ClusterResource)
        })}
      />
    </ConfigProvider>
  );
};
