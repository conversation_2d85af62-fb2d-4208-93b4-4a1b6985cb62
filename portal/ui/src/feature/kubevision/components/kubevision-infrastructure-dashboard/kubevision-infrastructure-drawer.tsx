import { faBox } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Descriptions, DescriptionsProps, Drawer, Flex, Row, Tag } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { ContainerFilter } from '@ui/feature/kubevision/components/kubevision-container-dashboard/kubevision-container-filter';
import { KubeVisionContainerTable } from '@ui/feature/kubevision/components/kubevision-container-dashboard/kubevision-container-table';
import { ExplorerFilter } from '@ui/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter';
import { KubeVisionExplorerFilterBar } from '@ui/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter-bar';
import { KubeVisionExplorerTable } from '@ui/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-table';
import { RecentViews } from '@ui/feature/kubevision/components/shared/recent-views';
import { DashboardType, heatmapView } from '@ui/feature/kubevision/const';
import { mergeFilter } from '@ui/feature/kubevision/filter';
import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import {
  useGetKubernetesClusterDetailQuery,
  useGetKubernetesNamespaceDetailQuery,
  useGetKubernetesNodeDetailQuery,
  useGetKubernetesPodDetailQuery
} from '@ui/lib/apiclient/organization/kubevision-queries';
import { KubernetesPodStatus } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { formatCoreSize, humanReadableSize } from '@ui/lib/utils';

import { ErrorNotFound } from '../shared/error-not-found';

import { ClusterUsage } from './cluster-usage';
import { InfrastructureFilter } from './kubevision-infrastructure-filter';

type Props = {
  instanceId: string;
  filter: InfrastructureFilter;
  onFilterChange: (filter: Partial<InfrastructureFilter>, overwrite?: boolean) => void;
};

export const KubeVisionInfrastructureDrawer = ({ instanceId, filter, onFilterChange }: Props) => {
  const { organizationMode, enabledClustersInfo } = useAkuityIntelligenceContext();
  const { pathname, search } = useLocation();

  const isNodesView = filter.view === heatmapView.Nodes;
  const isPodsView = filter.view === heatmapView.Pods;
  const isNamespacesView = filter.view === heatmapView.Namespaces;

  const instanceName = enabledClustersInfo.instanceName(instanceId);

  const { data: nodeDetailData, isLoading: isNodeDetailLoading } = useGetKubernetesNodeDetailQuery(
    {
      instanceId,
      clusterId: enabledClustersInfo.clusterId({
        instanceId,
        clusterName: filter.nodeCluster
      }),
      nodeId: filter.nodeId
    },
    { enabled: !!filter.nodeId && isNodesView }
  );

  const { data: podDetailData, isLoading: isPodDetailLoading } = useGetKubernetesPodDetailQuery(
    {
      instanceId,
      clusterId: enabledClustersInfo.clusterId({
        instanceId,
        clusterName: filter.podCluster
      }),
      podId: filter.podId
    },
    { enabled: !!filter.podId && isPodsView }
  );

  const { data: namespaceDetailData, isLoading: isNamespaceDetailLoading } =
    useGetKubernetesNamespaceDetailQuery(
      {
        instanceId,
        clusterId: enabledClustersInfo.clusterId({
          instanceId,
          clusterName: filter.namespaceCluster
        }),
        namespaceId: filter.namespaceId
      },
      {
        enabled: !!filter.namespaceId && isNamespacesView
      }
    );

  const { data: clusterDetailData, isLoading: isClusterDetailLoading } =
    useGetKubernetesClusterDetailQuery(
      {
        instanceId,
        clusterId: enabledClustersInfo.clusterId({
          instanceId,
          clusterName: filter.clusterCluster
        })
      },
      { enabled: !!filter.clusterCluster }
    );

  const { setSearchParams } = useCustomSearchParams();

  const explorerRef = useRef(null);

  const nodeExplorerFilter = {
    group: '',
    version: 'v1',
    kind: 'Pod',
    limit: 20,
    offset: 0,
    nodeId: filter.nodeId,
    nodeCluster: filter.nodeCluster,
    where: filter.where
  };

  const namespaceExplorerFilter = {
    group: '',
    version: 'v1',
    kind: 'Pod',
    namespace: namespaceDetailData?.name,
    cluster: enabledClustersInfo.clusterName(namespaceDetailData?.clusterId),
    limit: 20,
    offset: 0
  };

  const clusterExplorerFilter = {
    group: '',
    version: '',
    kind: 'Node',
    limit: 20,
    offset: 0
  };

  const defaultContainerFilter = {
    podId: filter.podId,
    limit: 20,
    offset: 0
  };

  const [explorerFilter, setExplorerFilter] = useState<ExplorerFilter | null>(null);
  const [containerFilter, setContainerFilter] = useState<ContainerFilter | null>(null);

  useEffect(() => {
    return () => {
      // when user navigate to other page, the drawer close event will not
      // be triggered, so we need to reset the filters here
      setExplorerFilter(null);
      setContainerFilter(null);
    };
  }, []);

  useEffect(() => {
    if (isNodesView && filter.nodeId) {
      setExplorerFilter(nodeExplorerFilter);
    }
  }, [filter.nodeId, filter.nodeCluster, filter.where, isNodesView]);

  useEffect(() => {
    if (isPodsView && filter.podId) {
      setContainerFilter(defaultContainerFilter);
    }
  }, [filter.podId, isPodsView]);

  useEffect(() => {
    if (isNamespacesView && namespaceDetailData?.name) {
      setExplorerFilter(namespaceExplorerFilter);
    }
  }, [
    enabledClustersInfo,
    namespaceDetailData?.name,
    namespaceDetailData?.clusterId,
    isNamespacesView
  ]);

  useEffect(() => {
    if (filter.clusterCluster) {
      setExplorerFilter({
        ...clusterExplorerFilter,
        cluster: filter.clusterCluster,
        instance: instanceId
      });
    }
  }, [filter.clusterCluster]);

  const drawerTitle = useMemo(() => {
    return isNodeDetailLoading ||
      isPodDetailLoading ||
      isNamespaceDetailLoading ||
      isClusterDetailLoading
      ? 'Loading...'
      : nodeDetailData?.name ||
          podDetailData?.name ||
          namespaceDetailData?.name ||
          `${clusterDetailData?.name} overview` ||
          'N/A';
  }, [
    isNodeDetailLoading,
    isPodDetailLoading,
    isNamespaceDetailLoading,
    isClusterDetailLoading,
    nodeDetailData,
    podDetailData,
    namespaceDetailData,
    clusterDetailData
  ]);

  const getStatusText = (status: KubernetesPodStatus) => {
    switch (status) {
      case KubernetesPodStatus.RUNNING:
        return 'Running';
      case KubernetesPodStatus.PENDING:
        return 'Pending';
      case KubernetesPodStatus.SUCCEEDED:
        return 'Succeeded';
      case KubernetesPodStatus.FAILED:
        return 'Failed';
      case KubernetesPodStatus.UNSPECIFIED:
        return 'Unspecified';
      default:
        return null;
    }
  };

  const getStatusColor = (status: KubernetesPodStatus) => {
    switch (status) {
      case KubernetesPodStatus.RUNNING:
        return 'success';
      case KubernetesPodStatus.PENDING:
        return 'warning';
      case KubernetesPodStatus.SUCCEEDED:
        return 'default';
      case KubernetesPodStatus.FAILED:
        return 'error';
      case KubernetesPodStatus.UNSPECIFIED:
        return 'default';
      default:
        return null;
    }
  };

  const PodStatusTag = ({ status }: { status: KubernetesPodStatus }) => {
    const statusText = getStatusText(status);
    const statusColor = getStatusColor(status);

    return (
      <Tag color={statusColor} className={'cursor-pointer text-xs px-2 py-1'}>
        {statusText}
      </Tag>
    );
  };

  const nodeDetailItems = [
    ...(organizationMode
      ? [
          {
            label: 'Instance',
            children: <div>{instanceName}</div>
          }
        ]
      : []),
    {
      label: 'Cluster',
      children: nodeDetailData?.clusterName || '-'
    },
    {
      label: 'CRI',
      children: nodeDetailData?.cri || '-'
    },
    {
      label: 'Kubelet Version',
      children: nodeDetailData?.kubeletVersion || '-'
    },
    {
      label: 'CPU Utilization',
      children:
        nodeDetailData?.usageCpu !== undefined && nodeDetailData?.usageCpu !== null
          ? nodeDetailData.usageCpu.toFixed(2) + '%'
          : 'N/A'
    },
    {
      label: 'Memory Utilization',
      children:
        nodeDetailData?.usageMemory !== undefined && nodeDetailData?.usageMemory !== null
          ? nodeDetailData.usageMemory.toFixed(2) + '%'
          : 'N/A'
    },
    {
      label: 'Pod Count',
      children:
        nodeDetailData?.allocatedPods !== undefined && nodeDetailData?.allocatedPods !== null
          ? nodeDetailData.allocatedPods.toString()
          : 'N/A'
    },
    {
      label: 'Node ID',
      children: nodeDetailData?.id || '-'
    },
    {
      label: 'Host',
      children: nodeDetailData?.hostname || '-'
    },
    {
      label: 'Platform',
      children: nodeDetailData?.platform || '-'
    },
    {
      label: 'Region',
      children: nodeDetailData?.region || '-'
    },
    {
      label: 'Availability Zone',
      children: nodeDetailData?.availabilityZone || '-'
    }
  ];

  const podDetailItems = [
    ...(organizationMode
      ? [
          {
            label: 'Instance',
            children: <div>{instanceName}</div>
          }
        ]
      : []),
    {
      label: 'Cluster',
      children: podDetailData?.clusterName || '-'
    },
    {
      label: 'Node',
      children: podDetailData?.nodeName || '-'
    },
    {
      label: 'Pod ID',
      children: podDetailData?.id || '-'
    },
    {
      label: 'Namespace',
      children: podDetailData?.namespace || '-'
    },
    {
      label: 'Region',
      children: podDetailData?.region || '-'
    },
    {
      label: 'Availability Zone',
      children: podDetailData?.availabilityZone || '-'
    },
    {
      label: 'CPU Usage',
      children:
        podDetailData?.usageCpu !== undefined && podDetailData?.usageCpu !== null
          ? formatCoreSize({ size: podDetailData.usageCpu, hasUnit: true })
          : 'N/A'
    },
    {
      label: 'Memory Usage',
      children:
        podDetailData?.usageMemory !== undefined && podDetailData?.usageMemory !== null
          ? humanReadableSize(podDetailData.usageMemory)
          : 'N/A'
    },
    {
      label: 'Phase',
      children: podDetailData?.status ? <PodStatusTag status={podDetailData.status} /> : '-'
    }
  ];

  const namespaceDetailItems = [
    ...(organizationMode
      ? [
          {
            label: 'Instance',
            children: <div>{instanceName}</div>
          }
        ]
      : []),
    {
      label: 'Cluster',
      children: enabledClustersInfo.clusterName(namespaceDetailData?.clusterId) || '-'
    },
    {
      label: 'Namespace',
      children: namespaceDetailData?.name || '-'
    },
    {
      label: 'CPU Usage',
      children:
        namespaceDetailData?.usageCpu !== undefined && namespaceDetailData?.usageCpu !== null
          ? formatCoreSize({ size: namespaceDetailData.usageCpu, hasUnit: true })
          : 'N/A'
    },
    {
      label: 'Memory Usage',
      children:
        namespaceDetailData?.usageMemory !== undefined && namespaceDetailData?.usageMemory !== null
          ? humanReadableSize(namespaceDetailData.usageMemory)
          : 'N/A'
    },
    {
      label: 'Pod Count',
      children:
        namespaceDetailData?.podCount !== undefined && namespaceDetailData?.podCount !== null
          ? namespaceDetailData.podCount.toString()
          : 'N/A'
    },
    {
      label: 'Running Pods',
      children:
        namespaceDetailData?.runningPodCount !== undefined &&
        namespaceDetailData?.runningPodCount !== null
          ? namespaceDetailData.runningPodCount.toString()
          : 'N/A'
    }
  ];

  const items: DescriptionsProps['items'] = useMemo(() => {
    if (isNodesView) {
      return nodeDetailItems;
    }
    if (isPodsView) {
      return podDetailItems;
    }
    if (isNamespacesView) {
      return namespaceDetailItems;
    }
    return [];
  }, [
    isNodesView,
    isPodsView,
    isNamespacesView,
    nodeDetailItems,
    podDetailItems,
    namespaceDetailItems
  ]);

  return (
    <Drawer
      open={
        !!filter?.nodeId || !!filter?.podId || !!filter?.namespaceId || !!filter?.clusterCluster
      }
      onClose={() => {
        setExplorerFilter(null);
        setContainerFilter(null);
        onFilterChange({
          nodeId: undefined,
          nodeInstance: undefined,
          nodeCluster: undefined,
          podId: undefined,
          podInstance: undefined,
          podCluster: undefined,
          namespaceId: undefined,
          namespaceInstance: undefined,
          namespaceCluster: undefined,
          clusterCluster: undefined,
          where: undefined
        });
      }}
      title={
        <Row justify='space-between' align='middle'>
          <span>
            <FontAwesomeIcon icon={faBox} className='mr-2' />
            {drawerTitle}
          </span>

          <RecentViews
            key={pathname + search}
            nodeName={nodeDetailData?.name}
            podName={podDetailData?.name}
            namespaceName={namespaceDetailData?.name}
            clusterName={clusterDetailData?.name}
          />
        </Row>
      }
      width='90%'
    >
      {isNodeDetailLoading ? (
        <Loading />
      ) : (
        nodeDetailData && (
          <div className={'flex flex-col h-full'}>
            <Descriptions
              styles={{ label: { fontWeight: 'bold', padding: '12px' } }}
              className='mb-8'
              size='small'
              bordered
              column={3}
              items={items}
            />

            {explorerFilter && (
              <>
                <KubeVisionExplorerFilterBar
                  instanceId={instanceId}
                  filter={explorerFilter}
                  onFilterChange={(filter) =>
                    setExplorerFilter(mergeFilter(explorerFilter, filter))
                  }
                />

                <div className={'grow overflow-auto'} ref={explorerRef}>
                  <KubeVisionExplorerTable
                    instanceId={instanceId}
                    filter={explorerFilter}
                    onFilterChange={(filter) =>
                      setExplorerFilter(mergeFilter(explorerFilter, filter))
                    }
                    onClickResource={(record) => {
                      setSearchParams(
                        {
                          tab: 'kubevision',
                          dashboard: DashboardType.Explorer,
                          resourceId: record.uid,
                          resourceInstance: enabledClustersInfo.instanceName(record.instanceId),
                          resourceCluster: enabledClustersInfo.clusterName(record.clusterId)
                        },
                        { overwrite: true }
                      );
                    }}
                  />
                </div>
              </>
            )}
          </div>
        )
      )}

      {isPodDetailLoading ? (
        <Loading />
      ) : (
        podDetailData && (
          <div className={'flex flex-col h-full'}>
            <Descriptions
              styles={{ label: { fontWeight: 'bold', padding: '12px' } }}
              className='mb-8'
              size='small'
              bordered
              column={3}
              items={items}
            />

            {containerFilter && (
              <div className={'grow overflow-auto'}>
                <KubeVisionContainerTable
                  instanceId={instanceId}
                  filter={containerFilter}
                  onFilterChange={(filter) =>
                    setContainerFilter(mergeFilter(containerFilter, filter))
                  }
                  noInteractive={true}
                  onClick={(container) =>
                    setSearchParams(
                      {
                        tab: 'kubevision',
                        dashboard: DashboardType.Containers,
                        containerId: container.id,
                        containerInstanceId: container.instanceId,
                        containerClusterId: container.clusterId
                      },
                      { overwrite: true }
                    )
                  }
                />
              </div>
            )}
          </div>
        )
      )}

      {isNamespaceDetailLoading ? (
        <Loading />
      ) : (
        namespaceDetailData && (
          <div className={'flex flex-col h-full'}>
            <Descriptions
              styles={{ label: { fontWeight: 'bold', padding: '12px' } }}
              className='mb-8'
              size='small'
              bordered
              column={3}
              items={items}
            />
            {explorerFilter && (
              <div className={'grow overflow-auto'} ref={explorerRef}>
                <KubeVisionExplorerTable
                  instanceId={instanceId}
                  filter={explorerFilter}
                  onFilterChange={(filter) =>
                    setExplorerFilter(mergeFilter(explorerFilter, filter))
                  }
                  onClickResource={(record) => {
                    setSearchParams(
                      {
                        tab: 'kubevision',
                        dashboard: DashboardType.Explorer,
                        resourceId: record.uid,
                        resourceInstance: enabledClustersInfo.instanceName(record.instanceId),
                        resourceCluster: enabledClustersInfo.clusterName(record.clusterId)
                      },
                      { overwrite: true }
                    );
                  }}
                />
              </div>
            )}
          </div>
        )
      )}

      {isClusterDetailLoading ? (
        <Loading />
      ) : (
        clusterDetailData && (
          <div className={'grow overflow-auto'} ref={explorerRef}>
            <ClusterUsage clusterDetailData={clusterDetailData} />
            {explorerFilter && (
              <div className={'grow overflow-auto'} ref={explorerRef}>
                <Flex className='mb-2'>
                  <b>Nodes - Total: {clusterDetailData.nodeCount}</b>
                </Flex>
                <KubeVisionExplorerTable
                  instanceId={instanceId}
                  filter={explorerFilter}
                  onFilterChange={(filter) =>
                    setExplorerFilter(mergeFilter(explorerFilter, filter))
                  }
                  onClickResource={(record) => {
                    setSearchParams(
                      {
                        tab: 'kubevision',
                        dashboard: DashboardType.Explorer,
                        resourceId: record.uid,
                        resourceInstance: enabledClustersInfo.instanceName(record.instanceId),
                        resourceCluster: enabledClustersInfo.clusterName(record.clusterId)
                      },
                      { overwrite: true }
                    );
                  }}
                />
              </div>
            )}
          </div>
        )
      )}

      {!isNodeDetailLoading &&
        !nodeDetailData &&
        !isPodDetailLoading &&
        !podDetailData &&
        !isNamespaceDetailLoading &&
        !namespaceDetailData &&
        !isClusterDetailLoading &&
        !clusterDetailData && <ErrorNotFound />}
    </Drawer>
  );
};
