import { PlainMessage } from '@bufbuild/protobuf';
import { faSquare } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, ConfigProvider, Flex, Radio, Row, Tooltip } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import {
  KubernetesNode,
  ListKubernetesNodesResponse,
  NodeFiller,
  FillValueUnit,
  ListKubernetesPodsResponse,
  KubernetesPod,
  NodeGroupBy,
  PodFiller,
  PodGroupBy,
  ListKubernetesNamespacesDetailsResponse,
  NamespaceDetail,
  NamespaceFiller
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { formatCoreSize, humanReadableSize, useLocalStorage } from '@ui/lib/utils';

import {
  fillerNameText,
  GapSize,
  HeatmapSize,
  heatmapView,
  SizeType,
  themes,
  ThemeType
} from '../../const';
import { isClusterExpired } from '../../utils';

import { CanvasHeatmap } from './canvas-heatmap';
import { KubevisionInfrastructureCluster } from './kubevision-infrastructure-cluster';
import { InfrastructureFilter } from './kubevision-infrastructure-filter';
import { KubeVisionInfrastructureTreeMap } from './kubevision-infrastructure-treemap';

export type HeatmapData =
  | PlainMessage<ListKubernetesNodesResponse>
  | PlainMessage<ListKubernetesPodsResponse>
  | PlainMessage<ListKubernetesNamespacesDetailsResponse>;

export type HeatmapItem =
  | PlainMessage<KubernetesNode>
  | PlainMessage<KubernetesPod>
  | PlainMessage<NamespaceDetail>;

type Props = {
  heatmapData: HeatmapData;
  filter: InfrastructureFilter;
  handleSelectedItem: (item: HeatmapItem) => void;
  handleSelectedCluster: (clusterId: string, instanceId: string) => void;
};

const heatmapConfig = {
  [SizeType.Small]: {
    size: HeatmapSize.Small,
    gap: GapSize.Small
  },
  [SizeType.Medium]: {
    size: HeatmapSize.Medium,
    gap: GapSize.Medium
  },
  [SizeType.Large]: {
    size: HeatmapSize.Large,
    gap: GapSize.Large
  }
};

const isListKubernetesNodesResponse = (
  data: HeatmapData
): data is PlainMessage<ListKubernetesNodesResponse> => {
  return (data as PlainMessage<ListKubernetesNodesResponse>)?.nodes !== undefined;
};

const isListKubernetesPodsResponse = (
  data: HeatmapData
): data is PlainMessage<ListKubernetesPodsResponse> => {
  return (data as PlainMessage<ListKubernetesPodsResponse>)?.pods !== undefined;
};

const isListKubernetesNamespacesDetailsResponse = (
  data: HeatmapData
): data is PlainMessage<ListKubernetesNamespacesDetailsResponse> => {
  return (
    (data as PlainMessage<ListKubernetesNamespacesDetailsResponse>)?.namespacesDetails !== undefined
  );
};

export const getNodeFillerName = (filler: NodeFiller) => {
  switch (filler) {
    case NodeFiller.USAGE_MEMORY:
      return fillerNameText.MemoryUtilization;
    case NodeFiller.ALLOCATED_PODS:
      return fillerNameText.PodCount;
    case NodeFiller.USAGE_PODS:
      return fillerNameText.PodUtilization;
    case NodeFiller.USAGE_CPU:
    default:
      return fillerNameText.CPUUtilization;
  }
};

export const getPodFillerName = (filler: PodFiller) => {
  switch (filler) {
    case PodFiller.USAGE_MEMORY:
      return fillerNameText.MemoryUsage;
    case PodFiller.STATUS:
      return fillerNameText.Phase;
    case PodFiller.USAGE_CPU:
    default:
      return fillerNameText.CPUUsage;
  }
};

export const getNamespaceFillerName = (filler: NamespaceFiller) => {
  switch (filler) {
    case NamespaceFiller.USAGE_CPU:
      return fillerNameText.CPUUsage;
    case NamespaceFiller.USAGE_MEMORY:
      return fillerNameText.MemoryUsage;
    default:
      return fillerNameText.CPUUsage;
  }
};

export const KubernetesInfrastructureHeatmap = ({
  heatmapData,
  filter,
  handleSelectedItem,
  handleSelectedCluster
}: Props) => {
  const { isDarkTheme } = useAkuityIntelligenceContext();

  const isNodeView = filter.view === heatmapView.Nodes;
  const isPodView = filter.view === heatmapView.Pods;
  const isNamespaceView = filter.view === heatmapView.Namespaces;
  const [theme, setTheme] = useLocalStorage<ThemeType>('heatmapTheme', 'default');
  const [sizeType, setSizeType] = useLocalStorage<SizeType>('heatmapSizeType', SizeType.Medium);
  const maxFillValue = useMemo(() => {
    if (
      isNamespaceView &&
      (filter.namespaceFiller === NamespaceFiller.USAGE_TO_REQUEST_CPU ||
        filter.namespaceFiller === NamespaceFiller.USAGE_TO_REQUEST_MEMORY)
    ) {
      return 100;
    }
    return heatmapData?.maxFillValue;
  }, [heatmapData, isNamespaceView, filter.namespaceFiller]);

  const heatmapRef = useRef<HTMLDivElement>(null);

  const [containerWidth, setContainerWidth] = useState(0);

  useEffect(() => {
    const updateContainerWidth = () => {
      if (heatmapRef.current) {
        setContainerWidth(heatmapRef.current.clientWidth);
      }
    };

    updateContainerWidth();
    window.addEventListener('resize', updateContainerWidth);

    return () => {
      window.removeEventListener('resize', updateContainerWidth);
    };
  }, [heatmapRef.current]);

  const { size: heatmapSize, gap: heatmapGap } = heatmapConfig[sizeType];

  const midYellow = [254, 220, 127];
  const highlightColor = theme === 'default' ? [177, 42, 144] : midYellow;
  const disableGrey = [238, 238, 238];

  const { startColor, midColor, endColor } = themes[theme];

  const inactiveTheme = theme === 'default' ? 'alternate' : 'default';
  const inactiveStartColor = themes[inactiveTheme].startColor;
  const inactiveMiddleColor = themes[inactiveTheme].midColor;
  const inactiveEndColor = themes[inactiveTheme].endColor;

  const handleSizeChange = useCallback(
    (e: CheckboxChangeEvent) => {
      const size = e.target.value;
      setSizeType(size);
    },
    [setSizeType]
  );

  const handleThemeChange = useCallback(() => {
    setTheme(inactiveTheme);
  }, [inactiveTheme, setTheme]);

  const calculateColor = useCallback(
    (fillValue?: number, isHighlighted?: boolean) => {
      if (isHighlighted) {
        return `rgb(${highlightColor.join(', ')})`;
      }

      if (fillValue === undefined) {
        return `rgb(${disableGrey.join(', ')})`;
      }

      // Original color calculation logic
      const ratio = fillValue > maxFillValue ? 1 : fillValue / (maxFillValue ?? 1);
      const r =
        ratio < 0.5
          ? Math.round(startColor[0] + ratio * 2 * (midColor[0] - startColor[0]))
          : Math.round(midColor[0] + (ratio - 0.5) * 2 * (endColor[0] - midColor[0]));
      const g =
        ratio < 0.5
          ? Math.round(startColor[1] + ratio * 2 * (midColor[1] - startColor[1]))
          : Math.round(midColor[1] + (ratio - 0.5) * 2 * (endColor[1] - midColor[1]));
      const b =
        ratio < 0.5
          ? Math.round(startColor[2] + ratio * 2 * (midColor[2] - startColor[2]))
          : Math.round(midColor[2] + (ratio - 0.5) * 2 * (endColor[2] - midColor[2]));

      return `rgb(${r}, ${g}, ${b})`;
    },
    [maxFillValue, startColor, midColor, endColor, highlightColor, disableGrey]
  );

  const handleClickItem = useCallback(
    (item: HeatmapItem) => {
      handleSelectedItem(item);
    },
    [handleSelectedItem]
  );

  const formatValue = (value: number | undefined, unit: FillValueUnit) => {
    if (isNodeView && unit === FillValueUnit.PERCENTAGE) {
      if (value === 0 || value === 100) {
        return value;
      }
      return value?.toFixed(2);
    }
    if (isPodView && filter.podFiller === PodFiller.USAGE_CPU) {
      return formatCoreSize({ size: value, hasUnit: true });
    }
    if (isPodView && filter.podFiller === PodFiller.USAGE_MEMORY) {
      return humanReadableSize(value);
    }
    if (isPodView && filter.podFiller === PodFiller.STATUS) {
      switch (value) {
        case 0:
          return 'Running';
        case 20:
          return 'Succeeded';
        case 50:
          return 'Pending';
        case 100:
          return 'Failed';
        default:
          return 'Unknown';
      }
    }
    if (isNamespaceView && filter.namespaceFiller === NamespaceFiller.USAGE_CPU) {
      return formatCoreSize({ size: value ?? 0, hasUnit: true });
    }
    if (isNamespaceView && filter.namespaceFiller === NamespaceFiller.USAGE_MEMORY) {
      return humanReadableSize(value ?? 0);
    }
    if (isNamespaceView && unit === FillValueUnit.PERCENTAGE) {
      return value?.toFixed(2);
    }
    return value;
  };

  const HeatmapGroup = ({ groups }: { groups: HeatmapItem[] }) => {
    return (
      <CanvasHeatmap
        groups={groups}
        calculateColor={calculateColor}
        heatmapSize={heatmapSize}
        heatmapGap={heatmapGap}
        handleClickItem={handleClickItem}
        isNodeView={isNodeView}
        isPodView={isPodView}
        filter={filter}
        formatValue={formatValue}
        heatmapData={heatmapData}
        getFillValueUnit={getFillValueUnit}
        getNodeFillerName={getNodeFillerName}
        getPodFillerName={getPodFillerName}
        containerWidth={containerWidth}
      />
    );
  };

  const getGrids = (heatmapData: HeatmapData): HeatmapItem[] | undefined => {
    if (isNodeView && isListKubernetesNodesResponse(heatmapData)) {
      return heatmapData.nodes as PlainMessage<KubernetesNode>[];
    } else if (isPodView && isListKubernetesPodsResponse(heatmapData)) {
      return heatmapData.pods as PlainMessage<KubernetesPod>[];
    } else if (isNamespaceView && isListKubernetesNamespacesDetailsResponse(heatmapData)) {
      return heatmapData.namespacesDetails as PlainMessage<NamespaceDetail>[];
    }
    return undefined;
  };

  const gridsData = useMemo(() => getGrids(heatmapData), [heatmapData]);

  const instanceGroupedGrids = useMemo(() => {
    if (!gridsData) return {};

    return gridsData.reduce(
      (acc: { [key: string]: { [key: string]: HeatmapItem[] } }, grid: HeatmapItem) => {
        const instanceId = grid.instanceId;
        grid.groups.forEach((group) => {
          if (!acc[instanceId]) {
            acc[instanceId] = {};
          }
          if (!acc[instanceId][group]) {
            acc[instanceId][group] = [];
          }
          acc[instanceId][group].push(grid);
        });
        return acc;
      },
      {}
    );
  }, [gridsData]);

  const HeatmapGrid = ({ grids }: { grids: HeatmapItem[] }) => {
    const { organizationMode, enabledClustersInfo } = useAkuityIntelligenceContext();

    if (grids.every((grid) => grid.groups.toString() === '')) {
      grids.forEach((grid) => {
        grid.groups = ['All'];
      });
    } else {
      grids.forEach((grid) => {
        if (grid.groups.toString() === '') {
          grid.groups = ['Others'];
        }
      });
    }

    return (
      <div ref={heatmapRef}>
        {Object.entries(instanceGroupedGrids).map(([instanceId, groupedGrids]) => (
          <div
            key={instanceId}
            className={organizationMode ? 'mb-4 bg-[#fafafa] rounded-lg p-4 pb-16' : ''}
          >
            {organizationMode && (
              <div className='text-lg' style={{ marginBottom: '-10px' }}>
                <span className='font-bold'>{enabledClustersInfo.instanceName(instanceId)}</span>
              </div>
            )}
            <Flex wrap={true} justify='start'>
              {Object.keys(groupedGrids).map((groupKey) => {
                const cluster = enabledClustersInfo
                  .clusters({ instanceId })
                  .find((cluster) => cluster.clusterName === groupKey);

                return (
                  <KubevisionInfrastructureCluster
                    key={groupKey}
                    clusterName={groupKey}
                    itemCount={groupedGrids[groupKey]?.length}
                    itemType={isNodeView ? 'node' : isPodView ? 'pod' : ''}
                    isDarkTheme={isDarkTheme}
                    isPodView={isPodView}
                    isMetricServerUnavailable={
                      (filter.groupBy === NodeGroupBy.CLUSTER ||
                        filter.groupBy === PodGroupBy.CLUSTER) &&
                      cluster?.metricServerUnavailable
                    }
                    isDegraded={cluster?.isDegraded}
                    isExpired={isClusterExpired(cluster?.lastConnectTime)}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectedCluster(
                        enabledClustersInfo.clusterId({
                          instanceId,
                          clusterName: groupKey
                        }),
                        instanceId
                      );
                    }}
                  >
                    <HeatmapGroup groups={groupedGrids[groupKey]} />
                  </KubevisionInfrastructureCluster>
                );
              })}
            </Flex>
          </div>
        ))}
      </div>
    );
  };

  const getFillValueUnit = (fillValueUnit: FillValueUnit) => {
    switch (fillValueUnit) {
      case FillValueUnit.PERCENTAGE:
        return isNodeView || isNamespaceView ? '%' : '';
      case FillValueUnit.COUNT:
      default:
        return '';
    }
  };

  const HeatmapFloatTool = () => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'RUNNING':
          return `rgb(${startColor.join(', ')})`;
        case 'PENDING':
          return `rgb(${midColor.join(', ')})`;
        case 'SUCCEEDED':
          return `rgb(${startColor.map((c, i) => Math.round((c + midColor[i]) / 2)).join(', ')})`;
        case 'FAILED':
          return `rgb(${endColor.join(', ')})`;
        case 'UNSPECIFIED':
        default:
          return 'grey';
      }
    };

    const statusBars = [
      { status: 'RUNNING', label: 'Running' },
      { status: 'SUCCEEDED', label: 'Succeeded' },
      { status: 'PENDING', label: 'Pending' },
      { status: 'FAILED', label: 'Failed' },
      { status: 'UNSPECIFIED', label: 'Unknown' }
    ];

    return (
      <Flex
        className={`fixed bottom-6 right-6 mt-5 px-4 py-5 rounded-lg shadow-xl ${isDarkTheme ? 'bg-[#141414]' : 'bg-[#fafafa]'}`}
        vertical={true}
        justify='end'
        align='middle'
      >
        <Flex className='pb-3 items-center border-b border-[#edeef2]' justify='center'>
          {isPodView && filter.podFiller === PodFiller.STATUS ? (
            <>
              <div className='h-[1.3rem] flex'>
                {statusBars.map(({ status, label }) => (
                  <Tooltip key={status} title={label}>
                    <Flex
                      align='middle'
                      className='h-full flex-1 rounded-sm'
                      style={{
                        backgroundColor: getStatusColor(status),
                        marginRight: status !== 'UNSPECIFIED' ? '3px' : '0'
                      }}
                    >
                      <span
                        className='px-1 text-white text-sm'
                        style={{
                          textShadow: `rgba(0, 0, 0, 0.6) 1px 1px 4px`
                        }}
                      >
                        {label}
                      </span>
                    </Flex>
                  </Tooltip>
                ))}
              </div>
            </>
          ) : (
            <Row className='w-full' align='middle' justify='center'>
              <strong className='mr-3'>
                {`${formatValue(heatmapData?.minFillValue, heatmapData?.fillValueUnit)}
            ${getFillValueUnit(heatmapData?.fillValueUnit)}`}
              </strong>
              <Flex
                align='middle'
                justify='center'
                className='flex-grow h-[1.2rem] rounded-xl'
                style={{
                  background: `linear-gradient(to right, rgb(${startColor.join(
                    ', '
                  )}), rgb(${midColor.join(', ')}), rgb(${endColor.join(', ')}))`
                }}
              >
                <strong
                  className='px-6 pt-[2px] text-white text-xs opacity-95'
                  style={{
                    textShadow: `rgba(0, 0, 0, 0.6) 1px 1px 4px`
                  }}
                >
                  {isNodeView && getNodeFillerName(filter?.nodeFiller)}
                  {isPodView && getPodFillerName(filter?.podFiller)}
                </strong>
              </Flex>
              <strong className='ml-3'>
                {`${formatValue(maxFillValue, heatmapData?.fillValueUnit)}
            ${getFillValueUnit(heatmapData?.fillValueUnit)}`}
              </strong>
            </Row>
          )}
        </Flex>

        <Flex className='items-center mt-3' justify='start'>
          <strong className='mr-2'>Size:</strong>
          <ConfigProvider
            theme={{
              components: {
                Radio: {
                  colorPrimary: '',
                  colorPrimaryHover: '',
                  colorPrimaryActive: '',
                  buttonColor: `rgb(${startColor.join(', ')})`,
                  buttonCheckedBg: `rgb(${startColor.join(', ')})`,
                  colorBorder: `rgb(${startColor.join(', ')})`,
                  paddingXS: 12
                }
              }
            }}
          >
            <Radio.Group
              size='small'
              buttonStyle='solid'
              onChange={handleSizeChange}
              value={
                heatmapSize === HeatmapSize.Small
                  ? SizeType.Small
                  : heatmapSize === HeatmapSize.Large
                    ? SizeType.Large
                    : SizeType.Medium
              }
            >
              <Tooltip title='Small Chart'>
                <Radio.Button value={SizeType.Small}>
                  <FontAwesomeIcon icon={faSquare} size='2xs' />
                </Radio.Button>
              </Tooltip>
              <Tooltip title='Medium Chart'>
                <Radio.Button value={SizeType.Medium}>
                  <FontAwesomeIcon icon={faSquare} size='sm' />
                </Radio.Button>
              </Tooltip>
              <Tooltip title='Large Chart'>
                <Radio.Button value={SizeType.Large}>
                  <FontAwesomeIcon icon={faSquare} size='lg' />
                </Radio.Button>
              </Tooltip>
            </Radio.Group>
          </ConfigProvider>

          <strong className='ml-5 mr-2'>Toggle Theme:</strong>
          <Tooltip title='Toggle Theme'>
            <Button
              title='Toggle Theme'
              className='p-0 w-[2.1rem] h-[1.4rem] gap-0 overflow-hidden !rounded-[5px] border-white'
              onClick={handleThemeChange}
            >
              <span
                className='w-[0.7rem] h-full'
                style={{ background: `rgb(${inactiveStartColor.join(', ')})` }}
              />
              <span
                className='w-[0.7rem] h-full'
                style={{ background: `rgb(${inactiveMiddleColor.join(', ')})` }}
              />
              <span
                className='w-[0.7rem] h-full'
                style={{ background: `rgb(${inactiveEndColor.join(', ')})` }}
              />
            </Button>
          </Tooltip>
        </Flex>
      </Flex>
    );
  };

  return (
    <>
      {(isNodeView || isPodView) && gridsData && gridsData.length > 0 && (
        <HeatmapGrid grids={gridsData} />
      )}
      {isNamespaceView && gridsData && gridsData.length > 0 && (
        <KubeVisionInfrastructureTreeMap
          namespaceDetails={gridsData as NamespaceDetail[]}
          fillValueUnit={heatmapData?.fillValueUnit}
          formatValue={formatValue}
          filter={filter}
          calculateColor={calculateColor}
          sizeType={sizeType}
          handleSelectedItem={handleSelectedItem}
          handleSelectedCluster={handleSelectedCluster}
        />
      )}
      <HeatmapFloatTool />
    </>
  );
};
