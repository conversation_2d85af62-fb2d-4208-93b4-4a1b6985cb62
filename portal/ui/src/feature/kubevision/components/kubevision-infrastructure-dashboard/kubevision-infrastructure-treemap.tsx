import { Flex } from 'antd';
import { ChartData } from 'chart.js';
import { TreemapDataPoint } from 'chartjs-chart-treemap';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Chart } from 'react-chartjs-2';

import {
  FillValueUnit,
  NamespaceDetail,
  NamespaceFiller,
  NamespaceGroupBy
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { formatCoreSize, humanReadableSize } from '@ui/lib/utils';

import { SizeType } from '../../const';
import { isClusterExpired } from '../../utils';
import { RankChart } from '../shared/rank-chart';

import { KubevisionInfrastructureCluster } from './kubevision-infrastructure-cluster';
import { InfrastructureFilter } from './kubevision-infrastructure-filter';

type ClusterGroupedNamespaceDetail = {
  clusterName: string;
  fillValue: number;
  namespaces: NamespaceDetail[];
};

type InstanceGroupedNamespaceDetail = {
  instanceName: string;
  clusterGroups: ClusterGroupedNamespaceDetail[];
};

type KubeVisionInfrastructureTreeMapProps = {
  namespaceDetails: NamespaceDetail[];
  fillValueUnit: FillValueUnit;
  formatValue: (fillValue: number | undefined, fillValueUnit: FillValueUnit) => string | number;
  filter: InfrastructureFilter;
  calculateColor: (fillValue?: number, isHighlighted?: boolean) => string;
  sizeType: SizeType;
  handleSelectedItem: (item: NamespaceDetail) => void;
  handleSelectedCluster: (clusterId: string, instanceId: string) => void;
};

export const KubeVisionInfrastructureTreeMap = ({
  namespaceDetails,
  fillValueUnit,
  formatValue,
  filter,
  calculateColor,
  sizeType,
  handleSelectedItem,
  handleSelectedCluster
}: KubeVisionInfrastructureTreeMapProps) => {
  const treemapRef = useRef<HTMLDivElement>(null);
  const { organizationMode, enabledClustersInfo, isDarkTheme } = useAkuityIntelligenceContext();
  const [maxWidth, setMaxWidth] = useState(0);
  const [minWidth, setMinWidth] = useState(0);

  const isUsageToRequest =
    filter.namespaceFiller === NamespaceFiller.USAGE_TO_REQUEST_CPU ||
    filter.namespaceFiller === NamespaceFiller.USAGE_TO_REQUEST_MEMORY;

  useEffect(() => {
    const adjustWidth = () => {
      if (treemapRef.current) {
        const divWidth = treemapRef.current.clientWidth;
        if (sizeType === SizeType.Small) {
          setMaxWidth(divWidth / 2);
          setMinWidth(divWidth / 4);
        } else if (sizeType === SizeType.Medium) {
          setMaxWidth(divWidth / 1.6);
          setMinWidth(divWidth / 3.2);
        } else if (sizeType === SizeType.Large) {
          setMaxWidth(divWidth / 1.2);
          setMinWidth(divWidth / 2.4);
        }
      }
    };
    adjustWidth();
    window.addEventListener('resize', adjustWidth);
    return () => {
      window.removeEventListener('resize', adjustWidth);
    };
  }, [treemapRef.current, sizeType]);

  const label = useMemo(() => {
    switch (filter.namespaceFiller) {
      case NamespaceFiller.USAGE_CPU:
        return 'CPU Usage';
      case NamespaceFiller.USAGE_MEMORY:
        return 'Memory Usage';
      case NamespaceFiller.USAGE_TO_REQUEST_CPU:
        return 'CPU Usage / Request';
      case NamespaceFiller.USAGE_TO_REQUEST_MEMORY:
        return 'Memory Usage / Request';
      default:
        return 'Unknown';
    }
  }, [filter.namespaceFiller]);

  const namespaceData = useMemo(() => {
    const data: InstanceGroupedNamespaceDetail[] = [];
    const groupedData = namespaceDetails.reduce(
      (acc, detail) => {
        if (!acc[detail.instanceId]) {
          acc[detail.instanceId] = {};
        }
        if (!acc[detail.instanceId][detail.clusterId]) {
          acc[detail.instanceId][detail.clusterId] = [];
        }
        // In isUsageToRequest view, we only want to show the namespaces that match the search query
        if (isUsageToRequest && filter.search && !detail.name.includes(filter.search)) {
          return acc;
        }
        acc[detail.instanceId][detail.clusterId].push(detail);
        return acc;
      },
      {} as Record<string, Record<string, NamespaceDetail[]>>
    );
    for (const instanceId in groupedData) {
      const instanceGroup: InstanceGroupedNamespaceDetail = {
        instanceName: enabledClustersInfo.instanceName(instanceId),
        clusterGroups: []
      };
      for (const clusterId in groupedData[instanceId]) {
        const clusterGroup: ClusterGroupedNamespaceDetail = {
          clusterName: enabledClustersInfo.clusterName(clusterId),
          fillValue: groupedData[instanceId][clusterId].reduce(
            (acc, namespace) => acc + (namespace.fillValue ?? 0),
            0
          ),
          namespaces: groupedData[instanceId][clusterId]
        };
        clusterGroup.namespaces.sort((a, b) => (b.fillValue ?? 0) - (a.fillValue ?? 0));
        instanceGroup.clusterGroups.push(clusterGroup);
      }
      instanceGroup.clusterGroups.sort((a, b) => a.clusterName.localeCompare(b.clusterName));
      data.push(instanceGroup);
    }
    data.sort((a, b) => a.instanceName.localeCompare(b.instanceName));
    return data;
  }, [namespaceDetails, enabledClustersInfo, isUsageToRequest, filter.search]);

  const clusterGroupsWidth = useMemo(() => {
    const clusterFillValues = namespaceData.reduce(
      (acc, instanceGroup) => {
        instanceGroup.clusterGroups.forEach((clusterGroup) => {
          acc[`${instanceGroup.instanceName}-${clusterGroup.clusterName}`] =
            clusterGroup.namespaces.reduce((acc, namespace) => {
              return acc + (namespace.fillValue ?? 0);
            }, 0);
        });
        return acc;
      },
      {} as Record<string, number>
    );

    const maxFillValue = Math.max(...Object.values(clusterFillValues));
    const minFillValue = Math.min(...Object.values(clusterFillValues));

    const widthScale =
      maxFillValue === minFillValue
        ? () => maxWidth
        : (value: number) => {
            return (
              minWidth +
              ((value - minFillValue) / (maxFillValue - minFillValue)) * (maxWidth - minWidth)
            );
          };

    return Object.fromEntries(
      Object.entries(clusterFillValues).map(([clusterName, fillValue]) => [
        clusterName,
        isUsageToRequest ? minWidth : widthScale(fillValue)
      ])
    );
  }, [namespaceData, maxWidth, minWidth, isUsageToRequest, filter.search]);

  const chartData = useCallback(
    (namespaces: NamespaceDetail[]) => {
      return {
        datasets: [
          {
            label: label,
            tree: namespaces.map((namespace) => namespace.fillValue ?? 0),
            spacing: 0.7,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            backgroundColor: (ctx: any) => {
              if (ctx.type !== 'data') {
                return 'transparent';
              }
              return calculateColor(
                ctx.raw.v,
                filter.search && namespaces[ctx.dataIndex].name.includes(filter.search)
              );
            },
            borderRadius: 2
          }
        ]
      } as unknown as ChartData<'treemap', TreemapDataPoint[]>;
    },
    [calculateColor, label, filter.search]
  );

  const chartOptions = useCallback(
    (namespaces: NamespaceDetail[]) => {
      return {
        animation: {
          duration: 0
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              label: (ctx: any) => {
                const value = formatValue(ctx.raw.v, fillValueUnit);
                return `${namespaces[ctx.dataIndex].name}: ${value}${isUsageToRequest ? '%' : ''}`;
              }
            }
          }
        },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onClick: (ctx: any, elements: any) => {
          const dataIdx = elements?.[0]?.index;
          if (dataIdx !== undefined && namespaces[dataIdx]) {
            handleSelectedItem(namespaces[dataIdx]);
          }
        }
      };
    },
    [label, namespaceDetails]
  );

  const typeValue = useCallback(
    (namespace: NamespaceDetail, isUsage: boolean) => {
      switch (filter.namespaceFiller) {
        case NamespaceFiller.USAGE_TO_REQUEST_CPU: {
          const cpuValue = isUsage ? namespace.usageCpu : namespace.requestCpu;
          return cpuValue == undefined || cpuValue == null
            ? 'N/A'
            : formatCoreSize({ size: cpuValue, hasUnit: true });
        }
        case NamespaceFiller.USAGE_TO_REQUEST_MEMORY: {
          const memoryValue = isUsage ? namespace.usageMemory : namespace.requestMemory;
          return memoryValue == undefined || memoryValue == null
            ? 'N/A'
            : humanReadableSize(memoryValue);
        }
      }
    },
    [filter.namespaceFiller]
  );

  return (
    <div ref={treemapRef}>
      {namespaceData.map(({ instanceName, clusterGroups }) => (
        <div
          key={instanceName}
          className={organizationMode ? 'mb-4 bg-[#fafafa] rounded-lg p-4' : ''}
        >
          {organizationMode && (
            <div className='text-lg' style={{ marginBottom: '-10px' }}>
              <span className='font-bold'>{instanceName}</span>
            </div>
          )}
          <Flex wrap={true} justify={'start'}>
            {clusterGroups.map(({ clusterName, fillValue, namespaces }) => {
              const cluster = enabledClustersInfo
                .clusters({ instanceName })
                .find((cluster) => cluster.clusterName === clusterName);

              return (
                <KubevisionInfrastructureCluster
                  key={clusterName}
                  clusterName={clusterName}
                  itemCount={namespaces?.length}
                  itemType='namespace'
                  isDarkTheme={isDarkTheme}
                  isMetricServerUnavailable={
                    filter.groupBy === NamespaceGroupBy.CLUSTER && cluster?.metricServerUnavailable
                  }
                  isDegraded={cluster?.isDegraded}
                  isExpired={isClusterExpired(cluster?.lastConnectTime)}
                  fillValue={isUsageToRequest ? '' : `${formatValue(fillValue, fillValueUnit)}`}
                  customStyle={{ width: clusterGroupsWidth[`${instanceName}-${clusterName}`] }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSelectedCluster(
                      enabledClustersInfo.clusterId({
                        instanceName,
                        clusterName
                      }),
                      enabledClustersInfo.instanceId(instanceName)
                    );
                  }}
                >
                  {(filter.namespaceFiller === NamespaceFiller.USAGE_CPU ||
                    filter.namespaceFiller === NamespaceFiller.USAGE_MEMORY) && (
                    <Chart
                      type='treemap'
                      data={chartData(namespaces)}
                      options={chartOptions(namespaces)}
                    />
                  )}
                  {(filter.namespaceFiller === NamespaceFiller.USAGE_TO_REQUEST_CPU ||
                    filter.namespaceFiller === NamespaceFiller.USAGE_TO_REQUEST_MEMORY) &&
                    (namespaces.length > 0 ? (
                      <RankChart
                        fillValueUnit={fillValueUnit}
                        calculateColor={calculateColor}
                        items={namespaces.map((namespace) => ({
                          name: namespace.name,
                          value: namespace.fillValue,
                          onHover: () => (
                            <div className='flex flex-col gap-1 min-w-[120px] items-center justify-center'>
                              <div className='mb-1 text-bold w-full text-nowrap whitespace-nowrap'>
                                {namespace.name}
                              </div>
                              <div className='text-xs w-full'>
                                <div className='flex flex-row w-full'>
                                  <div className='w-2/5'>Usage</div>
                                  <div className='w-3/5 text-right text-nowrap whitespace-nowrap'>
                                    {typeValue(namespace, true)}
                                  </div>
                                </div>
                              </div>
                              <div className='text-xs w-full'>
                                <div className='flex flex-row w-full'>
                                  <div className='w-2/5'>Request</div>
                                  <div className='w-3/5 text-right text-nowrap whitespace-nowrap'>
                                    {typeValue(namespace, false)}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )
                        }))}
                        formatValue={formatValue}
                        showValue={true}
                        onClick={(item) => {
                          const clusterId = enabledClustersInfo.clusterId({
                            instanceName,
                            clusterName
                          });
                          handleSelectedItem(
                            namespaceDetails.find(
                              (detail) =>
                                detail.clusterId === clusterId && detail.name === item.name
                            )
                          );
                        }}
                      />
                    ) : (
                      <div className='w-full min-h-[120px] flex flex-grow justify-center items-center text-gray-500'>
                        No data
                      </div>
                    ))}
                </KubevisionInfrastructureCluster>
              );
            })}
          </Flex>
        </div>
      ))}
    </div>
  );
};
