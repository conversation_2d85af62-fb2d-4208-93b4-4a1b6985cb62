import { PlainMessage } from '@bufbuild/protobuf';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faBox, faInfoCircle, faMicrochip, faMemory } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Card, ConfigProvider, Flex, Progress, Statistic, Tooltip } from 'antd';

import { GetKubernetesClusterDetailResponse } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { formatCoreSize, humanReadableSize } from '@ui/lib/utils';
import { useLocalStorage } from '@ui/lib/utils';

import { themes, ThemeType } from '../../const';

interface ClusterUsageProps {
  clusterDetailData: PlainMessage<GetKubernetesClusterDetailResponse>;
}

interface ProgressDisplayProps {
  percent: number;
  value: string;
  label: string;
  tooltip: string;
}

const ProgressDisplay = ({ percent, value, label, tooltip }: ProgressDisplayProps) => {
  const [theme] = useLocalStorage<ThemeType>('heatmapTheme', 'default');
  const { isDarkTheme } = useAkuityIntelligenceContext();
  const { startColor, midColor, endColor } = themes[theme];

  const getColorByPercent = (percent: number) => {
    const ratio = percent / 100;
    if (ratio < 0.5) {
      // between startColor and midColor
      const r = Math.round(startColor[0] + ratio * 2 * (midColor[0] - startColor[0]));
      const g = Math.round(startColor[1] + ratio * 2 * (midColor[1] - startColor[1]));
      const b = Math.round(startColor[2] + ratio * 2 * (midColor[2] - startColor[2]));
      return `rgb(${r}, ${g}, ${b})`;
    } else {
      // between midColor and endColor
      const r = Math.round(midColor[0] + (ratio - 0.5) * 2 * (endColor[0] - midColor[0]));
      const g = Math.round(midColor[1] + (ratio - 0.5) * 2 * (endColor[1] - midColor[1]));
      const b = Math.round(midColor[2] + (ratio - 0.5) * 2 * (endColor[2] - midColor[2]));
      return `rgb(${r}, ${g}, ${b})`;
    }
  };

  return (
    <Flex vertical={true} justify='center' align='center' className='flex-1 min-w-[200px]'>
      <ConfigProvider
        theme={{
          components: {
            Progress: {
              circleTextColor: getColorByPercent(percent),
              circleTextFontSize: '1.4rem'
            }
          }
        }}
      >
        <Tooltip
          title={() => (
            <div className='text-center'>
              <div>
                {label}: {value}
              </div>
              <div>Utilization: {Number(percent.toFixed(2))}%</div>
            </div>
          )}
          placement='top'
        >
          <Progress
            type='dashboard'
            percent={Number(percent.toFixed(2))}
            strokeColor={{
              '0%': `rgb(${startColor.join(', ')})`,
              '50%': `rgb(${midColor.join(', ')})`,
              '100%': `rgb(${endColor.join(', ')})`
            }}
            strokeWidth={10}
            format={(percent) => `${percent}%`}
          />
        </Tooltip>
      </ConfigProvider>

      <strong className={isDarkTheme ? 'text-[#ffffff73]' : 'text-[#00000073]'}>
        {label}
        <Tooltip title={tooltip} placement='bottom'>
          <FontAwesomeIcon icon={faInfoCircle} className='cursor-pointer ml-2' />
        </Tooltip>
      </strong>
    </Flex>
  );
};

interface PodNumDisplayProps {
  value: number;
  label: string;
  tooltip: string;
}

const PodNumDisplay = ({ value, label, tooltip }: PodNumDisplayProps) => {
  const [theme] = useLocalStorage<ThemeType>('heatmapTheme', 'default');
  const { isDarkTheme } = useAkuityIntelligenceContext();
  const themeColor = theme === 'default' ? themes[theme].startColor : themes[theme].midColor;

  return (
    <Flex vertical={true} justify='center' align='center' className='flex-1 min-w-[200px] mt-4'>
      <span className='text-[3.4rem] bolder' style={{ color: `rgb(${themeColor.join(', ')})` }}>
        {value}
      </span>
      <strong className={isDarkTheme ? 'text-[#ffffff73]' : 'text-[#00000073]'}>
        {label}
        <Tooltip title={tooltip} placement='bottom'>
          <FontAwesomeIcon icon={faInfoCircle} className='cursor-pointer ml-2' />
        </Tooltip>
      </strong>
    </Flex>
  );
};

interface CardDisplayProps {
  title: string;
  icon: IconDefinition;
  allocatable: string | number;
  children: React.ReactNode;
}

const CardDisplay = ({ title, icon, allocatable, children }: CardDisplayProps) => {
  const [theme] = useLocalStorage<ThemeType>('heatmapTheme', 'default');
  const themeColor = theme === 'default' ? themes[theme].startColor : themes[theme].midColor;

  return (
    <Card
      style={{ flex: '1 1 350px', minWidth: '300px', maxWidth: '100%', margin: '0' }}
      styles={{ body: { padding: 0, height: 0 } }}
      variant='outlined'
      title={
        <Flex className='py-2' justify='space-between' align='center'>
          <span className='text-2xl font-bold'>{title}</span>
          <Statistic
            title={`Allocatable ${title}`}
            prefix={<FontAwesomeIcon icon={icon} />}
            value={allocatable}
            valueStyle={{ fontSize: '1.2rem', color: `rgb(${themeColor.join(', ')})` }}
          />
        </Flex>
      }
      cover={<div className='px-4 py-6'>{children}</div>}
    />
  );
};

export const ClusterUsage = ({ clusterDetailData }: ClusterUsageProps) => {
  return (
    <div className='mb-8 flex flex-wrap gap-4'>
      <CardDisplay
        title='CPU'
        icon={faMicrochip}
        allocatable={formatCoreSize({
          size: clusterDetailData.cpuAllocatable,
          hasUnit: true
        })}
      >
        <Flex justify='space-around' gap='20px' wrap={true}>
          <ProgressDisplay
            percent={(clusterDetailData.cpuUsage / clusterDetailData.cpuAllocatable) * 100}
            value={formatCoreSize({ size: clusterDetailData.cpuUsage, hasUnit: true })}
            label='Usage'
            tooltip='The total CPU usage divided by allocatable CPUs'
          />
          <ProgressDisplay
            percent={(clusterDetailData.cpuRequest / clusterDetailData.cpuAllocatable) * 100}
            value={formatCoreSize({ size: clusterDetailData.cpuRequest, hasUnit: true })}
            label='Requests'
            tooltip='The total CPU requests divided by allocatable CPUs'
          />
        </Flex>
      </CardDisplay>

      <CardDisplay
        title='Memory'
        icon={faMemory}
        allocatable={humanReadableSize(clusterDetailData.memoryAllocatable)}
      >
        <Flex justify='space-around' gap='20px' wrap={true}>
          <ProgressDisplay
            percent={(clusterDetailData.memoryUsage / clusterDetailData.memoryAllocatable) * 100}
            value={humanReadableSize(clusterDetailData.memoryUsage)}
            label='Usage'
            tooltip='The total memory usage divided by allocatable memory'
          />
          <ProgressDisplay
            percent={(clusterDetailData.memoryRequest / clusterDetailData.memoryAllocatable) * 100}
            value={humanReadableSize(clusterDetailData.memoryRequest)}
            label='Requests'
            tooltip='The total memory requests divided by allocatable memory'
          />
        </Flex>
      </CardDisplay>

      <CardDisplay title='Pods' icon={faBox} allocatable={clusterDetailData.podAllocatable}>
        <Flex justify='space-around' gap='20px' wrap={true}>
          <PodNumDisplay value={clusterDetailData.podTotal} label='Total' tooltip='Total pods' />
          <PodNumDisplay
            value={clusterDetailData.podRunning}
            label='Running'
            tooltip='Running pods'
          />
        </Flex>
      </CardDisplay>
    </div>
  );
};
