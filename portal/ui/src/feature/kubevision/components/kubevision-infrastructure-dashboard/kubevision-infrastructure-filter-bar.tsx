import { faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Tooltip } from 'antd';
import { useMemo } from 'react';

import { ClusterSelect } from '@ui/feature/kubevision/components/shared/cluster-select';
import { FilterBar } from '@ui/feature/kubevision/components/shared/filter-bar';
import {
  NamespaceFiller,
  NamespaceGroupBy,
  NodeFiller,
  NodeGroupBy,
  PodFiller,
  PodGroupBy
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { heatmapView } from '../../const';

import { FillInSelect } from './fill-in-select';
import { GroupBySelect } from './group-by-select';
import {
  defaultInfrastructureFilter,
  InfrastructureFilter
} from './kubevision-infrastructure-filter';
import { SearchItem } from './search-item';
import { ViewSelect } from './view-select';

export type ViewType = 'Nodes' | 'Pods' | 'Namespaces';

type Props = {
  instanceId: string;
  filter: InfrastructureFilter;
  onFilterChange: (
    filter: Partial<InfrastructureFilter>,
    overwrite?: boolean,
    replace?: boolean
  ) => void;
};

export const KubeVisionInfrastructureFilterBar = ({
  instanceId,
  filter,
  onFilterChange
}: Props) => {
  const { enabledClustersInfo } = useAkuityIntelligenceContext();

  const isNodeView = filter.view === heatmapView.Nodes;
  const isPodView = filter.view === heatmapView.Pods;
  const isNamespaceView = filter.view === heatmapView.Namespaces;

  const isUsageToRequest =
    filter.namespaceFiller === NamespaceFiller.USAGE_TO_REQUEST_CPU ||
    filter.namespaceFiller === NamespaceFiller.USAGE_TO_REQUEST_MEMORY;

  const filler = isNodeView
    ? filter.nodeFiller
    : isPodView
      ? filter.podFiller
      : filter.namespaceFiller;

  const namespaceBadgeNode = useMemo(() => {
    return (
      <Tooltip
        styles={{ body: { width: 'fit-content' } }}
        title={
          <div className='px-2 py-3 w-[380px] whitespace-normal break-words'>
            Usage-to-request ratio represents the total container usage divided by the total
            container resource request within a namespace.
            <br />
            Only containers with the request set are included in this calculation.
            <br />
            If a namespace has no containers with the request set, it is shown as N/A.
          </div>
        }
      >
        <FontAwesomeIcon icon={faInfoCircle} className='ml-2' color='grey' />
      </Tooltip>
    );
  }, []);

  return (
    <FilterBar
      firstRow={
        <>
          <ViewSelect
            view={filter.view}
            onChange={(view) => {
              if (isNodeView) {
                onFilterChange({
                  view,
                  groupBy: NodeGroupBy.CLUSTER,
                  nodeFiller: NodeFiller.USAGE_CPU
                });
              } else if (isPodView) {
                onFilterChange({
                  view,
                  groupBy: PodGroupBy.CLUSTER,
                  podFiller: PodFiller.USAGE_CPU
                });
              } else if (isNamespaceView) {
                onFilterChange({
                  view,
                  groupBy: NamespaceGroupBy.CLUSTER,
                  namespaceFiller: NamespaceFiller.USAGE_CPU
                });
              }
            }}
          />
          <GroupBySelect
            view={filter.view}
            groupBy={filter.groupBy}
            onChange={(groupBy) => onFilterChange({ groupBy })}
          />
          <FillInSelect
            view={filter.view}
            filler={filler}
            onChange={(filler) => {
              if (isNodeView) {
                onFilterChange({ nodeFiller: filler as NodeFiller });
              } else if (isPodView) {
                onFilterChange({ podFiller: filler as PodFiller });
              } else if (isNamespaceView) {
                onFilterChange({ namespaceFiller: filler as NamespaceFiller });
              }
            }}
          />
          <ClusterSelect
            instanceId={instanceId}
            clusterId={filter.cluster}
            showAllOption={true}
            onChange={(clusterId) => {
              onFilterChange({
                cluster: enabledClustersInfo.clusterName(clusterId)
              });
            }}
          />
          {isNamespaceView && isUsageToRequest && namespaceBadgeNode}
        </>
      }
      secondRow={
        <SearchItem
          view={filter.view}
          search={filter.search}
          onApplyFilter={(search) => onFilterChange({ search })}
        />
      }
      onReset={() => onFilterChange(defaultInfrastructureFilter(), true)}
    />
  );
};
