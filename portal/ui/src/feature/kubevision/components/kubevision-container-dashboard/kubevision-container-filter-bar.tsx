import {
  ContainerFilter,
  default<PERSON>ontainer<PERSON>ilter,
  filterToListKubernetesContainersRequest
} from '@ui/feature/kubevision/components/kubevision-container-dashboard/kubevision-container-filter';
import { ClusterSelect } from '@ui/feature/kubevision/components/shared/cluster-select';
import {
  getStatusColor,
  getStatusText,
  getTypeColor,
  getTypeText
} from '@ui/feature/kubevision/components/shared/container-status-type-tag';
import { FilterBar } from '@ui/feature/kubevision/components/shared/filter-bar';
import { FilterTag } from '@ui/feature/kubevision/components/shared/filter-tag';
import { OwnerTag } from '@ui/feature/kubevision/components/shared/owner-tag';
import { usePlatformContext } from '@ui/feature/shared/context/platform-context';
import { getQueryParamsAsString } from '@ui/lib/apiclient/utils';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { InstanceSelect } from '../shared/instance-select';

type Props = {
  instanceId: string;
  filter: ContainerFilter;
  onFilterChange: (filter: Partial<ContainerFilter>, overwrite?: boolean) => void;
};

export const KubeVisionContainerFilterBar = ({ instanceId, filter, onFilterChange }: Props) => {
  const { apiPathPrefix, openUrlWithCookies } = usePlatformContext();
  const { organizationMode, organizationId, enabledClustersInfo } = useAkuityIntelligenceContext();

  const onExportToCSV = () => {
    const params = filterToListKubernetesContainersRequest(enabledClustersInfo, filter, instanceId);
    const url = `${apiPathPrefix}stream/orgs/${organizationId}/k8s/containers-csv?${getQueryParamsAsString(params)}`;
    openUrlWithCookies(url, '_blank');
  };

  const clusterId = enabledClustersInfo.clusterId({
    instanceId,
    clusterName: filter.cluster
  });

  return (
    <FilterBar
      firstRow={
        <>
          {organizationMode && (
            <InstanceSelect
              instanceId={instanceId}
              showAllOption={true}
              onChange={(instanceId) =>
                onFilterChange({
                  instance: enabledClustersInfo.instanceName(instanceId),
                  cluster: undefined,
                  offset: 0
                })
              }
            />
          )}

          <ClusterSelect
            instanceId={instanceId}
            clusterId={clusterId}
            showAllOption={true}
            onChange={(clusterId, instanceId) =>
              onFilterChange({
                instance: enabledClustersInfo.instanceName(instanceId),
                cluster: enabledClustersInfo.clusterName(clusterId),
                podId: undefined,
                offset: 0
              })
            }
          />

          <OwnerTag
            instanceId={instanceId}
            ownerId={filter.podId}
            clusterId={clusterId}
            onClear={() =>
              onFilterChange({
                podId: undefined,
                offset: 0
              })
            }
          />
        </>
      }
      secondRow={
        !!filter.nameContains || !!filter.status || !!filter.type ? (
          <>
            <FilterTag
              hidden={!filter.nameContains}
              text={
                <>
                  <strong className='mr-1'>Name contains: </strong>
                  {filter.nameContains}
                </>
              }
              onClear={() =>
                onFilterChange({
                  nameContains: undefined,
                  offset: 0
                })
              }
            />
            <FilterTag
              hidden={!getStatusText(filter.status)}
              text={
                <>
                  <strong className='mr-1'>Status: </strong>
                  {getStatusText(filter.status)}
                </>
              }
              color={getStatusColor(filter.status)}
              onClear={() =>
                onFilterChange({
                  status: undefined,
                  offset: 0
                })
              }
            />
            <FilterTag
              hidden={!getTypeText(filter.type)}
              text={
                <>
                  <strong className='mr-1'>Type: </strong>
                  {getTypeText(filter.type)}
                </>
              }
              color={getTypeColor(filter.type)}
              onClear={() =>
                onFilterChange({
                  type: undefined,
                  offset: 0
                })
              }
            />
          </>
        ) : null
      }
      onExportToCSV={onExportToCSV}
      onReset={() => onFilterChange(defaultContainerFilter(), true)}
    />
  );
};
