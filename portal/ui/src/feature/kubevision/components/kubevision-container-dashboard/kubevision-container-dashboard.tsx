import {
  clearQueryParams,
  generatePrefixedParams,
  mergeFilter
} from '@ui/feature/kubevision/filter';
import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import {
  Container,
  ContainerStatus,
  ContainerType
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { PageTitle } from '@ui/lib/components/page-title';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { TabType } from '../../const';
import { KubeVisionCheckEnabledClusters } from '../shared/kubevision-check-enabled-clusters';

import { KubernetesContainerDrawer } from './kubevision-container-drawer';
import { ContainerFilter, defaultContainerFilter } from './kubevision-container-filter';
import { KubeVisionContainerFilterBar } from './kubevision-container-filter-bar';
import { KubeVisionContainerTable } from './kubevision-container-table';

type Props = {
  instanceId: string;
};

const KubeVisionContainerDashboard = ({ instanceId }: Props) => {
  const { organizationMode, enabledClustersInfo } = useAkuityIntelligenceContext();
  const { getSearchParam, getSearchParamPagination, setSearchParams } = useCustomSearchParams();
  const initFilter: Record<string, string | number> = {};
  const baseParams = [
    'containerId',
    'containerInstance',
    'containerCluster',
    'podId',
    'instance',
    'cluster',
    'image',
    'imageTag',
    'imageDigest',
    'nameContains',
    'status',
    'type',
    'detailsTab'
  ];

  const logsParams = generatePrefixedParams(TabType.Logs, ['content']);

  const params = [...baseParams, ...logsParams];

  for (const param of params) {
    if (getSearchParam(param)) initFilter[param] = getSearchParam(param);
  }
  initFilter.status = (
    Number.parseInt(initFilter.status as string)
      ? Number.parseInt(initFilter.status as string)
      : undefined
  ) as ContainerStatus;
  initFilter.type = (
    Number.parseInt(initFilter.type as string)
      ? Number.parseInt(initFilter.type as string)
      : undefined
  ) as ContainerType;
  const { limit, offset, orderBy } = getSearchParamPagination();
  initFilter.limit = limit;
  initFilter.offset = offset;
  initFilter.orderBy = orderBy;

  const containerFilter = mergeFilter(defaultContainerFilter(), initFilter);
  const handleFilterChange = (filter: Partial<ContainerFilter>, overwrite?: boolean) => {
    const newFilter = overwrite
      ? mergeFilter(defaultContainerFilter(), filter)
      : mergeFilter(containerFilter, filter);

    let filterToUse = newFilter;

    if (newFilter.detailsTab !== TabType.Logs) {
      filterToUse = clearQueryParams(filterToUse, TabType.Logs);
    }

    const searchParams = {
      ...filterToUse,
      instance: organizationMode ? newFilter?.instance : undefined,
      status: newFilter?.status?.toString(),
      type: newFilter?.type?.toString(),
      limit: newFilter?.limit?.toString(),
      offset: newFilter?.offset?.toString(),
      orderBy: newFilter?.orderBy?.toString()
    };
    setSearchParams(
      Object.fromEntries(
        Object.entries(searchParams).map(([key, value]) => [key, value?.toString()])
      )
    );
  };

  const handleRowClick = (record: Container) => {
    handleFilterChange({
      containerId: record.id,
      containerInstance: enabledClustersInfo.instanceName(record.instanceId),
      containerCluster: enabledClustersInfo.clusterName(record.clusterId),
      detailsTab: TabType.Logs
    });
  };

  return (
    <>
      <PageTitle>Akuity Intelligence Containers</PageTitle>

      <KubernetesContainerDrawer
        instanceId={enabledClustersInfo.instanceId(containerFilter.containerInstance)}
        filter={containerFilter}
        onFilterChange={handleFilterChange}
      />
      <KubeVisionContainerFilterBar
        instanceId={instanceId}
        filter={containerFilter}
        onFilterChange={handleFilterChange}
      />
      <KubeVisionCheckEnabledClusters>
        <KubeVisionContainerTable
          instanceId={instanceId}
          filter={containerFilter}
          onFilterChange={handleFilterChange}
          onClick={handleRowClick}
        />
      </KubeVisionCheckEnabledClusters>
    </>
  );
};

export default KubeVisionContainerDashboard;
