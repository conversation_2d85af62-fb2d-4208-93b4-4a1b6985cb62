import { ConfigProvider, Flex, Table, TableColumnsType } from 'antd';

import { KubernetesButton } from '@ui/feature/kubevision/components/shared/kubevision-button';
import KubernetesTableRadioFilter from '@ui/feature/kubevision/components/shared/kubevision-table-radio-filter';
import { KubernetesTableStringFilter } from '@ui/feature/kubevision/components/shared/kubevision-table-string-filter';
import { TruncateText } from '@ui/feature/kubevision/components/shared/truncate-text';
import {
  akpTextDarkThemeColor,
  akpTextPrimaryColor,
  DashboardType
} from '@ui/feature/kubevision/const';
import { handleTableChange, parseSortOrder } from '@ui/feature/kubevision/filter';
import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import { useGetKubernetesContainersQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import {
  Container,
  ContainerStatus,
  ContainerType
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { formatCoreSize, humanReadableSize } from '@ui/lib/utils';

import { formatTime } from '../../utils';
import { ContainerStatusTag, ContainerTypeTag } from '../shared/container-status-type-tag';
import { createDetailsColumn } from '../shared/kubevision-table-details-column';

import {
  ContainerFilter,
  filterToListKubernetesContainersRequest,
  getFilterKey
} from './kubevision-container-filter';

type Props = {
  instanceId: string;
  filter: ContainerFilter;
  onFilterChange: (filter: Partial<ContainerFilter>, overwrite?: boolean) => void;
  image?: string;
  imageTag?: string;
  onClick?: (container: Container) => void;
  tableHeight?: number;
  noInteractive?: boolean;
};

export const KubeVisionContainerTable = ({
  instanceId,
  filter,
  onFilterChange,
  onClick,
  tableHeight,
  noInteractive
}: Props) => {
  const { organizationMode, isDarkTheme, enabledClustersInfo } = useAkuityIntelligenceContext();

  const { setSearchParams } = useCustomSearchParams();

  const { data: containerList, isLoading: isLoadingContainerList } =
    useGetKubernetesContainersQuery(
      filterToListKubernetesContainersRequest(enabledClustersInfo, filter, instanceId)
    );

  const updateFilter = (
    key: string,
    value: string | number | undefined,
    filter: ContainerFilter,
    onFilterChange: (filter: Partial<ContainerFilter>) => void
  ) => {
    const filterKey = getFilterKey(key, filter);
    const offsetKey = getFilterKey('offset', filter);

    onFilterChange({
      [filterKey]: value,
      [offsetKey]: 0
    });
  };

  const columns: TableColumnsType<Container> = [
    {
      title: '',
      className: '!pb-1',
      children: [
        createDetailsColumn({ onRowClick: onClick }),
        {
          key: 'instance',
          title: 'Instance',
          dataIndex: 'instanceId',
          width: 150,
          hidden: !organizationMode || !!filter.instance,
          render: (instanceId: string) => {
            const instanceName = enabledClustersInfo.instanceName(instanceId);
            return noInteractive ? (
              <TruncateText maxWidth={200}>{instanceName}</TruncateText>
            ) : (
              <KubernetesButton
                tooltip={
                  <>
                    <b className={'mr-1'}>Filter by instance:</b>
                    {instanceName}
                  </>
                }
                text={instanceName}
                maxWidth={200}
                onClick={(e) => {
                  e.stopPropagation();
                  onFilterChange({
                    instance: instanceName,
                    cluster: undefined,
                    offset: 0
                  });
                }}
              />
            );
          }
        },
        {
          key: 'cluster',
          title: 'Cluster',
          dataIndex: 'clusterId',
          width: 150,
          hidden: !!filter.cluster,
          render: (clusterId: string, record: Container) => {
            const clusterName = enabledClustersInfo.clusterName(clusterId);
            return noInteractive ? (
              <TruncateText maxWidth={200}>{clusterName}</TruncateText>
            ) : (
              <KubernetesButton
                tooltip={
                  <>
                    <b className={'mr-1'}>Filter by cluster:</b>
                    {clusterName}
                  </>
                }
                text={clusterName}
                maxWidth={200}
                onClick={(e) => {
                  e.stopPropagation();
                  onFilterChange({
                    instance: enabledClustersInfo.instanceName(record.instanceId),
                    cluster: clusterName,
                    offset: 0
                  });
                }}
              />
            );
          }
        },
        {
          key: 'name',
          title: 'Container',
          dataIndex: 'name',
          filtered: !!filter[getFilterKey('nameContains', filter) as keyof ContainerFilter],
          filterDropdown: ({ close }: { close: () => void }) => (
            <KubernetesTableStringFilter
              label='Container Name'
              defaultValue={
                filter[getFilterKey('nameContains', filter) as keyof ContainerFilter]?.toString() ??
                ''
              }
              onClearFilter={() => {
                updateFilter('nameContains', undefined, filter, onFilterChange);
                close();
              }}
              onApplyFilter={(nameLike: string) => {
                updateFilter('nameContains', nameLike, filter, onFilterChange);
                close();
              }}
            />
          ),
          render: (_: unknown, record: Container) => (
            <div>
              <div className={'mb-2'}>{record.name} </div>
              <KubernetesButton
                className={'text-xs rounded px-1 py-1'}
                tooltip={
                  <>
                    <b>Filter by Pod:</b> {record.podName}
                  </>
                }
                text={
                  <>
                    <b>Pod:</b> {record.podName}
                  </>
                }
                maxWidth={360}
                onClick={(e) => {
                  e.stopPropagation();
                  setSearchParams(
                    {
                      tab: 'kubevision',
                      dashboard: DashboardType.Containers,
                      instance: enabledClustersInfo.instanceName(record.instanceId),
                      cluster: enabledClustersInfo.clusterName(record.clusterId),
                      podId: record.podId
                    },
                    { overwrite: true }
                  );
                }}
              />
            </div>
          )
        },
        {
          key: 'status',
          title: 'Status',
          dataIndex: 'status',
          filtered: !!filter[getFilterKey('status', filter) as keyof ContainerFilter],
          filterDropdown: () => {
            return (
              <KubernetesTableRadioFilter
                label={'Status'}
                defaultValue={
                  filter[getFilterKey('status', filter) as keyof ContainerFilter]?.toString() ?? ''
                }
                options={[
                  { label: 'Running', value: '1' },
                  { label: 'Error', value: '2' },
                  { label: 'Completed', value: '3' },
                  { label: 'Pending', value: '4' }
                ]}
                onClearFilter={() => updateFilter('status', undefined, filter, onFilterChange)}
                onApplyFilter={(value: string) =>
                  updateFilter(
                    'status',
                    Number.parseInt(value) as ContainerStatus,
                    filter,
                    onFilterChange
                  )
                }
              />
            );
          },
          render: (status: ContainerStatus) => (
            <ContainerStatusTag
              status={status}
              onClick={(e) => {
                e.stopPropagation();
                onFilterChange({
                  [getFilterKey('status', filter) as keyof ContainerFilter]: status,
                  [getFilterKey('offset', filter) as keyof ContainerFilter]: 0
                });
              }}
            />
          )
        },
        {
          key: 'type',
          title: 'Type',
          dataIndex: 'type',
          filtered: !!filter[getFilterKey('type', filter) as keyof ContainerFilter],
          filterDropdown: () => {
            return (
              <KubernetesTableRadioFilter
                label={'Type'}
                defaultValue={
                  filter[getFilterKey('type', filter) as keyof ContainerFilter]?.toString() ?? ''
                }
                options={[
                  { label: 'Container', value: '1' },
                  { label: 'Init Container', value: '2' },
                  { label: 'Sidecar Container', value: '3' },
                  { label: 'Ephemeral Container', value: '4' }
                ]}
                onClearFilter={() => updateFilter('type', undefined, filter, onFilterChange)}
                onApplyFilter={(value: string) =>
                  updateFilter(
                    'type',
                    Number.parseInt(value) as ContainerType,
                    filter,
                    onFilterChange
                  )
                }
              />
            );
          },
          render: (type: ContainerType) => (
            <ContainerTypeTag
              type={type}
              onClick={(e) => {
                e.stopPropagation();
                onFilterChange({
                  [getFilterKey('type', filter) as keyof ContainerFilter]: type,
                  [getFilterKey('offset', filter) as keyof ContainerFilter]: 0
                });
              }}
            />
          )
        }
      ]
    },
    {
      title: <div className='text-xs text-gray-500'>Resource Usage</div>,
      className: '!pb-1',
      children: [
        {
          key: 'cpuUsage',
          title: 'CPU (Cores)',
          dataIndex: 'cpuUsage',
          sorter: true,
          sortOrder: parseSortOrder(
            filter[getFilterKey('orderBy', filter) as keyof ContainerFilter]?.toString() ?? '',
            'cpuUsage'
          ),
          render: (cpuUsage: number) =>
            cpuUsage ? formatCoreSize({ size: cpuUsage, hasUnit: false }) : '-'
        },
        {
          key: 'memoryUsage',
          title: 'Memory',
          dataIndex: 'memoryUsage',
          sorter: true,
          sortOrder: parseSortOrder(
            filter[getFilterKey('orderBy', filter) as keyof ContainerFilter]?.toString() ?? '',
            'memoryUsage'
          ),
          render: (memoryUsage: number) => (memoryUsage ? humanReadableSize(memoryUsage) : '-')
        }
      ]
    },
    {
      title: <div className='text-xs text-gray-500'>Resource Limits</div>,
      className: '!pb-1',
      children: [
        {
          key: 'cpuLimit',
          title: 'CPU (Cores)',
          dataIndex: 'cpuLimit',
          sorter: true,
          sortOrder: parseSortOrder(
            filter[getFilterKey('orderBy', filter) as keyof ContainerFilter]?.toString() ?? '',
            'cpuLimit'
          ),
          render: (cpuLimit: number) => (cpuLimit ? cpuLimit.toString() : '-')
        },
        {
          key: 'memoryLimit',
          title: 'Memory',
          dataIndex: 'memoryLimit',
          sorter: true,
          sortOrder: parseSortOrder(
            filter[getFilterKey('orderBy', filter) as keyof ContainerFilter]?.toString() ?? '',
            'memoryLimit'
          ),
          render: (memoryLimit: number) => (memoryLimit ? humanReadableSize(memoryLimit) : '-')
        }
      ]
    },
    {
      title: <div className='text-xs text-gray-500'>Resource Requests</div>,
      className: '!pb-1',
      children: [
        {
          key: 'cpuRequest',
          title: 'CPU (Cores)',
          dataIndex: 'cpuRequest',
          sorter: true,
          sortOrder: parseSortOrder(
            filter[getFilterKey('orderBy', filter) as keyof ContainerFilter]?.toString() ?? '',
            'cpuRequest'
          ),
          render: (cpuRequest: number) => (cpuRequest ? cpuRequest.toString() : '-')
        },
        {
          key: 'memoryRequest',
          title: 'Memory',
          dataIndex: 'memoryRequest',
          sorter: true,
          sortOrder: parseSortOrder(
            filter[getFilterKey('orderBy', filter) as keyof ContainerFilter]?.toString() ?? '',
            'memoryRequest'
          ),
          render: (memoryRequest: number) =>
            memoryRequest ? humanReadableSize(memoryRequest) : '-'
        }
      ]
    },
    {
      title: '',
      className: '!pb-1',
      children: [
        {
          title: 'Age',
          key: 'startTime',
          dataIndex: 'startTime',
          sorter: true,
          sortOrder: parseSortOrder(
            filter[getFilterKey('orderBy', filter) as keyof ContainerFilter]?.toString() ?? '',
            'startTime'
          ),
          render: (startTime: string) => (
            <TruncateText className={'block min-w-20'} maxWidth={360}>
              {formatTime(startTime, { fromNow: true })}
            </TruncateText>
          )
        }
      ]
    }
  ];

  return isLoadingContainerList ? (
    <Flex justify={'center'} className='w-full mt-20' style={{ color: akpTextPrimaryColor }}>
      <Loading />
    </Flex>
  ) : (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            colorText: isDarkTheme ? akpTextDarkThemeColor : akpTextPrimaryColor
          }
        }
      }}
    >
      <Table
        className='w-full h-full'
        columns={columns}
        rowClassName={onClick ? 'cursor-pointer' : ''}
        rowKey={(row) => `${row.id}`}
        dataSource={containerList?.containers}
        onChange={handleTableChange(onFilterChange, filter, getFilterKey)}
        onRow={(record) => ({
          onClick: () => onClick?.(record)
        })}
        pagination={{
          pageSizeOptions: [20, 50, 100],
          pageSize: Number(filter[getFilterKey('limit', filter) as keyof ContainerFilter]),
          total: containerList?.count ?? 0,
          current:
            Number(filter[getFilterKey('offset', filter) as keyof ContainerFilter]) /
              Number(filter[getFilterKey('limit', filter) as keyof ContainerFilter]) +
            1,
          showSizeChanger: true
        }}
        scroll={tableHeight ? { x: 'max-content', y: tableHeight } : { x: 'max-content' }}
      />
    </ConfigProvider>
  );
};
