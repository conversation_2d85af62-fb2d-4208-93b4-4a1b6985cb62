import { faBox, faCircleQuestion, faListAlt } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Breadcrumb, Descriptions, DescriptionsProps, Drawer, Row, Tabs, Tooltip } from 'antd';
import { useLocation } from 'react-router-dom';

import { ContainerFilter } from '@ui/feature/kubevision/components/kubevision-container-dashboard/kubevision-container-filter';
import {
  akpTextPrimaryColor,
  DashboardType,
  KubernetesLogType,
  TabType
} from '@ui/feature/kubevision/const';
import useCustomSearchParams from '@ui/feature/kubevision/hooks/use-custom-search-params';
import { useGetKubernetesContainerQuery } from '@ui/lib/apiclient/organization/kubevision-queries';
import { ContainerType } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { formatCoreSize, humanReadableSize } from '@ui/lib/utils';

import { clearQueryParams } from '../../filter';
import { formatTime } from '../../utils';
import { ContainerStatusTag, ContainerTypeTag } from '../shared/container-status-type-tag';
import { ErrorNotFound } from '../shared/error-not-found';
import { KubernetesButton } from '../shared/kubevision-button';
import { KubeVisionLogs } from '../shared/kubevision-log';
import { RecentViews } from '../shared/recent-views';

type Props = {
  instanceId: string;
  filter: ContainerFilter;
  onFilterChange: (filter: Partial<ContainerFilter>, overwrite?: boolean) => void;
};

export const KubernetesContainerDrawer = ({ instanceId, filter, onFilterChange }: Props) => {
  const { pathname, search } = useLocation();
  const { setSearchParams } = useCustomSearchParams();
  const { enabledClustersInfo } = useAkuityIntelligenceContext();

  const clusterId = enabledClustersInfo.clusterId({
    instanceId,
    clusterName: filter.containerCluster
  });

  const { data, isLoading } = useGetKubernetesContainerQuery(
    {
      instanceId,
      containerId: filter.containerId,
      clusterId: clusterId ? clusterId : undefined
    },
    { enabled: !!filter.containerId && !!filter.containerCluster }
  );
  const container = data?.container;

  const tabs = [
    {
      key: TabType.Logs,
      label: (
        <div className={'px-1 flex items-center'}>
          <FontAwesomeIcon icon={faListAlt} className={'mr-2'} />
          Logs
        </div>
      ),
      disabled: true,
      children: (
        <KubeVisionLogs
          instanceId={instanceId}
          clusterId={container?.clusterId}
          resourceId={container?.id}
          logType={KubernetesLogType.Container}
          toTop={432}
        />
      )
    }
  ];

  const shouldShowTypeIcon =
    container?.type == ContainerType.INIT_CONTAINER ||
    container?.type == ContainerType.SIDECAR_CONTAINER ||
    container?.type == ContainerType.EPHEMERAL_CONTAINER;
  let typeLink = '';
  switch (container?.type) {
    case ContainerType.INIT_CONTAINER:
      typeLink = 'https://kubernetes.io/docs/concepts/workloads/pods/init-containers';
      break;
    case ContainerType.SIDECAR_CONTAINER:
      typeLink = 'https://kubernetes.io/docs/concepts/workloads/pods/sidecar-containers';
      break;
    case ContainerType.EPHEMERAL_CONTAINER:
      typeLink = 'https://kubernetes.io/docs/concepts/workloads/pods/ephemeral-containers';
      break;
  }

  const descriptionsItems: DescriptionsProps['items'] = [
    {
      label: 'Image Name',
      children: (
        <div>
          <KubernetesButton
            tooltip={
              <>
                <b className={'mr-1'}>Show image details:</b>
                {container?.image}:{container?.imageTag}
              </>
            }
            text={`${container?.image}:${container?.imageTag}`}
            onClick={() =>
              setSearchParams(
                {
                  tab: 'kubevision',
                  dashboard: DashboardType.Images,
                  image: container?.image,
                  imageTag: container?.imageTag,
                  imageDigest: container?.imageDigest
                },
                { overwrite: true }
              )
            }
          />
        </div>
      )
    },
    {
      label: 'Pod Name',
      children: (
        <div>
          <KubernetesButton
            tooltip={
              <>
                <b className={'mr-1'}>Show pod details:</b>
                {container?.podName}
              </>
            }
            text={container?.podName}
            onClick={() =>
              setSearchParams(
                {
                  tab: 'kubevision',
                  dashboard: DashboardType.Explorer,
                  resourceId: container?.podId,
                  resourceCluster: enabledClustersInfo.clusterName(container?.clusterId),
                  resourceInstance: enabledClustersInfo.instanceName(container?.instanceId)
                },
                { overwrite: true }
              )
            }
          />
        </div>
      )
    },
    {
      label: 'Container Status',
      children: (
        <div>
          <ContainerStatusTag
            status={container?.status}
            onClick={(e) => {
              e.stopPropagation();
              onFilterChange({
                containerId: undefined,
                containerCluster: undefined,
                status: container?.status,
                offset: 0
              });
            }}
          />
        </div>
      )
    },
    {
      label: (
        <div>
          <span>Container Type </span>
          {shouldShowTypeIcon && (
            <a className={'ml-1 cursor-pointer'} href={typeLink} target={'_blank'}>
              <FontAwesomeIcon icon={faCircleQuestion} className='text-[#ccd6de]' />
            </a>
          )}
        </div>
      ),
      children: (
        <div>
          <ContainerTypeTag
            type={container?.type}
            onClick={(e) => {
              e.stopPropagation();
              onFilterChange({
                containerId: undefined,
                containerCluster: undefined,
                type: container?.type,
                offset: 0
              });
            }}
          />
        </div>
      )
    },
    {
      label: 'Node Name',
      children: container?.nodeName || '-'
    },
    {
      label: 'Age',
      children: formatTime(container?.startTime, { fromNow: true })
    },
    {
      label: 'CPU',
      children: (
        <div>
          <div>
            Usage:{' '}
            {container?.cpuUsage
              ? formatCoreSize({ size: container.cpuUsage, hasUnit: false })
              : '-'}
          </div>
          <div>Request: {container?.cpuRequest ? container.cpuRequest.toString() : '-'}</div>
          <div>Limit: {container?.cpuLimit ? container.cpuLimit.toString() : '-'}</div>
        </div>
      )
    },
    {
      label: 'Memory',
      children: (
        <div>
          <div>
            Usage: {container?.memoryUsage ? humanReadableSize(container.memoryUsage) : '-'}
          </div>
          <div>
            Request: {container?.memoryRequest ? humanReadableSize(container.memoryRequest) : '-'}
          </div>
          <div>
            Limit: {container?.memoryLimit ? humanReadableSize(container.memoryLimit) : '-'}
          </div>
        </div>
      )
    }
  ];

  return (
    <Drawer
      open={!!filter.containerId}
      onClose={() => {
        const clearedFilter = clearQueryParams(filter, 'detailsTab');
        onFilterChange({
          ...clearedFilter,
          containerId: undefined,
          containerCluster: undefined,
          detailsTab: undefined
        });
      }}
      title={
        <Row justify='space-between' align='middle'>
          <span>
            <FontAwesomeIcon icon={faBox} className='mr-2' />{' '}
            {`${isLoading ? 'Loading...' : (container?.name ?? 'N/A')}`}
          </span>

          <div className='grow' />
          <div className='mr-2 flex align-middle text-sm'>
            {isLoading ? (
              <div style={{ margin: '-0.5rem', color: akpTextPrimaryColor }}>
                <Loading />
              </div>
            ) : (
              <div className='mr-2'>
                <Breadcrumb
                  items={[
                    {
                      title: (
                        <Tooltip
                          styles={{ body: { width: 'fit-content' } }}
                          title={
                            <div
                              className='whitespace-nowrap overflow-hidden text-ellipsis'
                              style={{ maxWidth: '400px' }}
                            >
                              <b className='mr-1'>Filter by pod:</b> {container?.podName || ''}
                            </div>
                          }
                        >
                          {container?.podName || ''}
                        </Tooltip>
                      ),
                      href: '',
                      onClick: (e) => {
                        e.preventDefault();
                        onFilterChange({
                          containerId: undefined,
                          containerCluster: undefined,
                          podId: container?.podId,
                          instance: container?.instanceId
                            ? enabledClustersInfo.instanceName(container.instanceId)
                            : undefined,
                          cluster: container?.clusterId
                            ? enabledClustersInfo.clusterName(container.clusterId)
                            : undefined,
                          offset: 0
                        });
                      }
                    },
                    {
                      title: (
                        <Tooltip title={`${container?.name || ''}`}>
                          {container?.name || ''}
                        </Tooltip>
                      ),
                      href: '',
                      onClick: (e) => {
                        e.preventDefault();
                      }
                    }
                  ]}
                />
              </div>
            )}
          </div>
          <RecentViews key={pathname + search} containerName={container?.name} />
        </Row>
      }
      width='80%'
    >
      {isLoading ? (
        <Loading />
      ) : container ? (
        <div>
          <Descriptions
            styles={{ label: { fontWeight: 'bold', padding: '12px' } }}
            className='mb-4'
            size='small'
            bordered
            column={2}
            items={descriptionsItems}
          />

          <Tabs destroyOnHidden activeKey={filter.detailsTab || TabType.Logs} items={tabs} />
        </div>
      ) : (
        <ErrorNotFound />
      )}
    </Drawer>
  );
};
