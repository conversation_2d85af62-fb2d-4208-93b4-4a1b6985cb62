import { faCaretDown, faCircleQuestion } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Dropdown, Flex, MenuProps, Row, Tooltip } from 'antd';
import { useCallback, useEffect } from 'react';
import React, { useMemo } from 'react';
import { useLocation } from 'react-router-dom';

import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import KubeVisionContainerDashboard from './components/kubevision-container-dashboard/kubevision-container-dashboard';
import { KubevisionDeletionDashboard } from './components/kubevision-deletion-dashboard/kubevision-deletion-dashboard';
import { KubevisonDeprecatedAPIDashboard } from './components/kubevision-deprecated-api-dashboard/kubevison-deprecated-api-dashboard';
import { KubeVisionExplorerDashboard } from './components/kubevision-explorer-dashboard/kubevision-explorer-dashboard';
import KubeVisionImageDashboard from './components/kubevision-image-dashboard/kubevision-image-dashboard';
import { KubevisionIncidentDashboard } from './components/kubevision-incident-dashboard/kubevision-incident-dashboard';
import { KubeVisionInfrastructureDashboard } from './components/kubevision-infrastructure-dashboard/kubevision-infrastructure-dashboard';
import { KubevisionOverviewDashboard } from './components/kubevision-overview-dashboard/kubevision-overview-dashboard';
import { KubeVisionCheckEnabledClusters } from './components/shared/kubevision-check-enabled-clusters';
import { RecentViews } from './components/shared/recent-views';
import { SpotlightSearch } from './components/shared/spotlight-search';
import { dashboardTitle, DashboardType } from './const';
import useCustomSearchParams from './hooks/use-custom-search-params';
const DashboardDropdown: React.FC<{
  activatedSubTab: DashboardType;
  onDashboardChange: MenuProps['onClick'];
  isAISREFeatureOn: boolean;
}> = React.memo(({ activatedSubTab, onDashboardChange, isAISREFeatureOn }) => {
  const dashboardOptions: MenuProps['items'] = useMemo(
    () => [
      {
        key: DashboardType.Overview,
        label: <span>{dashboardTitle.Overview}</span>
      },
      { type: 'divider' },
      {
        key: 'group:overview',
        type: 'group',
        label: 'Overview',
        children: [
          { key: DashboardType.Explorer, label: dashboardTitle.Explorer },
          { key: DashboardType.Deprecated, label: <span>{dashboardTitle.Deprecated}</span> },
          { key: DashboardType.Deletion, label: <span>{dashboardTitle.Deletion}</span> }
        ]
      },
      { type: 'divider' },
      {
        key: 'group:workloads',
        type: 'group',
        label: 'Workloads',
        children: [
          { key: DashboardType.Containers, label: <span>{dashboardTitle.Containers}</span> },
          { key: DashboardType.Images, label: <span>{dashboardTitle.Images}</span> }
        ]
      },
      { type: 'divider' },
      { key: DashboardType.Infrastructure, label: <span>{dashboardTitle.Infrastructure}</span> },
      ...(isAISREFeatureOn
        ? [
            {
              key: DashboardType.Incidents,
              label: <span>{dashboardTitle.Incidents}</span>
            }
          ]
        : [])
    ],
    [isAISREFeatureOn]
  );

  return (
    <Dropdown
      placement='bottomLeft'
      menu={{
        items: dashboardOptions,
        selectable: true,
        selectedKeys: [activatedSubTab],
        onClick: onDashboardChange
      }}
    >
      <Row className='w-fit' align='middle'>
        <h1>{dashboardTitle[activatedSubTab]}</h1>
        <FontAwesomeIcon className='ml-4 text-2xl' icon={faCaretDown} />
      </Row>
    </Dropdown>
  );
});

type Props = {
  isk8sFeatureOn: boolean;
  isAISREFeatureOn: boolean;
  disabled?: boolean;
};

export const KubeVision = ({ isk8sFeatureOn, isAISREFeatureOn, disabled }: Props) => {
  const { instanceId } = useAkuityIntelligenceContext();

  const { pathname, search } = useLocation();

  const { getSearchParam, setSearchParams } = useCustomSearchParams();

  const activatedSubTab = useMemo(() => {
    const dashboard = getSearchParam('dashboard') as DashboardType;
    if (Object.values(DashboardType).includes(dashboard)) {
      return dashboard;
    }
    return DashboardType.Overview;
  }, [getSearchParam]);

  const handleDashboardChange = useCallback<MenuProps['onClick']>(
    (e) => {
      setSearchParams({ tab: 'kubevision', dashboard: e.key }, { overwrite: true });
    },
    [setSearchParams]
  );

  useEffect(() => {
    const currentDashboard = getSearchParam('dashboard');

    if (currentDashboard) {
      return;
    }

    const shouldSetDefault = pathname.endsWith('/kubevision') || pathname.endsWith('/monitor');

    if (shouldSetDefault) {
      setSearchParams({ dashboard: DashboardType.Overview }, { replace: true });
    }
  }, [pathname, getSearchParam, setSearchParams]);

  const renderDashboard = () => {
    switch (activatedSubTab) {
      case DashboardType.Images:
        return <KubeVisionImageDashboard instanceId={instanceId} />;
      case DashboardType.Containers:
        return <KubeVisionContainerDashboard instanceId={instanceId} />;
      case DashboardType.Infrastructure:
        return <KubeVisionInfrastructureDashboard instanceId={instanceId} />;
      case DashboardType.Deprecated:
        return <KubevisonDeprecatedAPIDashboard />;
      case DashboardType.Deletion:
        return <KubevisionDeletionDashboard />;
      case DashboardType.Explorer:
        return <KubeVisionExplorerDashboard instanceId={instanceId} />;
      case DashboardType.Incidents:
        if (isAISREFeatureOn) {
          return <KubevisionIncidentDashboard instanceId={instanceId} />;
        }
        return <></>;
      case DashboardType.Overview:
      default:
        return (
          <KubevisionOverviewDashboard
            instanceId={instanceId}
            isAISREFeatureOn={isAISREFeatureOn}
          />
        );
    }
  };

  if (!isk8sFeatureOn || disabled) {
    return <></>;
  }

  return (
    <KubeVisionCheckEnabledClusters>
      <Row className='pb-3' align='middle' justify='space-between'>
        <Flex align='center'>
          <DashboardDropdown
            activatedSubTab={activatedSubTab}
            onDashboardChange={handleDashboardChange}
            isAISREFeatureOn={isAISREFeatureOn}
          />
          {activatedSubTab === DashboardType.Deletion && (
            <Tooltip
              placement='right'
              title='Resources displayed here have been stuck in the deletion state for more than 1 hour, as indicated by their deletion timestamps which were set over 1 hour ago.'
            >
              <FontAwesomeIcon
                className='mt-1 ml-4 text-lg text-gray-300'
                icon={faCircleQuestion}
              />
            </Tooltip>
          )}

          <div id='kubevision-dashboard-header-tree-view-btn' className='pl-4 py-0.5' />
          <div id='kubevision-dashboard-header-degraded-status' className='pl-4 py-0.5' />
        </Flex>

        <Flex align='center' className='my-2'>
          <SpotlightSearch instanceId={instanceId} className='mr-2' />
          <RecentViews key={pathname + search} />
        </Flex>
      </Row>

      {renderDashboard()}
    </KubeVisionCheckEnabledClusters>
  );
};
