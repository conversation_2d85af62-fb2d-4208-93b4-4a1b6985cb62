import { PlainMessage } from '@bufbuild/protobuf';
import { faColumns, faSquareMinus, faWandMagicSparkles } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, ConfigProvider, Modal } from 'antd';
import { useRef, useState } from 'react';
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer-continued';

import { AISuggestedChange } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { useModal } from '@ui/lib/hooks';

// Define the type for ReactDiffViewer component
type ReactDiffViewerType = {
  resetCodeBlocks?: () => void;
};

interface DiffViewerProps {
  suggestedChange: PlainMessage<AISuggestedChange>;
  messageTypeClass: string;
  onSendMessage: (message: string) => void;
}

export const DiffViewer = ({
  suggestedChange,
  messageTypeClass,
  onSendMessage
}: DiffViewerProps) => {
  const { isDarkTheme } = useAkuityIntelligenceContext();
  const diffViewerRef = useRef<ReactDiffViewerType | null>(null);
  const [splitView, setSplitView] = useState<boolean>(false);
  const { show: showConfirmModal, hide: hideConfirmModal } = useModal();

  const customDiffStyles = {
    variables: {
      light: {
        diffViewerBackground: 'rgba(255,255,255,0.3)',
        diffViewerColor: '#64748b',
        gutterBackground: 'rgba(255,255,255,0.2)'
      },
      dark: {
        diffViewerBackground: '#1a1f2e'
      }
    },
    contentText: {
      fontSize: '0.85rem',
      lineHeight: '1.4'
    },
    gutter: {
      minWidth: '40px'
    },
    line: {
      padding: '2px 0'
    }
  };

  const handleApplyChange = () => {
    // Open confirmation modal instead of directly applying changes
    showConfirmModal(({ visible, hide }) => (
      <Modal
        title='Confirm to Apply Changes'
        open={visible}
        onOk={confirmApplyChange}
        onCancel={hide}
        okText='Apply'
        cancelText='Cancel'
      >
        <p>Are you sure you want to apply these changes?</p>
      </Modal>
    ));
  };

  const confirmApplyChange = () => {
    onSendMessage('Apply suggested patch');
    hideConfirmModal();
  };

  const toggleDiffCollapse = () => {
    // Use the resetCodeBlocks method if available
    diffViewerRef.current?.resetCodeBlocks?.();
  };

  const toggleSplitView = () => {
    setSplitView(!splitView);
    // Reset code blocks after changing view mode to ensure proper rendering
    if (diffViewerRef.current?.resetCodeBlocks) {
      setTimeout(() => {
        diffViewerRef.current.resetCodeBlocks?.();
      }, 0);
    }
  };

  return (
    <div className={`diff-container ${messageTypeClass}`}>
      <div className='diff-header'>
        <Button
          icon={<FontAwesomeIcon icon={faSquareMinus} />}
          type={'default'}
          size='small'
          onClick={toggleDiffCollapse}
          className='diff-collapse-button'
        >
          Collapse
        </Button>
        <ConfigProvider theme={{ components: { Button: { defaultColor: '#64748b' } } }}>
          <Button
            icon={<FontAwesomeIcon icon={faColumns} />}
            type={splitView ? 'primary' : 'default'}
            size='small'
            onClick={toggleSplitView}
            className='view-button'
          >
            Layout
          </Button>
        </ConfigProvider>
      </div>
      <div className='react-diff-viewer-container'>
        <ReactDiffViewer
          ref={(ref) => {
            if (ref) diffViewerRef.current = ref;
          }}
          useDarkTheme={isDarkTheme}
          compareMethod={DiffMethod.WORDS}
          oldValue={suggestedChange.old}
          newValue={suggestedChange.new}
          splitView={splitView}
          styles={customDiffStyles}
          nonce={__webpack_nonce__}
        />
      </div>
      {messageTypeClass === 'bot-message' && !suggestedChange.applied && (
        <div className='diff-footer'>
          <Button
            onClick={handleApplyChange}
            size='small'
            type='primary'
            icon={<FontAwesomeIcon icon={faWandMagicSparkles} />}
          >
            Apply Changes
          </Button>
        </div>
      )}
    </div>
  );
};
