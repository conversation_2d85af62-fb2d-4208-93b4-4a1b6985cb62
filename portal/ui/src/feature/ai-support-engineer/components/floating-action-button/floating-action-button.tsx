import { faRobot } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button } from 'antd';
import React, { useEffect, useState, useRef } from 'react';

import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { useLocalStorage } from '../../../../lib/utils';
import './floating-action-button.less';

interface FloatingActionButtonProps {
  onClick: () => void;
}

type SnapPosition = 'left' | 'right' | 'none';

interface ButtonPosition {
  x: number;
  y: number;
}

const defaultPosition: ButtonPosition = {
  x: window.innerWidth - 56,
  y: window.innerHeight / 2
};

const validatePosition = (pos: ButtonPosition): ButtonPosition => {
  const maxX = window.innerWidth - 56; // button width
  const maxY = window.innerHeight - 56; // button height

  return {
    x: Math.min(Math.max(0, pos.x), maxX),
    y: Math.min(Math.max(0, pos.y), maxY)
  };
};

const getInitialPosition = (storedPos: ButtonPosition): ButtonPosition => {
  const validatedPos = validatePosition(storedPos);
  // Determine if it should snap to left or right based on the validated position
  const windowCenter = window.innerWidth / 2;
  const shouldSnapLeft = validatedPos.x < windowCenter;
  return {
    x: shouldSnapLeft ? 0 : window.innerWidth - 56,
    y: validatedPos.y
  };
};

export const FloatingActionButton = ({ onClick }: FloatingActionButtonProps) => {
  const { showAlert } = useAkuityIntelligenceContext();
  const [storedPosition, setStoredPosition] = useLocalStorage<ButtonPosition>(
    'akpAIButtonPosition',
    defaultPosition
  );
  const [position, setPosition] = useState<ButtonPosition>(() =>
    getInitialPosition(storedPosition)
  );
  const [isDragging, setIsDragging] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [snapPosition, setSnapPosition] = useState<SnapPosition>(() =>
    position.x === 0 ? 'left' : 'right'
  );
  const buttonRef = useRef<HTMLDivElement>(null);
  const lastMousePosition = useRef({ x: 0, y: 0 });
  const moveThreshold = useRef(5); // 5px threshold to determine if it's a drag
  const startPosition = useRef({ x: 0, y: 0 });
  const hasMoved = useRef(false);

  // Effect to handle component mount and window resize
  useEffect(() => {
    const handleResize = () => {
      if (!isPressed) {
        const newPosition = getInitialPosition(position);
        setPosition(newPosition);
        setStoredPosition(newPosition);
        setSnapPosition(newPosition.x === 0 ? 'left' : 'right');
      }
    };

    // Handle initial position
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isDragging]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isPressed) return;

      // Check if movement exceeds threshold
      const deltaX = Math.abs(e.clientX - startPosition.current.x);
      const deltaY = Math.abs(e.clientY - startPosition.current.y);
      if (deltaX > moveThreshold.current || deltaY > moveThreshold.current) {
        setIsDragging(true);
        hasMoved.current = true;
        lastMousePosition.current = { x: e.clientX, y: e.clientY };

        const newX = Math.min(
          Math.max(0, e.clientX - dragStart.x),
          window.innerWidth - (buttonRef.current?.offsetWidth || 0)
        );
        const newY = Math.min(
          Math.max(0, e.clientY - dragStart.y),
          window.innerHeight - (buttonRef.current?.offsetHeight || 0)
        );

        setPosition({ x: newX, y: newY });

        // Update snap position while dragging based on window center
        const windowCenter = window.innerWidth / 2;
        const shouldSnapLeft = e.clientX < windowCenter;
        const shouldSnapRight = e.clientX >= windowCenter;

        if (shouldSnapLeft) {
          setSnapPosition('left');
        } else if (shouldSnapRight) {
          setSnapPosition('right');
        }
      }
    };

    const handleMouseUp = () => {
      if (!isPressed) return;

      setIsPressed(false);
      setIsDragging(false);
      setTimeout(() => {
        hasMoved.current = false;
      }, 200);

      // Snap to edge based on which half of the window the mouse is in
      const windowCenter = window.innerWidth / 2;
      const mouseX = lastMousePosition.current.x;
      const shouldSnapLeft = mouseX < windowCenter;

      const newX = shouldSnapLeft ? 0 : window.innerWidth - 56;
      const newPosition = {
        ...position,
        x: newX
      };
      setPosition(newPosition);
      setStoredPosition(newPosition);
      setSnapPosition(shouldSnapLeft ? 'left' : 'right');
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragStart, position, setStoredPosition]);

  const handleClick = () => {
    if (!hasMoved.current) {
      onClick();
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (buttonRef.current) {
      setIsPressed(true);
      setIsDragging(false);
      hasMoved.current = false;
      startPosition.current = { x: e.clientX, y: e.clientY };
      setDragStart({
        x: e.clientX - buttonRef.current.offsetLeft,
        y: e.clientY - buttonRef.current.offsetTop
      });
      lastMousePosition.current = { x: e.clientX, y: e.clientY };
    }
  };

  return (
    <div
      ref={buttonRef}
      className={`floating-action-button ${isDragging ? 'dragging' : ''} ${snapPosition !== 'none' ? `snap-${snapPosition}` : ''}`}
      style={{
        position: 'fixed',
        left:
          !isDragging && snapPosition === 'right' && isHovered
            ? `${position.x - 140}px`
            : `${position.x}px`,
        top: `${position.y}px`,
        transition: isDragging ? 'none' : 'all 0.3s ease-in-out',
        cursor: isDragging ? 'grabbing' : 'grab',
        zIndex: 10000
      }}
      onMouseDown={handleMouseDown}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Button
        type='primary'
        shape='circle'
        size='large'
        onClick={handleClick}
        className='pulse-animation'
        icon={
          showAlert ? (
            <FontAwesomeIcon icon={showAlert.alertIcon} className='tilt-shaking' />
          ) : (
            <FontAwesomeIcon icon={faRobot} />
          )
        }
        style={{
          width: isHovered ? '200px' : '56px',

          background: isHovered
            ? 'linear-gradient(135deg, rgba(255, 182, 193, 0.9) 0%, rgba(221, 160, 221, 0.9) 25%, rgba(176, 224, 230, 0.9) 50%, rgba(255, 218, 185, 0.9) 75%, rgba(255, 182, 193, 0.9) 100%)'
            : 'linear-gradient(135deg, rgba(255, 218, 185, 0.9) 0%, rgba(176, 224, 230, 0.9) 25%, rgba(221, 160, 221, 0.9) 50%, rgba(255, 182, 193, 0.9) 75%, rgba(255, 218, 185, 0.9) 100%)'
        }}
      >
        {isHovered && 'Akuity Intelligence'}
      </Button>
    </div>
  );
};
