import { PlainMessage } from '@bufbuild/protobuf';
import { Flex } from 'antd';

import { AIMessageContext } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';

import { ResourceTag } from '../resource-tag/resource-tag';

interface SuggestedContextsProps {
  suggestedContexts: PlainMessage<AIMessageContext>[];
  contexts: PlainMessage<AIMessageContext>[];
  updateContexts: (contexts: PlainMessage<AIMessageContext>[]) => void;
}

export const SuggestedContexts = ({
  suggestedContexts,
  updateContexts,
  contexts
}: SuggestedContextsProps) => {
  const { enabledClustersInfo } = useAkuityIntelligenceContext();

  const getContextKey = (context: PlainMessage<AIMessageContext>): string => {
    if (context.kargoProject) {
      return `kargo-${context.kargoProject?.name}-${context.kargoProject?.instanceId}`;
    }
    if (context.argoCdApp) {
      return `argo-${context.argoCdApp?.name}-${context.argoCdApp?.instanceId}`;
    }
    if (context.k8sNamespace) {
      return `k8s-${context.k8sNamespace?.name}-${context.k8sNamespace?.clusterId}`;
    }
    return '';
  };

  const handleApplyContext = (context: PlainMessage<AIMessageContext>) => {
    const existingKeys = new Set(contexts.map(getContextKey));

    const newContextKey = getContextKey(context);

    if (newContextKey && !existingKeys.has(newContextKey)) {
      updateContexts(contexts.concat(context));
    }
  };

  if (!suggestedContexts || suggestedContexts.length === 0) {
    return null;
  }

  const resource = (context: PlainMessage<AIMessageContext>) => {
    if (context.kargoProject) {
      return {
        kind: 'Project',
        name: context.kargoProject?.name || ''
      };
    }

    if (context.argoCdApp) {
      return {
        kind: 'Application',
        name: context.argoCdApp?.name || ''
      };
    }

    return {
      kind: 'Namespace',
      name: context.k8sNamespace?.name || '',
      clusterId: context.k8sNamespace?.clusterId
    };
  };

  return (
    <>
      <strong className='block mt-4 text-sm'>Suggested Contexts:</strong>
      <span className='block mt-2 mb-2 text-sm'>
        Click to add the suggested context to your next message.
      </span>
      <Flex wrap gap='2px' className='mb-4'>
        {suggestedContexts.map((context, index) => {
          return (
            <div
              key={index}
              onClick={() => handleApplyContext(context)}
              style={{ cursor: 'pointer' }}
              className='hover:opacity-80'
              title='Click to add to context'
            >
              <ResourceTag enabledClustersInfo={enabledClustersInfo} resource={resource(context)} />
            </div>
          );
        })}
      </Flex>
    </>
  );
};
