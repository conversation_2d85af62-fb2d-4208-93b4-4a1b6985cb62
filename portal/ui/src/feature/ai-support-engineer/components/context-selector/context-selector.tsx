import { PlainMessage } from '@bufbuild/protobuf';
import { faLayerGroup, faPlus } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Dropdown, Flex, Input, Row } from 'antd';
import React, { useMemo, useState } from 'react';

import { AIMessageContext } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components/loading';
import { useAkuityIntelligenceContext } from '@ui/lib/context/akuity-intelligence-context';
import { useSpotlightSearch } from '@ui/lib/hooks/use-spotlight-search';

import ArgoLogo from '../../../../../public/images/argo/argo-logo.svg';
import KargoLogo from '../../../../../public/images/kargo.png';
import { getKind, cxtKey, contextToResource } from '../../utils';
import { ResourceTag } from '../resource-tag/resource-tag';
interface ContextWithLink {
  context: PlainMessage<AIMessageContext>;
  link?: string;
}

export const ContextSelector = ({
  updateContexts,
  selectedContexts
}: {
  selectedContexts: PlainMessage<AIMessageContext>[];
  updateContexts: (contexts: PlainMessage<AIMessageContext>[]) => void;
}) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const { isInAKP, isInKargo, instanceId, organizationId, organizationName, enabledClustersInfo } =
    useAkuityIntelligenceContext();
  const {
    resources: searchResults,
    isLoading,
    query: searchText,
    setQuery: setSearchText
  } = useSpotlightSearch(organizationId, instanceId, true);

  const contextsWithLinks = React.useMemo<ContextWithLink[]>(() => {
    if (!searchResults) {
      return new Array<ContextWithLink>();
    }

    const resources = new Array<ContextWithLink>();
    searchResults.forEach((item) => {
      let context: PlainMessage<AIMessageContext>;
      let link: string | undefined;

      if (item.kind === 'Application' && item.group === 'dashboard.akuity.io') {
        context = {
          argoCdApp: {
            name: item.name,
            instanceId: item.instanceId
          }
        };
        link = item.argocdApplicationInfo?.link;
      } else if (item.kind === 'Namespace' && item.group === '') {
        context = {
          k8sNamespace: {
            name: item.name,
            clusterId: item.clusterId,
            instanceId: item.instanceId
          }
        };
        const resource = contextToResource(context, {
          isInAKP,
          isInKargo,
          organizationName,
          instanceName: enabledClustersInfo.instanceName(item.instanceId),
          clusterName: enabledClustersInfo.clusterName(item.clusterId)
        });
        link = resource.link;
      } else if (item.kind === 'Project' && item.group === 'kargo.akuity.io') {
        context = {
          kargoProject: {
            name: item.name,
            instanceId: item.instanceId
          }
        };
        link = item.columns['Link'];
      } else {
        return;
      }

      resources.push({
        context,
        link
      });
    });
    return resources;
  }, [searchResults, isInAKP, isInKargo, organizationName, enabledClustersInfo]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  };

  const ctxByKey = useMemo(() => {
    const map = new Map<string, ContextWithLink>();
    contextsWithLinks.forEach((item) => {
      map.set(cxtKey(item.context), item);
    });
    return map;
  }, [contextsWithLinks]);

  const selectedKeys = useMemo(() => selectedContexts?.map(cxtKey) || [], [selectedContexts]);

  const dropdownItems = Array.from(
    ctxByKey.entries().map(([key, item]) => ({
      key,
      label: (
        <div className='resource-item p-2 rounded group'>
          <Flex vertical gap={2}>
            <div className='font-medium'>
              {item.context.k8sNamespace?.name ||
                item.context.argoCdApp?.name ||
                item.context.kargoProject?.name}
            </div>
            <Flex align='center' className='text-xs text-gray-500' gap={8}>
              <Flex align='center' gap={1}>
                <span className='text-gray-400'>Kind:</span>
                <span className='flex items-center ml-1 text-[#64748b]'>
                  <span>{getKind(item.context)}</span>
                  {getKind(item.context) === 'Application' && item.context.argoCdApp?.name && (
                    <img className='w-[14px] ml-2' src={ArgoLogo} alt='logo' />
                  )}
                  {getKind(item.context) === 'Project' && item.context.kargoProject?.name && (
                    <img className='w-[14px] ml-2' src={KargoLogo} alt='logo' />
                  )}
                </span>

                {getKind(item.context) === 'Namespace' && (
                  <>
                    <span className='ml-2 text-gray-400'>Cluster:</span>
                    <span className='flex items-center ml-1 text-[#64748b]'>
                      {enabledClustersInfo.clusterName(item.context.k8sNamespace?.clusterId)}
                    </span>
                  </>
                )}
              </Flex>
            </Flex>
          </Flex>
        </div>
      )
    }))
  );

  return (
    <Row>
      <Dropdown
        placement='topLeft'
        trigger={['click']}
        open={dropdownVisible}
        onOpenChange={setDropdownVisible}
        menu={{
          items: dropdownItems,
          multiple: true,
          selectedKeys,
          style: {
            maxHeight: '400px',
            overflow: 'auto',
            width: 'max-content'
          },
          onClick: ({ key }: { key: string }) => {
            if (selectedKeys.includes(key)) {
              updateContexts((selectedContexts || []).filter((ctx) => key !== cxtKey(ctx)));
            } else {
              const contextWithLink = ctxByKey.get(key);
              if (contextWithLink) {
                updateContexts([...(selectedContexts || []), contextWithLink.context]);
              }
            }
          }
        }}
      >
        {dropdownVisible ? (
          <Input
            size='small'
            placeholder='Search Application, Name, Namespace...'
            value={searchText}
            onChange={handleSearchChange}
            className='w-[280px]'
            allowClear={!!searchText}
            autoFocus
            suffix={
              isLoading && <Loading text=' ' className='text-gray-500 text-xs !m-0 !mr-[-.5rem]' />
            }
          />
        ) : (
          <Button size='small' icon={<FontAwesomeIcon icon={faLayerGroup} />}>
            Add Context ({selectedContexts?.length || 0} selected)
            <FontAwesomeIcon icon={faPlus} size='xs' />
          </Button>
        )}
      </Dropdown>

      <div className='context-tags-container ml-2 flex-1 overflow-x-auto whitespace-nowrap flex flex-row items-center'>
        {selectedContexts?.map((ctx, i) => {
          const contextWithLink = contextsWithLinks.find(
            (item) => cxtKey(item.context) === cxtKey(ctx)
          );

          const resource = contextToResource(ctx, {
            isInAKP,
            isInKargo,
            organizationName,
            instanceName: ctx.k8sNamespace?.instanceId
              ? enabledClustersInfo.instanceName(ctx.k8sNamespace.instanceId)
              : undefined,
            clusterName: ctx.k8sNamespace?.clusterId
              ? enabledClustersInfo.clusterName(ctx.k8sNamespace.clusterId)
              : undefined
          });

          return (
            <ResourceTag
              key={i}
              enabledClustersInfo={enabledClustersInfo}
              resource={{
                ...resource,
                link: contextWithLink?.link || resource.link
              }}
              closable
              onClose={() => {
                updateContexts(selectedContexts.filter((c) => c !== ctx));
              }}
            />
          );
        })}
      </div>
    </Row>
  );
};
