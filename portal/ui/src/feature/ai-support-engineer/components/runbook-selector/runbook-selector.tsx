import { PlainMessage } from '@bufbuild/protobuf';
import { faBook, faPlus } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Dropdown, Flex, Input, Row, Tag } from 'antd';
import { useMemo, useState } from 'react';

import { Runbook } from '@ui/lib/apiclient/argocd/v1/argocd_pb';
import { useGetInstanceQuery, useListInstances } from '@ui/lib/apiclient/organization/ai-queries';
import { AIMessageRunbook } from '@ui/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@ui/lib/components/loading';
import { useAISupportEngineerContext } from '@ui/lib/context/ai-support-engineer-context';

type RunbookItem = PlainMessage<Runbook> & {
  instanceId: string;
};

type RunbookSelectorProps = {
  selectedRunbooks: PlainMessage<AIMessageRunbook>[];
  updateRunbooks: (runbooks: PlainMessage<AIMessageRunbook>[]) => void;
};

export const RunbookSelector = ({ selectedRunbooks, updateRunbooks }: RunbookSelectorProps) => {
  const [searchText, setSearchText] = useState('');
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const { instanceId, enabledClustersInfo } = useAISupportEngineerContext();

  const { data: instanceResp, isLoading: isInstanceLoading } = useGetInstanceQuery(instanceId, {
    enabled: !!instanceId
  });
  const { data: instancesResp, isLoading: isInstancesLoading } = useListInstances({
    enabled: !instanceId
  });

  const isLoading = isInstanceLoading || isInstancesLoading;

  const instances = useMemo(
    () =>
      instanceId
        ? instanceResp?.instance
          ? [instanceResp.instance]
          : []
        : (instancesResp?.instances ?? []),
    [instanceResp, instancesResp, instanceId]
  );

  const runbooks: RunbookItem[] = useMemo(() => {
    const items: RunbookItem[] = [];
    instances.forEach((i) => {
      i.spec?.kubeVisionConfig?.aiConfig?.runbooks?.forEach((r) => {
        items.push({ ...r, instanceId: i.id });
      });
    });
    return items;
  }, [instances]);

  const dropdownItems = runbooks.map((r) => ({
    key: `${r.instanceId}-${r.name}`,
    label: (
      <div className='resource-item p-2 rounded group min-w-[160px]'>
        <Flex vertical gap={2}>
          <div className='font-medium'>{r.name}</div>
          <div className='text-xs text-gray-500'>
            {enabledClustersInfo.instanceName(r.instanceId)}
          </div>
        </Flex>
      </div>
    )
  }));

  return (
    <Row>
      <Dropdown
        placement='topLeft'
        trigger={['click']}
        open={dropdownVisible}
        onOpenChange={setDropdownVisible}
        menu={{
          items: dropdownItems,
          multiple: true,
          selectedKeys: selectedRunbooks.map((r) => `${r.instanceId}-${r.name}`),
          style: {
            maxHeight: '400px',
            overflow: 'auto',
            width: 'max-content'
          },
          onClick: ({ key }: { key: string }) => {
            const runbook = runbooks.find((r) => `${r.instanceId}-${r.name}` === key);
            if (
              runbook &&
              !selectedRunbooks.some(
                (r) => r.name === runbook.name && r.instanceId === runbook.instanceId
              )
            ) {
              updateRunbooks([...selectedRunbooks, runbook]);
            }
          }
        }}
      >
        {dropdownVisible ? (
          <Input
            size='small'
            placeholder='Search Runbook...'
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className='w-[280px]'
            allowClear={!!searchText}
            autoFocus
            suffix={
              isLoading && <Loading text=' ' className='text-gray-500 text-xs !m-0 !mr-[-.5rem]' />
            }
          />
        ) : (
          <Button size='small' icon={<FontAwesomeIcon icon={faBook} />}>
            Add Runbook ({selectedRunbooks?.length || 0} selected)
            <FontAwesomeIcon icon={faPlus} size='xs' />
          </Button>
        )}
      </Dropdown>

      <div className='context-tags-container ml-2 flex-1 overflow-x-auto whitespace-nowrap flex flex-row items-center'>
        {selectedRunbooks?.map((runbook, i) => {
          return (
            <Tag
              key={i}
              className={`text-[#64748b] mr-1 border-[#64748b81] bg-[#e2e8f0] hover:bg-slate-50`}
              closable
              onClose={() => {
                updateRunbooks(selectedRunbooks.filter((r) => r !== runbook));
              }}
            >
              {runbook.name}
            </Tag>
          );
        })}
      </div>
    </Row>
  );
};
