import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { ConfigProvider, theme } from 'antd';

import {
  AkuityIntelligenceContextProvider,
  useAkuityIntelligenceContext
} from '@ui/lib/context/akuity-intelligence-context';
import { ModalContextProvider } from '@ui/lib/context/modal-context';
import { NotificationContextProvider } from '@ui/lib/context/notification-context';
import { OrganizationRole } from '@ui/lib/types';
import type { ExtendedAIMessageContext } from '@ui/lib/types';

import { AISupportEngineerDrawer } from './components/ai-support-engineer-drawer/ai-support-engineer-drawer';
import { useAISupportEngineerDrawer } from './hooks/use-ai-support-engineer-drawer';

interface AISupportEngineerProps {
  organizationId: string;
  organizationName?: string;
  instanceId?: string;
  showAlert?: { alertIcon: IconDefinition };
  defaultContexts?: ExtendedAIMessageContext[];
  isKubeVisionExtensionEnabled?: boolean;
  currentRole?: OrganizationRole;
  modelVersion?: string;
}

export const AISupportEngineerContent = () => {
  const { isOpen, clearSelection, setIsOpen, conversationId, lastOpenConversationId } =
    useAISupportEngineerDrawer();
  const { isDarkTheme } = useAkuityIntelligenceContext();

  return (
    <ConfigProvider
      theme={{ algorithm: isDarkTheme ? theme.darkAlgorithm : theme.defaultAlgorithm }}
    >
      <NotificationContextProvider>
        <ModalContextProvider>
          <AISupportEngineerDrawer
            open={isOpen}
            clearSelection={clearSelection}
            setIsOpen={setIsOpen}
            selectedConversationId={conversationId}
            lastOpenConversationId={lastOpenConversationId}
          />
        </ModalContextProvider>
      </NotificationContextProvider>
    </ConfigProvider>
  );
};

export const AISupportEngineer = ({
  organizationId,
  organizationName,
  instanceId,
  showAlert,
  defaultContexts,
  isKubeVisionExtensionEnabled,
  currentRole,
  modelVersion
}: AISupportEngineerProps) => {
  return (
    <div className='ai-support-engineer h-auto'>
      <ConfigProvider
        theme={{
          components: {
            Modal: {
              zIndexPopupBase: 1002
            }
          }
        }}
      >
        <AkuityIntelligenceContextProvider
          isKubeVisionExtensionEnabled={isKubeVisionExtensionEnabled}
          organizationId={organizationId}
          organizationName={organizationName}
          instanceId={instanceId}
          showAlert={showAlert}
          defaultContexts={defaultContexts}
          currentRole={currentRole}
          modelVersion={modelVersion}
        >
          <AISupportEngineerContent />
        </AkuityIntelligenceContextProvider>
      </ConfigProvider>
    </div>
  );
};
