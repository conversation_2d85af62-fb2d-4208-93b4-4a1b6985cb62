import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { useQuery } from '@tanstack/react-query';
import { ConfigProvider, Flex, theme } from 'antd';
import React, { createContext, useContext, useMemo } from 'react';

import { akpTextPrimaryColor } from '@ui/feature/kubevision/const';
import { usePlatformContext } from '@ui/feature/shared/context/platform-context';
import { usePlatformApiFetch } from '@ui/feature/shared/hooks/use-platform-api-fetch';
import type { ExtendedAIMessageContext } from '@ui/lib/types';

import { ListInstancesResponse } from '../apiclient/argocd/v1/argocd_pb';
import { KubeVisionConfig } from '../apiclient/generated';
import { ListKubernetesEnabledClustersResponse } from '../apiclient/organization/v1/organization_pb';
import { EnabledClustersInfo } from '../apiclient/utils';
import { Loading } from '../components';
import { useHistory } from '../hooks/use-history';
import { usePermissionsChecker } from '../hooks/use-permissions';
import { useThemePreference } from '../hooks/use-theme-preference';
import { OrganizationRole } from '../types';

import { ModalContextProvider } from './modal-context';
import { NotificationContextProvider } from './notification-context';

interface AkuityIntelligenceContextType {
  // Common properties
  organizationMode: boolean;
  organizationId: string;
  organizationName?: string;
  instanceId: string;
  enabledClustersInfo: EnabledClustersInfo;
  isDarkTheme: boolean;
  setIsDarkTheme: (isDark: boolean) => void;
  currentRole?: OrganizationRole;
  permissionCheckerAkp?: ReturnType<typeof usePermissionsChecker>['permissionChecker'];

  // Platform flags
  isInAKP: boolean;
  isInArgoCD: boolean;
  isInKargo: boolean;

  // KubeVision specific
  kubeVisionConfig?: KubeVisionConfig;

  // AI Support Engineer specific
  showAlert?: { alertIcon: IconDefinition };
  defaultContexts?: ExtendedAIMessageContext[];
  isKubeVisionEnabled?: boolean;
  modelVersion?: string;
}

const AkuityIntelligenceContext = createContext<AkuityIntelligenceContextType | undefined>(
  undefined
);

const refetchConfig = {
  refetchInterval: 60000,
  refetchOnMount: true,
  refetchOnWindowFocus: true,
  refetchOnReconnect: true,
  keepPreviousData: true
};

export const useAkuityIntelligenceContext = () => {
  const ctx = useContext(AkuityIntelligenceContext);

  if (ctx === undefined) {
    throw new Error(`useAkuityIntelligenceContext must be used within a Provider`);
  }

  return ctx;
};

type Props = {
  children: React.ReactNode;
  organizationMode?: boolean;
  organizationId: string;
  organizationName?: string;
  instanceId: string;
  kubeVisionConfig?: KubeVisionConfig;
  includeDisabled?: boolean;
  currentRole?: OrganizationRole;
  disableLoadingAnimation?: boolean;

  // AI Support Engineer specific props
  showAlert?: { alertIcon: IconDefinition };
  defaultContexts?: ExtendedAIMessageContext[];
  isKubeVisionExtensionEnabled?: boolean;
  modelVersion?: string;
};

export const AkuityIntelligenceContextProvider = ({
  children,
  disableLoadingAnimation,
  ...props
}: Props) => {
  const { platform } = usePlatformContext();
  const apiFetch = usePlatformApiFetch();
  const isInAKP = platform === 'akuity-platform';
  const isInArgoCD = platform === 'argocd';
  const isInKargo = platform === 'kargo';

  const { permissionChecker } = usePermissionsChecker({
    organizationId: props.organizationId,
    enabled: isInAKP
  });
  // 1. only AKP will check currentRole, we don't need it for Argo CD Extension and Kargo Extension
  // 2. isKubeVisionExtensionEnabled is needed for Argo CD Extension and Kargo Extension, for AKP, this value will return undefined, so undefined should be excluded here, otherwise AKP will always get false value

  // Permission checks for clusters query
  const clustersQueryEnabled =
    !!props.organizationId &&
    ((isInAKP &&
      permissionChecker.can({
        action: 'get',
        object: 'organization/kubernetes-dashboard',
        resource: '*'
      })) ||
      ((isInArgoCD || isInKargo) &&
        !props.currentRole &&
        props.isKubeVisionExtensionEnabled !== false) ||
      (!isInAKP && !props.currentRole));

  // Query for ArgoCD instances (KubeVision specific)
  const { data: instances } = useQuery({
    queryKey: ['argocdInstances', props.organizationId],
    queryFn: () =>
      apiFetch(`orgs/${props.organizationId}/argocd/instances`)
        .then(ListInstancesResponse.fromJson)
        .then((res) => res.instances),
    enabled: !!props.organizationId && props.organizationMode,
    staleTime: 60000,
    ...refetchConfig
  });

  const instanceInQueryCluster = isInKargo ? '' : props.instanceId;

  // Query for enabled clusters (shared between both contexts)
  const { data: enabledClusters, isLoading: isEnabledClusterLoading } = useQuery({
    queryKey: ['enabledClusters', props.organizationId, instanceInQueryCluster],
    queryFn: () => {
      return apiFetch(
        `orgs/${props.organizationId}/k8s/clusters?instanceId=${instanceInQueryCluster}`
      )
        .then(ListKubernetesEnabledClustersResponse.fromJson)
        .then((res) => res.clusters.filter((c) => props.includeDisabled || c.isEnabled));
    },
    enabled: clustersQueryEnabled,
    staleTime: 60000,
    ...refetchConfig
  });

  const enabledClustersInfo = new EnabledClustersInfo(enabledClusters || []);

  // Instance resolution logic (from KubeVision context)
  const url = useHistory();
  const instanceName = new URLSearchParams(url).get('instance');
  const instanceId = enabledClustersInfo.instanceId(instanceName) || props.instanceId;

  const kubeVisionConfig =
    instances?.find((instance) => instance.id === instanceId)?.spec?.kubeVisionConfig ||
    props.kubeVisionConfig;

  // For dark theme in Argo CD Extension
  const { isDarkTheme, setIsDarkTheme } = useThemePreference();

  // Check if KubeVision(Intelligence) is enabled (from AI Support Engineer context)
  const isKubeVisionEnabled = useMemo(() => {
    const enabledClusters = enabledClustersInfo.clusterIds({ instanceId });
    switch (platform) {
      case 'akuity-platform':
        return enabledClusters.length > 0;
      case 'argocd':
        return props.isKubeVisionExtensionEnabled && enabledClusters.length > 0;
      case 'kargo':
        return true;
    }
  }, [platform, enabledClustersInfo, instanceId, props.isKubeVisionExtensionEnabled]);

  // Process default contexts (from AI Support Engineer context)
  const processedDefaultContexts = useMemo(() => {
    if (!props.defaultContexts || !enabledClustersInfo) {
      return [];
    }
    return props.defaultContexts.map((context) => {
      if (!context.k8sNamespace?.clusterName || !context.k8sNamespace?.instanceName) {
        return context;
      }
      const { clusterName, instanceName, ...k8sNamespaceRest } = context.k8sNamespace;

      return {
        ...context,
        k8sNamespace: {
          ...k8sNamespaceRest,
          instanceId: enabledClustersInfo.instanceId(instanceName),
          clusterId: enabledClustersInfo.clusterId({ clusterName, instanceName })
        }
      };
    });
  }, [props.defaultContexts, enabledClustersInfo]);

  const contextValue: AkuityIntelligenceContextType = {
    organizationMode: props.organizationMode,
    organizationId: props.organizationId,
    organizationName: props.organizationName,
    instanceId,
    enabledClustersInfo,
    isDarkTheme,
    setIsDarkTheme,
    currentRole: props.currentRole,
    permissionCheckerAkp: permissionChecker,
    isInAKP,
    isInArgoCD,
    isInKargo,
    kubeVisionConfig,
    showAlert: props.showAlert,
    defaultContexts: processedDefaultContexts,
    isKubeVisionEnabled,
    modelVersion: props.modelVersion
  };

  return (
    <AkuityIntelligenceContext.Provider value={contextValue}>
      <ConfigProvider
        theme={{ algorithm: isDarkTheme ? theme.darkAlgorithm : theme.defaultAlgorithm }}
      >
        <NotificationContextProvider>
          <ModalContextProvider>
            {isEnabledClusterLoading && !disableLoadingAnimation ? (
              <Flex
                justify={'center'}
                className='w-full mt-20'
                style={{ color: akpTextPrimaryColor }}
              >
                <Loading />
              </Flex>
            ) : (
              children
            )}
          </ModalContextProvider>
        </NotificationContextProvider>
      </ConfigProvider>
    </AkuityIntelligenceContext.Provider>
  );
};
