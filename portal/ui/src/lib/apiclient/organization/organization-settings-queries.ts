import { PlainMessage, proto3 } from '@bufbuild/protobuf';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { useMainContext } from '@ui/lib/context/main-context';
import { useNotificationContext } from '@ui/lib/context/notification-context';
import { UseQueryOptionsOverride } from '@ui/lib/types';

import { queryKeys } from '../query-keys';
import { ApiError, useApiFetch } from '../use-api-fetch';

import {
  CreateNotificationConfigRequest,
  CreateNotificationConfigResponse,
  DeleteNotificationConfigRequest,
  DeliveryMethod,
  GetNotificationConfigRequest,
  GetNotificationConfigResponse,
  GetNotificationDeliveryHistoryDetailRequest,
  GetNotificationDeliveryHistoryDetailResponse,
  ListNotificationConfigsRequest,
  ListNotificationConfigsResponse,
  ListNotificationDeliveryHistoryRequest,
  ListNotificationDeliveryHistoryResponse,
  NotificationConfig,
  PingNotificationConfigRequest,
  RedeliverNotificationRequest,
  UpdateNotificationConfigRequest,
  UpdateNotificationConfigResponse,
  VerifyOrganizationDomainsResponse
} from './v1/organization_pb';

export const useGetNotificationConfigsQuery = (
  data?: Pick<ListNotificationConfigsRequest, 'deliveryMethods'>,
  opts?: UseQueryOptionsOverride<
    ListNotificationConfigsResponse['notificationConfigs'],
    readonly string[]
  >
) => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  const deliveryMethods = data?.deliveryMethods?.map(
    (i) => proto3.getEnumType(DeliveryMethod).findNumber(i)?.name
  );
  const search = new URLSearchParams(deliveryMethods.map((s) => ['deliveryMethods', s])).toString();

  return useQuery({
    queryKey: queryKeys.organizations.notificationConfigs(currentOrg.id, search).queryKey,
    queryFn: () =>
      apiFetch(`v1/orgs/${currentOrg.id}/notification-configs?${search}`)
        .then(ListNotificationConfigsResponse.fromJson)
        .then((res) => res.notificationConfigs),
    staleTime: 500,
    ...opts
  });
};

export const useGetNotificationConfigQuery = ({
  id
}: Omit<PlainMessage<GetNotificationConfigRequest>, 'organizationId'>) => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.notificationConfig(currentOrg.id, id).queryKey,
    queryFn: () =>
      apiFetch(`v1/orgs/${currentOrg.id}/notification-configs/${id}`)
        .then(GetNotificationConfigResponse.fromJson)
        .then((res) => res.notificationConfig)
  });
};

export const useCreateNotificationConfigMutation = () => {
  const { currentOrg } = useMainContext();
  const queryClient = useQueryClient();
  const apiFetch = useApiFetch();

  return useMutation<
    NotificationConfig,
    ApiError,
    Omit<CreateNotificationConfigRequest, 'organizationId'>
  >({
    mutationFn: (data) =>
      apiFetch(`v1/orgs/${currentOrg.id}/notification-configs`, {
        method: 'POST',
        body: data.toJsonString()
      })
        .then(CreateNotificationConfigResponse.fromJson)
        .then((res) => res.notificationConfig),
    onSuccess: () =>
      queryClient.invalidateQueries({
        queryKey: queryKeys.organizations.notificationConfigs._def
      })
  });
};

export const useUpdateNotificationConfigMutation = () => {
  const { currentOrg } = useMainContext();
  const queryClient = useQueryClient();
  const apiFetch = useApiFetch();

  return useMutation<
    UpdateNotificationConfigResponse,
    ApiError,
    Omit<UpdateNotificationConfigRequest, 'organizationId'>
  >({
    mutationFn: (data) =>
      apiFetch(`v1/orgs/${currentOrg.id}/notification-configs/${data.id}`, {
        method: 'PUT',
        body: data.toJsonString()
      }),
    onSuccess: (data, { id }) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.organizations.notificationConfigs._def
      });

      queryClient.setQueryData(
        queryKeys.organizations.notificationConfig(currentOrg.id, id).queryKey,
        data.notificationConfig
      );
    }
  });
};

export const useDeleteNotificationConfigMutation = () => {
  const { currentOrg } = useMainContext();
  const queryClient = useQueryClient();
  const apiFetch = useApiFetch();

  return useMutation<
    unknown,
    ApiError,
    Omit<PlainMessage<DeleteNotificationConfigRequest>, 'organizationId'>
  >({
    mutationFn: ({ id }) =>
      apiFetch(`v1/orgs/${currentOrg.id}/notification-configs/${id}`, {
        method: 'DELETE'
      }),
    onSuccess: () =>
      queryClient.invalidateQueries({
        queryKey: queryKeys.organizations.notificationConfigs._def
      })
  });
};

export const useListNotificationDeliveryHistory = (
  {
    id,
    limit = BigInt(10),
    offset = BigInt(0)
  }: Omit<PlainMessage<ListNotificationDeliveryHistoryRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<ListNotificationDeliveryHistoryResponse, readonly string[]>
) => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  const search = new URLSearchParams({
    limit: String(limit),
    offset: String(offset)
  });

  return useQuery({
    queryKey: queryKeys.organizations.notificationDeliveryHistory(currentOrg.id, id, limit, offset)
      .queryKey,
    queryFn: () =>
      apiFetch(
        `v1/orgs/${currentOrg.id}/notification-configs/${id}/delivery-history?${search}`
      ).then(ListNotificationDeliveryHistoryResponse.fromJson),
    ...opts
  });
};

export const useGetNotificationDeliveryHistoryDetails = (
  {
    id,
    configId
  }: Omit<PlainMessage<GetNotificationDeliveryHistoryDetailRequest>, 'organizationId'>,
  opts?: UseQueryOptionsOverride<
    GetNotificationDeliveryHistoryDetailResponse['detail'],
    readonly string[]
  >
) => {
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.notificationDeliveryHistoryDetails(
      currentOrg.id,
      configId,
      id
    ).queryKey,
    queryFn: () =>
      apiFetch(`v1/orgs/${currentOrg.id}/notification-configs/${configId}/delivery-history/${id}`)
        .then(GetNotificationDeliveryHistoryDetailResponse.fromJson)
        .then((res) => res.detail),
    ...opts
  });
};

export const usePingNotificationConfig = () => {
  const { currentOrg } = useMainContext();
  const queryClient = useQueryClient();
  const apiFetch = useApiFetch();

  return useMutation<
    unknown,
    ApiError,
    Omit<PlainMessage<PingNotificationConfigRequest>, 'organizationId'>
  >({
    mutationFn: ({ id }) =>
      apiFetch(`v1/orgs/${currentOrg.id}/notification-configs/${id}/ping`, {
        method: 'POST'
      }),
    onSuccess: () =>
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: queryKeys.organizations.notificationConfigs._def
        });
        queryClient.invalidateQueries({
          queryKey: queryKeys.organizations.notificationDeliveryHistory._def
        });
      }, 1000)
  });
};

export const useRedeliverNotification = () => {
  const { currentOrg } = useMainContext();
  const queryClient = useQueryClient();
  const apiFetch = useApiFetch();

  return useMutation<
    unknown,
    ApiError,
    Omit<PlainMessage<RedeliverNotificationRequest>, 'organizationId'>
  >({
    mutationFn: ({ id, configId }) =>
      apiFetch(
        `v1/orgs/${currentOrg.id}/notification-configs/${configId}/notifications/${id}/redeliver`,
        {
          method: 'POST'
        }
      ),
    onSuccess: () =>
      setTimeout(
        () =>
          queryClient.invalidateQueries({
            queryKey: queryKeys.organizations.notificationDeliveryHistory._def
          }),
        1000
      )
  });
};

export const useGetDomainList = ({ id }: { id: string }) => {
  const apiFetch = useApiFetch();

  return useQuery({
    queryKey: queryKeys.organizations.domain(id).queryKey,
    queryFn: () =>
      apiFetch(`v1/organizations/${id}/domains`)
        .then(VerifyOrganizationDomainsResponse.fromJson)
        .then((res) => res.domains),
    initialData: []
  });
};

export const useUpdateDomainList = () => {
  const queryClient = useQueryClient();
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();
  const notification = useNotificationContext();

  return useMutation<
    VerifyOrganizationDomainsResponse,
    ApiError,
    { id: string; domains: string[] }
  >({
    mutationFn: ({ id, domains }) =>
      apiFetch(`v1/organizations/${id}/domains`, {
        method: 'POST',
        body: JSON.stringify({ domains })
      }),
    onSuccess: (data) => {
      queryClient.setQueryData(
        queryKeys.organizations.domain(currentOrg.id).queryKey,
        data.domains
      );
      notification.success({ message: 'Domains Added Successfully' });
    }
  });
};

export const useDeleteDomain = () => {
  const queryClient = useQueryClient();
  const { currentOrg } = useMainContext();
  const apiFetch = useApiFetch();
  const notification = useNotificationContext();

  return useMutation<null, ApiError, { id: string; domain: string }>({
    mutationFn: ({ id, domain }) =>
      apiFetch(`v1/organizations/${id}/domains/${domain}`, {
        method: 'DELETE'
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.organizations.domain(currentOrg.id).queryKey
      });
      notification.success({ message: 'Domain Deleted Successfully' });
    }
  });
};
