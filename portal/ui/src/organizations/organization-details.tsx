import { faIdBadge } from '@fortawesome/free-regular-svg-icons/faIdBadge';
import {
  faChartSimple,
  faCog,
  faCreditCard,
  faIdCardClip,
  faKey,
  faListUl,
  faPaperPlane,
  faUsers,
  faUsersRectangle
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Alert, Button, Col, Row, Space, Tabs } from 'antd';
import { useState } from 'react';
import { Navigate, generatePath, useNavigate, useSearchParams } from 'react-router-dom';

import { paths } from '@ui/config/paths';
import { OrganizationMember } from '@ui/lib/apiclient/generated';
import {
  useGetFeatureStatus,
  useGetInvitees,
  useGetMembers,
  useGetOrganization,
  useListApiKeysQuery
} from '@ui/lib/apiclient/organization/organization-queries';
import { FeatureStatus } from '@ui/lib/apiclient/types/features/v1/features_pb';
import { refreshToken } from '@ui/lib/apiclient/utils';
import { PageTitle } from '@ui/lib/components/page-title';
import { PaidFeatureTag } from '@ui/lib/components/paid-feature-tag';
import { AkuityIntelligenceContextProvider } from '@ui/lib/context/akuity-intelligence-context';
import { useMainContext } from '@ui/lib/context/main-context';
import { useModal } from '@ui/lib/hooks';
import { useRequiredParams } from '@ui/lib/hooks/use-required-params';
import { OrganizationRole } from '@ui/lib/types';

import { PlatformContextProvider } from '../feature/shared/context/platform-context-provider';
import { Error, IconLabel, Loading, PageContent, RoleLabel } from '../lib/components';

import { ApiKeysTab } from './components/api-keys-tab';
import { AuditLog } from './components/audit-log';
import { auditArchiveFeatureFreshness } from './components/audit-log-archive/utils';
import { BillingContextProvider } from './components/billing-context-provider';
import { BillingDetailsTab } from './components/billing-details-tab';
import { ManageCustomRoles } from './components/custom-roles/manage-custom-roles';
import { InviteMemberModal } from './components/invite-member-modal';
import { KubeVisionUsageTab } from './components/kubevision-usage/kubevision-usage';
import { MembersTab } from './components/members-tab';
import { SSOConfiguration } from './components/sso-configuration/sso-configuration';
import { Teams } from './components/teams/teams';

export const OrganizationDetailsPage = () => {
  const [search, setSearch] = useSearchParams();
  const { id } = useRequiredParams<'id'>(['id']);
  const navigate = useNavigate();

  const ctx = useMainContext();
  const canGetApiKeys = ctx.permissionChecker.can({
    action: 'get',
    object: 'organization/apikeys',
    resource: '*'
  });

  const {
    data: organization,
    isLoading: isOrgLoading,
    refetch: refetchOrg
  } = useGetOrganization({ id });

  const {
    data: apiKeys,
    isLoading: isApiKeysLoading,
    refetch: refetchApiKeys
  } = useListApiKeysQuery({ id }, { enabled: !!organization && canGetApiKeys });

  const {
    data: members,
    isLoading: isMembersLoading,
    refetch: refetchMembers
  } = useGetMembers({ id }, { enabled: !!organization });

  const {
    data: invitees,
    isLoading: isInviteesLoading,
    refetch: refetchInvitees
  } = useGetInvitees({ id }, { enabled: !!organization });

  const {
    data: featureStatuses,
    isLoading: isFeatureStatusesLoading,
    error: featureStatusesError
  } = useGetFeatureStatus({ id }, { enabled: !!organization });

  const isLoading =
    isOrgLoading ||
    isMembersLoading ||
    isInviteesLoading ||
    isApiKeysLoading ||
    isFeatureStatusesLoading;

  const error = featureStatusesError;

  const hasPermissionToChangeRoleOfOther = ctx.permissionChecker.can({
    action: 'update',
    object: 'organization/member-role',
    resource: '*'
  });

  const hasPermissionToRemove = (targetMember: OrganizationMember) => {
    // Can't remove yourself
    if (targetMember.email === ctx.user.email) {
      return false;
    }

    return ctx.currentRole === OrganizationRole.Owner;
  };

  const [emailsSent, setEmailsSent] = useState<string>('');

  const { show } = useModal((p) =>
    organization ? (
      <InviteMemberModal
        {...p}
        onSend={(emails) => {
          setEmailsSent(emails.join(', '));
          refetchInvitees();
        }}
        organizationId={organization?.id}
      />
    ) : null
  );

  if (!organization && !isOrgLoading) {
    return <Navigate to={paths.organizations} />;
  }

  return (
    <PageContent
      breadcrumbs={[
        { label: 'Organizations', path: '/organizations' },
        { label: organization?.name || 'Not found', path: '', loading: isLoading }
      ]}
    >
      {isLoading ? (
        <Loading />
      ) : error ? (
        <Error err={error} />
      ) : (
        <>
          <Row justify='space-between' align='middle'>
            <div />
            <Col className='flex gap-8 items-end mb-4'>
              <div className='flex'>
                My Role:{' '}
                <b className='ml-2'>
                  <RoleLabel role={ctx.currentRole as OrganizationRole} />
                </b>
              </div>
              <div className='text-akuity-500'>
                Org ID: <span className='font-medium'>{organization.id}</span>
              </div>
            </Col>
          </Row>
          <Tabs
            destroyOnHidden
            className='-mt-4'
            activeKey={search.get('tab') || 'members'}
            onChange={(tab) => {
              setSearch({ tab });

              if (tab === 'archive') {
                auditArchiveFeatureFreshness.seen();
              }
            }}
            tabBarExtraContent={
              <Button
                icon={<FontAwesomeIcon icon={faCog} />}
                type='text'
                onClick={() => navigate(generatePath(paths.organizationSettings, { id }))}
                size='small'
              >
                Settings
              </Button>
            }
            items={[
              {
                key: 'members',
                label: <IconLabel icon={faUsers} label='Members' />,
                children: (
                  <>
                    <PageTitle>Organization Members</PageTitle>
                    {emailsSent ? (
                      <Alert
                        className='mb-4'
                        type='info'
                        message={`Sent invite to ${emailsSent}`}
                        banner
                      />
                    ) : null}
                    <MembersTab
                      role={ctx.currentRole}
                      members={members}
                      invitees={invitees}
                      organization={organization}
                      hasPermissionToRemove={hasPermissionToRemove}
                      hasPermissionToUninvite={() =>
                        ctx.user.organizations?.find(
                          (membership) =>
                            membership.id === organization.id &&
                            membership.role !== OrganizationRole.Member
                        ) != null
                      }
                      onChange={() => {
                        refetchMembers();
                        refetchInvitees();
                      }}
                      setSearch={setSearch}
                      hasPermissionToChangeRoleOfOtherMembers={hasPermissionToChangeRoleOfOther}
                    />

                    {OrganizationRole.Owner === ctx.currentRole && (
                      <Row className='mt-4'>
                        <Col>
                          <Button type='primary' onClick={() => show()} className='ml-2'>
                            <FontAwesomeIcon icon={faPaperPlane} className='mr-2' /> Invite member
                          </Button>
                        </Col>
                      </Row>
                    )}
                  </>
                )
              },
              featureStatuses.team !== FeatureStatus.NOT_AVAILABLE && {
                key: 'teams',
                disabled: featureStatuses.team === FeatureStatus.DISABLED,
                label: <IconLabel icon={faUsersRectangle} label='Teams' />,
                children: (
                  <>
                    <PageTitle>Organization Teams</PageTitle>
                    <Teams />
                  </>
                )
              },
              ctx.permissionChecker.can({
                action: 'get',
                object: 'organization/custom-role',
                resource: '*'
              }) && {
                key: 'custom-roles',
                disabled: ctx.currentOrgFeatureStatuses.customRoles === FeatureStatus.DISABLED,
                label: (
                  <Space>
                    <IconLabel icon={faIdCardClip} label='Custom Roles' />
                    {ctx.currentOrgFeatureStatuses.customRoles === FeatureStatus.DISABLED && (
                      <PaidFeatureTag />
                    )}
                  </Space>
                ),
                children: (
                  <>
                    <PageTitle>Organization Custom Roles</PageTitle>
                    <ManageCustomRoles />
                  </>
                )
              },
              canGetApiKeys && {
                key: 'api-keys',
                label: <IconLabel icon={faKey} label='API Keys' />,
                children: (
                  <>
                    <PageTitle>Organization API Keys</PageTitle>
                    <ApiKeysTab
                      apiKeys={apiKeys}
                      organizationId={organization.id}
                      refetchApiKeys={refetchApiKeys}
                      role={ctx.currentRole}
                    />
                  </>
                )
              },
              ctx.permissionChecker.can({
                action: 'get',
                object: 'organization/audit-log',
                resource: '*'
              }) && {
                key: 'audit',
                label: <IconLabel icon={faListUl} label='Audit' />,
                children: (
                  <>
                    <PageTitle>Organization Audit</PageTitle>
                    <AuditLog members={members} organizationId={id} />
                  </>
                )
              },
              featureStatuses.sso !== FeatureStatus.NOT_AVAILABLE &&
                ctx.permissionChecker.can({
                  action: 'get',
                  object: 'organization/sso-configuration',
                  resource: '*'
                }) && {
                  key: 'sso',
                  disabled: featureStatuses.sso === FeatureStatus.DISABLED,
                  label: (
                    <Space>
                      <IconLabel icon={faIdBadge} label='SSO' />
                      {featureStatuses.sso === FeatureStatus.DISABLED && (
                        <PaidFeatureTag label='Enterprise Feature' />
                      )}
                    </Space>
                  ),
                  children: (
                    <>
                      <PageTitle>Organization SSO</PageTitle>
                      {featureStatuses?.sso && <SSOConfiguration organization={organization} />}
                    </>
                  )
                },
              !ctx.systemSettings.selfHosted &&
                ctx.currentRole === OrganizationRole.Owner && {
                  key: 'intelligence-usage',
                  label: (
                    <Space>
                      <IconLabel icon={faChartSimple} label='Intelligence Usage' />
                    </Space>
                  ),
                  children: (
                    <>
                      <PageTitle>Intelligence Usage</PageTitle>
                      <PlatformContextProvider
                        platform='akuity-platform'
                        refreshToken={() => refreshToken()}
                      >
                        <AkuityIntelligenceContextProvider
                          organizationMode={true}
                          kubeVisionConfig={null}
                          instanceId={''}
                          organizationId={organization.id}
                          currentRole={ctx.currentRole}
                        >
                          <KubeVisionUsageTab organizationId={organization.id} />
                        </AkuityIntelligenceContextProvider>
                      </PlatformContextProvider>
                    </>
                  )
                },
              !ctx.systemSettings.selfHosted &&
                ctx.systemSettings.billingEnabled &&
                ctx.permissionChecker.can({
                  action: 'get',
                  object: 'organization/billing',
                  resource: '*'
                }) && {
                  key: 'billing',
                  label: <IconLabel icon={faCreditCard} label='Billing' />,
                  children: (
                    <>
                      <PageTitle>Organization Billing</PageTitle>
                      <BillingContextProvider>
                        <BillingDetailsTab
                          organization={organization}
                          refetchOrganization={refetchOrg}
                          members={members}
                          hasAuthorityToPurchasePlan={
                            ctx.permissionChecker.can({
                              action: 'create',
                              object: 'organization/billing',
                              resource: '*'
                            }) &&
                            ctx.permissionChecker.can({
                              action: 'update',
                              object: 'organization/billing',
                              resource: '*'
                            }) &&
                            ctx.permissionChecker.can({
                              action: 'delete',
                              object: 'organization/billing',
                              resource: '*'
                            })
                          }
                        />
                      </BillingContextProvider>
                    </>
                  )
                }
            ].filter(Boolean)}
          />
        </>
      )}
    </PageContent>
  );
};
